Name: hotel-api
Host: 0.0.0.0
Port: 8081
MaxConns: 100
DevServer:
  Enabled: true
  Port: 9091
  HealthPath: "/ping"
JwtAuth:
  secret: "your-secret-key-至少32位" # 生产环境建议使用环境变量
  expiresIn: "604800s"                    # 7天（单位：秒）
  signingMethod: "HS256"# 签名算法
  issuer: "<EMAIL>" # 签发者
  audience: "demo" # 接收方
Timeout: 30000
MySQL:
  API: root:123456@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=UTC
Redis:
  Host: "localhost:6379"
  Type: "node"
  Pass: ""
  DB: 0
