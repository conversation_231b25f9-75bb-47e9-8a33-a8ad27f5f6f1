# API 服务配置模板
# 复制此文件为 config.yaml 或 config.dev.yaml 并填入实际值

Name: hotel-api
Host: ${API_HOST:-0.0.0.0}
Port: ${API_PORT:-8080}
MaxConns: ${API_MAX_CONNS:-100}

DevServer:
  Enabled: ${DEV_SERVER_ENABLED:-true}
  Port: ${DEV_SERVER_PORT:-9091}
  HealthPath: "${DEV_SERVER_HEALTH_PATH:-/ping}"

JwtAuth:
  secret: "${JWT_SECRET}" # 必须设置，至少32位
  expiresIn: "${JWT_EXPIRES_IN:-604800s}" # 7天（单位：秒）
  signingMethod: "${JWT_SIGNING_METHOD:-HS256}" # 签名算法
  issuer: "${JWT_ISSUER}" # 签发者，必须设置
  audience: "${JWT_AUDIENCE:-hotel-api}" # 接收方

MySQL:
  API: "${MYSQL_USER}:${MYSQL_PASSWORD}@tcp(${MYSQL_HOST}:${MYSQL_PORT})/${MYSQL_DATABASE}?charset=${MYSQL_CHARSET:-utf8mb4}&parseTime=${MYSQL_PARSE_TIME:-True}&loc=${MYSQL_LOC:-UTC}&timeout=${MYSQL_TIMEOUT:-60s}&writeTimeout=${MYSQL_WRITE_TIMEOUT:-60s}&readTimeout=${MYSQL_READ_TIMEOUT:-60s}"

Timeout: ${API_TIMEOUT:-30000}

# 安全注意事项：
# 1. 请确保所有环境变量都已正确设置
# 2. JWT_SECRET 必须是强密码，至少32位
# 3. 数据库密码应该是复杂密码
# 4. 生产环境中请使用 HTTPS