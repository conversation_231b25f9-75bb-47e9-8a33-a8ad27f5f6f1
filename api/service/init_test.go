package service

import (
	"context"
	"hotel/api/config"
	"hotel/api/protocol"
	"hotel/common/pagehelper"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var (
	qs *QuestionnaireService
)

func TestMain(m *testing.M) {
	c := config.Init()
	qs = NewQuestionnaireService(c)
	m.Run()
}

// TestQuestionnaireService_SubmitQuestionnaire 测试提交问卷
func TestQuestionnaireService_SubmitQuestionnaire(t *testing.T) {
	ctx := context.Background()

	t.Run("成功提交问卷", func(t *testing.T) {
		req := &protocol.SubmitQuestionnaireRequest{
			QuestionnaireName: "测试问卷",
			SubmitterEmail:    "<EMAIL>",
			Answers: map[string]interface{}{
				"question1": "答案1",
				"question2": "答案2",
				"rating":    5,
			},
		}

		resp, err := qs.SubmitQuestionnaire(ctx, req)
		if err != nil && err.Error() == "问卷提交失败，请稍后重试" {
			t.<PERSON><PERSON>("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.Equal(t, "问卷提交成功", resp.Message)
		assert.NotEmpty(t, resp.RequestId)
	})

	t.Run("问卷名称为空", func(t *testing.T) {
		req := &protocol.SubmitQuestionnaireRequest{
			QuestionnaireName: "",
			SubmitterEmail:    "<EMAIL>",
			Answers: map[string]interface{}{
				"question1": "答案1",
			},
		}

		resp, err := qs.SubmitQuestionnaire(ctx, req)
		require.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "问卷名称不能为空")
	})

	t.Run("用户邮箱为空", func(t *testing.T) {
		req := &protocol.SubmitQuestionnaireRequest{
			QuestionnaireName: "测试问卷",
			SubmitterEmail:    "",
			Answers: map[string]interface{}{
				"question1": "答案1",
			},
		}

		resp, err := qs.SubmitQuestionnaire(ctx, req)
		require.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "用户邮箱无效")
	})

	t.Run("问卷回答为空", func(t *testing.T) {
		req := &protocol.SubmitQuestionnaireRequest{
			QuestionnaireName: "测试问卷",
			SubmitterEmail:    "<EMAIL>",
			Answers:           map[string]interface{}{},
		}

		resp, err := qs.SubmitQuestionnaire(ctx, req)
		require.Error(t, err)
		assert.Nil(t, resp)
		assert.Contains(t, err.Error(), "问卷回答内容不能为空")
	})
}

// TestQuestionnaireService_GetQuestionnaireStats 测试获取问卷统计信息
func TestQuestionnaireService_GetQuestionnaireStats(t *testing.T) {
	ctx := context.Background()

	t.Run("获取问卷统计信息", func(t *testing.T) {
		req := &protocol.GetQuestionnaireStatsRequest{}

		resp, err := qs.GetQuestionnaireStats(ctx, req)
		if err != nil && err.Error() == "Error 1146 (42S02): Table 'hoteldev.questionnaire' doesn't exist" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, resp)
		// 统计数量应该大于等于基础值71
		assert.GreaterOrEqual(t, resp.TotalSubmissions, int64(71))
	})
}

// TestQuestionnaireService_ListQuestionnaires 测试分页查询问卷列表
func TestQuestionnaireService_ListQuestionnaires(t *testing.T) {
	ctx := context.Background()

	t.Run("默认分页查询", func(t *testing.T) {
		req := &protocol.ListQuestionnairesRequest{
			PageReq: pagehelper.PageReq{
				PageNum:  1,
				PageSize: 10,
			},
		}

		resp, err := qs.ListQuestionnaires(ctx, req)
		if err != nil && err.Error() == "Error 1146 (42S02): Table 'hoteldev.questionnaire' doesn't exist" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, int64(0))
		assert.NotNil(t, resp.List)
	})

	t.Run("使用默认分页参数", func(t *testing.T) {
		req := &protocol.ListQuestionnairesRequest{
			PageReq: pagehelper.PageReq{
				PageNum:  0, // 无效页码，应该使用默认值1
				PageSize: 0, // 无效页大小，应该使用默认值20
			},
		}

		resp, err := qs.ListQuestionnaires(ctx, req)
		if err != nil && err.Error() == "Error 1146 (42S02): Table 'hoteldev.questionnaire' doesn't exist" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, int64(0))
		assert.NotNil(t, resp.List)
	})

	t.Run("限制最大页大小", func(t *testing.T) {
		req := &protocol.ListQuestionnairesRequest{
			PageReq: pagehelper.PageReq{
				PageNum:  1,
				PageSize: 200, // 超过最大限制100，应该被限制为100
			},
		}

		resp, err := qs.ListQuestionnaires(ctx, req)
		if err != nil && err.Error() == "Error 1146 (42S02): Table 'hoteldev.questionnaire' doesn't exist" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, int64(0))
		assert.NotNil(t, resp.List)
	})

	t.Run("按日期范围查询", func(t *testing.T) {
		req := &protocol.ListQuestionnairesRequest{
			PageReq: pagehelper.PageReq{
				PageNum:  1,
				PageSize: 10,
			},
			StartDate: "2024-01-01",
			EndDate:   "2024-12-31",
		}

		resp, err := qs.ListQuestionnaires(ctx, req)
		if err != nil && err.Error() == "Error 1146 (42S02): Table 'hoteldev.questionnaire' doesn't exist" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, resp)
		assert.GreaterOrEqual(t, resp.Total, int64(0))
		assert.NotNil(t, resp.List)
	})
}

// TestQuestionnaireService_Integration 集成测试：提交问卷后查询
func TestQuestionnaireService_Integration(t *testing.T) {
	ctx := context.Background()

	t.Run("提交问卷后查询统计和列表", func(t *testing.T) {
		// 1. 先获取当前统计数据
		statsReq := &protocol.GetQuestionnaireStatsRequest{}
		initialStats, err := qs.GetQuestionnaireStats(ctx, statsReq)
		if err != nil && err.Error() == "Error 1146 (42S02): Table 'hoteldev.questionnaire' doesn't exist" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		initialCount := initialStats.TotalSubmissions

		// 2. 提交一个新问卷
		submitReq := &protocol.SubmitQuestionnaireRequest{
			QuestionnaireName: "集成测试问卷",
			SubmitterEmail:    "<EMAIL>",
			Answers: map[string]interface{}{
				"satisfaction": "非常满意",
				"rating":       5,
				"feedback":     "这是一个很好的服务",
				"timestamp":    time.Now().Unix(),
			},
		}

		submitResp, err := qs.SubmitQuestionnaire(ctx, submitReq)
		if err != nil && err.Error() == "问卷提交失败，请稍后重试" {
			t.Skip("数据库表不存在，跳过集成测试")
		}
		require.NoError(t, err)
		require.NotNil(t, submitResp)
		assert.Equal(t, "问卷提交成功", submitResp.Message)

		// 3. 再次获取统计数据，应该增加了1
		newStats, err := qs.GetQuestionnaireStats(ctx, statsReq)
		require.NoError(t, err)
		assert.Equal(t, initialCount+1, newStats.TotalSubmissions)

		// 4. 查询问卷列表，应该能找到刚提交的问卷
		listReq := &protocol.ListQuestionnairesRequest{
			PageReq: pagehelper.PageReq{
				PageNum:  1,
				PageSize: 50,
			},
		}

		listResp, err := qs.ListQuestionnaires(ctx, listReq)
		require.NoError(t, err)
		require.NotNil(t, listResp)

		// 验证列表中包含刚提交的问卷
		found := false
		for _, item := range listResp.List {
			if item.QuestionnaireName == "集成测试问卷" && item.SubmitterEmail == "<EMAIL>" {
				found = true
				assert.NotEmpty(t, item.RequestId)
				assert.Equal(t, 1, item.Status)
				assert.NotNil(t, item.Answers)
				assert.Equal(t, "非常满意", item.Answers["satisfaction"])
				assert.Equal(t, float64(5), item.Answers["rating"]) // JSON数字会被解析为float64
				break
			}
		}
		assert.True(t, found, "应该能在列表中找到刚提交的问卷")
	})
}

// TestQuestionnaireService_Name 测试服务名称
func TestQuestionnaireService_Name(t *testing.T) {
	name := qs.Name()
	assert.Equal(t, "questionnaire", name)
}
