package protocol

import (
	"hotel/common/pagehelper"
	"time"
)

// SubmitQuestionnaireRequest 提交问卷请求
type // SubmitQuestionnaireRequest represents the request structure for SubmitQuestionnaire operation with 3 fields
SubmitQuestionnaireRequest struct {
		// QuestionnaireName is the display name for this Questionnaire
QuestionnaireName string                 `json:"questionnaireName"` // 问卷名称
		// SubmitterEmail is the email address for this Submitter
SubmitterEmail    string                 `json:"submitterEmail"`    // 填写人ID
		// Answers contains the answers data
Answers           map[string]interface{} `json:"answers"`           // 问卷回答内容JSON
}

// SubmitQuestionnaireResponse 提交问卷响应
type // SubmitQuestionnaireResponse represents the response structure for SubmitQuestionnaire operation with 2 fields
SubmitQuestionnaireResponse struct {
		// Message contains the message for this 
Message   string `json:"message"`
		// RequestId is the unique identifier for this Request
RequestId string `json:"requestId"`
}

// GetQuestionnaireStatsRequest 获取问卷统计信息请求
type // GetQuestionnaireStatsRequest represents the request structure for GetQuestionnaireStats operation
GetQuestionnaireStatsRequest struct {
}

// GetQuestionnaireStatsResponse 获取问卷统计信息响应
type // GetQuestionnaireStatsResponse represents the response structure for GetQuestionnaireStats operation with 1 fields
GetQuestionnaireStatsResponse struct {
		// TotalSubmissions represents the total number of TotalSubmissions
TotalSubmissions int64 `json:"totalSubmissions"`
}

// ListQuestionnairesRequest 问卷列表查询请求
type // ListQuestionnairesRequest represents the request structure for ListQuestionnaires operation with 3 fields
ListQuestionnairesRequest struct {
	pagehelper.PageReq
		// StartDate represents the date for this Start
StartDate string `json:"startDate"` // 开始日期 YYYY-MM-DD
		// EndDate represents the date for this End
EndDate   string `json:"endDate"`   // 结束日期 YYYY-MM-DD
}

// ListQuestionnairesResponse 问卷列表查询响应
type // ListQuestionnairesResponse represents the response structure for ListQuestionnaires operation with 2 fields
ListQuestionnairesResponse struct {
		// Total represents the total number of 
Total int64                `json:"total"`
		// List contains a list of  items
List  []*QuestionnaireItem `json:"list"`
}

// QuestionnaireItem 问卷列表项
type // QuestionnaireItem represents a data structure for API communication with 7 fields
QuestionnaireItem struct {
		// Id is the unique identifier for this 
Id                int64                  `json:"id"`
		// QuestionnaireName is the display name for this Questionnaire
QuestionnaireName string                 `json:"questionnaireName"`
		// SubmitterEmail is the email address for this Submitter
SubmitterEmail    string                 `json:"submitterEmail"`
		// Answers contains the answers data
Answers           map[string]interface{} `json:"answers"`
		// RequestId is the unique identifier for this Request
RequestId         string                 `json:"requestId"`
		// Status represents the current status of this 
Status            int                    `json:"status"`
		// CreateTime represents the timestamp when this Create occurred
CreateTime        time.Time              `json:"createTime"`
}
