package cqrs

import (
	"context"
	"encoding/json"
	"fmt"
	redisConsumer "hotel/common/cqrs/consumer/redis"
	nsqProducer "hotel/common/cqrs/producer/nsq"
	redisProducer "hotel/common/cqrs/producer/redis"
	"hotel/common/cqrs/types"
)

// redisProducerAdapter 适配器，将 Redis Stream Producer 适配到 cqrs.Producer 接口
type redisProducerAdapter struct {
	producer *redisProducer.Producer
}

// serialize 序列化消息
func (a *redisProducerAdapter) serialize(message interface{}) ([]byte, error) {
	switch v := message.(type) {
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	default:
		return json.Marshal(v)
	}
}

func (a *redisProducerAdapter) Publish(ctx context.Context, topic string, message interface{}) error {
	data, err := a.serialize(message)
	if err != nil {
		return fmt.Errorf("failed to serialize message: %w", err)
	}
	return a.producer.Publish(ctx, topic, data)
}

func (a *redisProducerAdapter) PublishWithOptions(ctx context.Context, topic string, message interface{}, opts *types.PublishOptions) error {
	data, err := a.serialize(message)
	if err != nil {
		return fmt.Errorf("failed to serialize message: %w", err)
	}

	var redisOpts *redisProducer.PublishOptions
	if opts != nil {
		redisOpts = &redisProducer.PublishOptions{
			Headers: opts.Headers,
		}
	}
	return a.producer.PublishWithOptions(ctx, topic, data, redisOpts)
}

func (a *redisProducerAdapter) PublishRaw(ctx context.Context, topic string, message []byte) error {
	return a.producer.Publish(ctx, topic, message)
}

func (a *redisProducerAdapter) PublishRawWithOptions(ctx context.Context, topic string, message []byte, opts *types.PublishOptions) error {
	var redisOpts *redisProducer.PublishOptions
	if opts != nil {
		redisOpts = &redisProducer.PublishOptions{
			Headers: opts.Headers,
		}
	}
	return a.producer.PublishWithOptions(ctx, topic, message, redisOpts)
}

func (a *redisProducerAdapter) Close() error {
	return a.producer.Close()
}

// nsqProducerAdapter 适配器，将 NSQ Producer 适配到 cqrs.Producer 接口
type nsqProducerAdapter struct {
	producer *nsqProducer.Producer
}

// serialize 序列化消息
func (a *nsqProducerAdapter) serialize(message interface{}) ([]byte, error) {
	switch v := message.(type) {
	case []byte:
		return v, nil
	case string:
		return []byte(v), nil
	default:
		return json.Marshal(v)
	}
}

func (a *nsqProducerAdapter) Publish(ctx context.Context, topic string, message interface{}) error {
	data, err := a.serialize(message)
	if err != nil {
		return fmt.Errorf("failed to serialize message: %w", err)
	}
	return a.producer.Publish(ctx, topic, data)
}

func (a *nsqProducerAdapter) PublishWithOptions(ctx context.Context, topic string, message interface{}, opts *types.PublishOptions) error {
	data, err := a.serialize(message)
	if err != nil {
		return fmt.Errorf("failed to serialize message: %w", err)
	}

	var nsqOpts *nsqProducer.PublishOptions
	if opts != nil {
		nsqOpts = &nsqProducer.PublishOptions{
			Headers:  opts.Headers,
			Delay:    opts.Delay,
			Priority: opts.Priority,
		}
	}
	return a.producer.PublishWithOptions(ctx, topic, data, nsqOpts)
}

func (a *nsqProducerAdapter) PublishRaw(ctx context.Context, topic string, message []byte) error {
	return a.producer.Publish(ctx, topic, message)
}

func (a *nsqProducerAdapter) PublishRawWithOptions(ctx context.Context, topic string, message []byte, opts *types.PublishOptions) error {
	var nsqOpts *nsqProducer.PublishOptions
	if opts != nil {
		nsqOpts = &nsqProducer.PublishOptions{
			Headers:  opts.Headers,
			Delay:    opts.Delay,
			Priority: opts.Priority,
		}
	}
	return a.producer.PublishWithOptions(ctx, topic, message, nsqOpts)
}

func (a *nsqProducerAdapter) Close() error {
	return a.producer.Close()
}

// NewProducer 创建生产者
func NewProducer(config *types.Config) (types.Producer, error) {
	switch config.Type {
	case "redis_stream":
		producerConfig := &redisProducer.ProducerConfig{
			MaxLen: 10000, // 默认值
		}
		producer, err := redisProducer.NewProducer(config.Redis, producerConfig)
		if err != nil {
			return nil, err
		}
		return &redisProducerAdapter{producer: producer}, nil
	case "nsq":
		// TODO: 实现 NSQ 适配器
		return nil, fmt.Errorf("NSQ producer not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported message queue type: %s", config.Type)
	}
}

// redisConsumerAdapter 适配器，将 Redis Stream Consumer 适配到 cqrs.Consumer 接口
type redisConsumerAdapter struct {
	consumer *redisConsumer.Consumer
}

func (a *redisConsumerAdapter) Start() error {
	return a.consumer.Start()
}

func (a *redisConsumerAdapter) Stop() {
	a.consumer.Stop()
}

func (a *redisConsumerAdapter) IsRunning() bool {
	return a.consumer.IsRunning()
}

// NewConsumer 创建消费者
func NewConsumer(config *types.Config, consumerConfig *types.ConsumerConfig, handler types.MessageHandler) (types.Consumer, error) {
	if handler == nil {
		return nil, fmt.Errorf("handler cannot be nil")
	}

	switch config.Type {
	case "redis_stream":
		redisConsumerConfig := &redisConsumer.ConsumerConfig{
			TopicName:    consumerConfig.TopicName,
			ChannelName:  consumerConfig.ChannelName,
			ConsumerName: consumerConfig.ConsumerName,
		}
		consumer, err := redisConsumer.NewConsumer(config.Redis, redisConsumerConfig, handler)
		if err != nil {
			return nil, err
		}
		return &redisConsumerAdapter{consumer: consumer}, nil
	case "nsq":
		// TODO: 实现 NSQ Consumer
		return nil, fmt.Errorf("NSQ consumer not implemented yet")
	default:
		return nil, fmt.Errorf("unsupported message queue type: %s", config.Type)
	}
}

const (
	RedisStreamType = "redis_stream"
	NSQType         = "nsq"
)
