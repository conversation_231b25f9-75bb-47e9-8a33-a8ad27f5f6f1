package nsq

import (
	"context"
	"fmt"
	"time"

	"hotel/common/log"

	"github.com/nsqio/go-nsq"
)

// ProducerConfig NSQ生产者配置
type ProducerConfig struct {
	NSQDAddress      string   `yaml:"nsqd_address" json:"nsqd_address"`           // NSQ守护进程地址
	LookupdAddresses []string `yaml:"lookupd_addresses" json:"lookupd_addresses"` // NSQ查找服务地址列表
}

// PublishOptions NSQ发布选项
type PublishOptions struct {
	Delay    time.Duration     `json:"delay"`    // 延迟发送时间
	Headers  map[string]string `json:"headers"`  // 消息头（NSQ不直接支持，可以包含在消息体中）
	Priority int               `json:"priority"` // 消息优先级（NSQ不直接支持）
}

// Producer NSQ生产者实现
type Producer struct {
	producer *nsq.Producer
	config   *ProducerConfig
}

// NewProducer 创建NSQ生产者
func NewProducer(config *ProducerConfig) (*Producer, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if config.NSQDAddress == "" {
		return nil, fmt.Errorf("nsqd_address cannot be empty")
	}

	// 创建NSQ配置
	nsqConfig := nsq.NewConfig()

	// 创建NSQ生产者
	producer, err := nsq.NewProducer(config.NSQDAddress, nsqConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create NSQ producer: %w", err)
	}

	return &Producer{
		producer: producer,
		config:   config,
	}, nil
}

// Publish 发布消息到指定主题
func (p *Producer) Publish(ctx context.Context, topic string, message []byte) error {
	return p.PublishWithOptions(ctx, topic, message, nil)
}

// PublishWithOptions 带选项发布消息
func (p *Producer) PublishWithOptions(ctx context.Context, topic string, message []byte, opts *PublishOptions) error {
	if topic == "" {
		return fmt.Errorf("topic name cannot be empty")
	}
	if message == nil {
		return fmt.Errorf("message cannot be nil")
	}

	// 如果有延迟选项，使用延迟发布
	if opts != nil && opts.Delay > 0 {
		err := p.producer.DeferredPublish(topic, opts.Delay, message)
		if err != nil {
			log.Errorc(ctx, "Failed to publish deferred message to topic %s: %v", topic, err)
			return fmt.Errorf("failed to publish deferred message: %w", err)
		}
		log.Infoc(ctx, "Deferred message published to topic %s with delay %v", topic, opts.Delay)
		return nil
	}

	// 普通发布
	err := p.producer.Publish(topic, message)
	if err != nil {
		log.Errorc(ctx, "Failed to publish message to topic %s: %v", topic, err)
		return fmt.Errorf("failed to publish message: %w", err)
	}

	log.Infoc(ctx, "Message published to topic %s", topic)
	return nil
}

// PublishMulti 批量发布消息到指定主题
func (p *Producer) PublishMulti(ctx context.Context, topic string, messages [][]byte) error {
	if topic == "" {
		return fmt.Errorf("topic name cannot be empty")
	}
	if len(messages) == 0 {
		return fmt.Errorf("messages cannot be empty")
	}

	err := p.producer.MultiPublish(topic, messages)
	if err != nil {
		log.Errorc(ctx, "Failed to publish multiple messages to topic %s: %v", topic, err)
		return fmt.Errorf("failed to publish multiple messages: %w", err)
	}

	log.Infoc(ctx, "Published %d messages to topic %s", len(messages), topic)
	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	if p.producer != nil {
		p.producer.Stop()
		log.Info("NSQ producer closed")
	}
	return nil
}

// Ping 检查NSQ连接状态
func (p *Producer) Ping() error {
	return p.producer.Ping()
}
