package nsq

import (
	"context"
	"fmt"
	"sync"
	"time"

	"hotel/common/cqrs/types"
	"hotel/common/log"

	"github.com/nsqio/go-nsq"
)

// NSQConfig NSQ配置
type NSQConfig struct {
	NSQDAddress      string        `yaml:"nsqd_address" json:"nsqd_address"`
	LookupdAddresses []string      `yaml:"lookupd_addresses" json:"lookupd_addresses"`
	MaxInFlight      int           `yaml:"max_in_flight" json:"max_in_flight"`
	MaxAttempts      uint16        `yaml:"max_attempts" json:"max_attempts"`
	RequeueDelay     time.Duration `yaml:"requeue_delay" json:"requeue_delay"`
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	TopicName    string `yaml:"topic_name" json:"topic_name"`
	ChannelName  string `yaml:"channel_name" json:"channel_name"`
	ConsumerName string `yaml:"consumer_name" json:"consumer_name"`
}

// Consumer NSQ消费者实现
type Consumer struct {
	consumer  *nsq.Consumer
	config    *ConsumerConfig
	nsqConfig *NSQConfig
	handler   types.MessageHandler
	running   bool
	mu        sync.RWMutex
}

// NewConsumer 创建NSQ消费者
func NewConsumer(nsqConfig *NSQConfig, consumerConfig *ConsumerConfig, handler types.MessageHandler) (*Consumer, error) {
	if nsqConfig == nil {
		return nil, fmt.Errorf("nsq config cannot be nil")
	}
	if consumerConfig == nil {
		return nil, fmt.Errorf("consumer config cannot be nil")
	}
	if consumerConfig.TopicName == "" {
		return nil, fmt.Errorf("topic name cannot be empty")
	}
	if consumerConfig.ChannelName == "" {
		return nil, fmt.Errorf("channel name cannot be empty")
	}
	if handler == nil {
		return nil, fmt.Errorf("handler cannot be nil")
	}

	// 创建NSQ配置
	config := nsq.NewConfig()
	config.UserAgent = "hotel-be-nsq-consumer"

	// 设置配置参数
	if nsqConfig.MaxInFlight > 0 {
		config.MaxInFlight = nsqConfig.MaxInFlight
	}
	if nsqConfig.MaxAttempts > 0 {
		config.MaxAttempts = nsqConfig.MaxAttempts
	}
	if nsqConfig.RequeueDelay > 0 {
		config.DefaultRequeueDelay = nsqConfig.RequeueDelay
	}

	// 创建NSQ消费者
	consumer, err := nsq.NewConsumer(consumerConfig.TopicName, consumerConfig.ChannelName, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create NSQ consumer: %w", err)
	}

	c := &Consumer{
		consumer:  consumer,
		config:    consumerConfig,
		nsqConfig: nsqConfig,
		handler:   handler,
		running:   false,
	}

	// 设置消息处理器
	consumer.AddHandler(c)

	return c, nil
}

// HandleMessage 实现nsq.Handler接口
func (c *Consumer) HandleMessage(message *nsq.Message) error {
	ctx := context.Background()

	// 调用用户定义的处理器
	err := c.handler(ctx, message.Body)
	if err != nil {
		log.Errorc(ctx, "Error handling NSQ message: %v", err)
		// 重新排队消息以便重试
		message.Requeue(c.nsqConfig.RequeueDelay)
		return nil
	}

	// 成功处理，完成消息
	message.Finish()
	return nil
}

// Start 开始消费消息
func (c *Consumer) Start() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		return fmt.Errorf("consumer is already running")
	}

	// 连接到NSQ
	if c.nsqConfig.NSQDAddress != "" {
		err := c.consumer.ConnectToNSQD(c.nsqConfig.NSQDAddress)
		if err != nil {
			return fmt.Errorf("failed to connect to NSQD %s: %w", c.nsqConfig.NSQDAddress, err)
		}
	}

	// 连接到NSQ Lookupd
	if len(c.nsqConfig.LookupdAddresses) > 0 {
		err := c.consumer.ConnectToNSQLookupds(c.nsqConfig.LookupdAddresses)
		if err != nil {
			return fmt.Errorf("failed to connect to NSQ Lookupd: %w", err)
		}
	}

	// 如果既没有NSQD地址也没有Lookupd地址，使用默认地址
	if c.nsqConfig.NSQDAddress == "" && len(c.nsqConfig.LookupdAddresses) == 0 {
		err := c.consumer.ConnectToNSQD("127.0.0.1:4150")
		if err != nil {
			return fmt.Errorf("failed to connect to default NSQD: %w", err)
		}
	}

	c.running = true
	log.Info("NSQ consumer started for topic: %s, channel: %s", c.config.TopicName, c.config.ChannelName)

	return nil
}

// Stop 停止消费者
func (c *Consumer) Stop() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return
	}

	if c.consumer != nil {
		c.consumer.Stop()
		c.running = false
		log.Info("NSQ consumer stopped for topic: %s", c.config.TopicName)
	}
}

// IsRunning 检查消费者是否正在运行
func (c *Consumer) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}
