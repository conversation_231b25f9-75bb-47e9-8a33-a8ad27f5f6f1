package domain

// Country 国家代码常量，基于 ISO_3166-1 二位字母代码
const (
	// 亚洲
	CountryCN = "CN" // 中国
	CountryJP = "JP" // 日本
	CountryKR = "KR" // 韩国
	CountrySG = "SG" // 新加坡
	CountryTH = "TH" // 泰国
	CountryVN = "VN" // 越南
	CountryMY = "MY" // 马来西亚
	CountryID = "ID" // 印度尼西亚
	CountryPH = "PH" // 菲律宾
	CountryIN = "IN" // 印度
	CountryBD = "BD" // 孟加拉国
	CountryLK = "LK" // 斯里兰卡
	CountryNP = "NP" // 尼泊尔
	CountryMM = "MM" // 缅甸
	CountryKH = "KH" // 柬埔寨
	CountryLA = "LA" // 老挝
	CountryBN = "BN" // 文莱
	CountryTL = "TL" // 东帝汶
	CountryMV = "MV" // 马尔代夫
	CountryBT = "BT" // 不丹
	CountryMN = "MN" // 蒙古
	CountryKZ = "KZ" // 哈萨克斯坦
	CountryUZ = "UZ" // 乌兹别克斯坦
	CountryKG = "KG" // 吉尔吉斯斯坦
	CountryTJ = "TJ" // 塔吉克斯坦
	CountryTM = "TM" // 土库曼斯坦
	CountryAF = "AF" // 阿富汗
	CountryPK = "PK" // 巴基斯坦
	CountryIR = "IR" // 伊朗
	CountryIQ = "IQ" // 伊拉克
	CountrySY = "SY" // 叙利亚
	CountryLB = "LB" // 黎巴嫩
	CountryJO = "JO" // 约旦
	CountryIL = "IL" // 以色列
	CountryPS = "PS" // 巴勒斯坦
	CountrySA = "SA" // 沙特阿拉伯
	CountryYE = "YE" // 也门
	CountryOM = "OM" // 阿曼
	CountryAE = "AE" // 阿联酋
	CountryQA = "QA" // 卡塔尔
	CountryBH = "BH" // 巴林
	CountryKW = "KW" // 科威特
	CountryTR = "TR" // 土耳其
	CountryCY = "CY" // 塞浦路斯
	CountryGE = "GE" // 格鲁吉亚
	CountryAM = "AM" // 亚美尼亚
	CountryAZ = "AZ" // 阿塞拜疆

	// 欧洲
	CountryRU    = "RU" // 俄罗斯
	CountryUA    = "UA" // 乌克兰
	CountryBY    = "BY" // 白俄罗斯
	CountryMD    = "MD" // 摩尔多瓦
	CountryRO    = "RO" // 罗马尼亚
	CountryBG    = "BG" // 保加利亚
	CountryRS    = "RS" // 塞尔维亚
	CountryHR    = "HR" // 克罗地亚
	CountrySI    = "SI" // 斯洛文尼亚
	CountryBA    = "BA" // 波斯尼亚和黑塞哥维那
	CountryME    = "ME" // 黑山
	CountryMK    = "MK" // 北马其顿
	CountryAL    = "AL" // 阿尔巴尼亚
	CountryGR    = "GR" // 希腊
	CountryIT    = "IT" // 意大利
	CountryES    = "ES" // 西班牙
	CountryPT    = "PT" // 葡萄牙
	CountryFR    = "FR" // 法国
	CountryDE    = "DE" // 德国
	CountryAT    = "AT" // 奥地利
	CountryCH    = "CH" // 瑞士
	CountryLI    = "LI" // 列支敦士登
	CountryBE    = "BE" // 比利时
	CountryNL    = "NL" // 荷兰
	CountryLU    = "LU" // 卢森堡
	CountryPL    = "PL" // 波兰
	CountryCZ    = "CZ" // 捷克
	CountrySK    = "SK" // 斯洛伐克
	CountryHU    = "HU" // 匈牙利
	CountrySI_SI = "SI" // 斯洛文尼亚（重复，已定义）
	CountryDK    = "DK" // 丹麦
	CountrySE    = "SE" // 瑞典
	CountryNO    = "NO" // 挪威
	CountryFI    = "FI" // 芬兰
	CountryIS    = "IS" // 冰岛
	CountryEE    = "EE" // 爱沙尼亚
	CountryLV    = "LV" // 拉脱维亚
	CountryLT    = "LT" // 立陶宛
	CountryIE    = "IE" // 爱尔兰
	CountryGB    = "GB" // 英国
	CountryMT    = "MT" // 马耳他

	// 北美洲
	CountryUS = "US" // 美国
	CountryCA = "CA" // 加拿大
	CountryMX = "MX" // 墨西哥
	CountryGT = "GT" // 危地马拉
	CountryBZ = "BZ" // 伯利兹
	CountrySV = "SV" // 萨尔瓦多
	CountryHN = "HN" // 洪都拉斯
	CountryNI = "NI" // 尼加拉瓜
	CountryCR = "CR" // 哥斯达黎加
	CountryPA = "PA" // 巴拿马
	CountryCU = "CU" // 古巴
	CountryJM = "JM" // 牙买加
	CountryHT = "HT" // 海地
	CountryDO = "DO" // 多米尼加
	CountryPR = "PR" // 波多黎各
	CountryTT = "TT" // 特立尼达和多巴哥
	CountryBB = "BB" // 巴巴多斯
	CountryGD = "GD" // 格林纳达
	CountryLC = "LC" // 圣卢西亚
	CountryVC = "VC" // 圣文森特和格林纳丁斯
	CountryAG = "AG" // 安提瓜和巴布达
	CountryKN = "KN" // 圣基茨和尼维斯
	CountryDM = "DM" // 多米尼克

	// 南美洲
	CountryBR = "BR" // 巴西
	CountryAR = "AR" // 阿根廷
	CountryCL = "CL" // 智利
	CountryPE = "PE" // 秘鲁
	CountryCO = "CO" // 哥伦比亚
	CountryVE = "VE" // 委内瑞拉
	CountryEC = "EC" // 厄瓜多尔
	CountryBO = "BO" // 玻利维亚
	CountryPY = "PY" // 巴拉圭
	CountryUY = "UY" // 乌拉圭
	CountryGY = "GY" // 圭亚那
	CountrySR = "SR" // 苏里南
	CountryGF = "GF" // 法属圭亚那
	CountryFK = "FK" // 福克兰群岛

	// 非洲
	CountryZA = "ZA" // 南非
	CountryEG = "EG" // 埃及
	CountryLY = "LY" // 利比亚
	CountryTN = "TN" // 突尼斯
	CountryDZ = "DZ" // 阿尔及利亚
	CountryMA = "MA" // 摩洛哥
	CountrySD = "SD" // 苏丹
	CountrySS = "SS" // 南苏丹
	CountryET = "ET" // 埃塞俄比亚
	CountryER = "ER" // 厄立特里亚
	CountryDJ = "DJ" // 吉布提
	CountrySO = "SO" // 索马里
	CountryKE = "KE" // 肯尼亚
	CountryTZ = "TZ" // 坦桑尼亚
	CountryUG = "UG" // 乌干达
	CountryRW = "RW" // 卢旺达
	CountryBI = "BI" // 布隆迪
	CountryCD = "CD" // 刚果民主共和国
	CountryCG = "CG" // 刚果共和国
	CountryGA = "GA" // 加蓬
	CountryGQ = "GQ" // 赤道几内亚
	CountryST = "ST" // 圣多美和普林西比
	CountryCM = "CM" // 喀麦隆
	CountryNG = "NG" // 尼日利亚
	CountryNE = "NE" // 尼日尔
	CountryTD = "TD" // 乍得
	CountryCF = "CF" // 中非共和国
	CountryML = "ML" // 马里
	CountryBF = "BF" // 布基纳法索
	CountryCI = "CI" // 科特迪瓦
	CountryGH = "GH" // 加纳
	CountryTG = "TG" // 多哥
	CountryBJ = "BJ" // 贝宁
	CountrySN = "SN" // 塞内加尔
	CountryGM = "GM" // 冈比亚
	CountryGN = "GN" // 几内亚
	CountryGW = "GW" // 几内亚比绍
	CountrySL = "SL" // 塞拉利昂
	CountryLR = "LR" // 利比里亚
	CountryMR = "MR" // 毛里塔尼亚
	CountryMZ = "MZ" // 莫桑比克
	CountryZW = "ZW" // 津巴布韦
	CountryBW = "BW" // 博茨瓦纳
	CountryNA = "NA" // 纳米比亚
	CountrySZ = "SZ" // 斯威士兰
	CountryLS = "LS" // 莱索托
	CountryMG = "MG" // 马达加斯加
	CountryMU = "MU" // 毛里求斯
	CountrySC = "SC" // 塞舌尔
	CountryKM = "KM" // 科摩罗
	CountryYT = "YT" // 马约特
	CountryRE = "RE" // 留尼汪

	// 大洋洲
	CountryAU    = "AU" // 澳大利亚
	CountryNZ    = "NZ" // 新西兰
	CountryFJ    = "FJ" // 斐济
	CountryPG    = "PG" // 巴布亚新几内亚
	CountrySB    = "SB" // 所罗门群岛
	CountryVU    = "VU" // 瓦努阿图
	CountryNC    = "NC" // 新喀里多尼亚
	CountryPF    = "PF" // 法属波利尼西亚
	CountryWS    = "WS" // 萨摩亚
	CountryTO    = "TO" // 汤加
	CountryKI    = "KI" // 基里巴斯
	CountryTV    = "TV" // 图瓦卢
	CountryNR    = "NR" // 瑙鲁
	CountryPW    = "PW" // 帕劳
	CountryFM    = "FM" // 密克罗尼西亚联邦
	CountryMH    = "MH" // 马绍尔群岛
	CountryCK    = "CK" // 库克群岛
	CountryNU    = "NU" // 纽埃
	CountryTK    = "TK" // 托克劳
	CountryAS    = "AS" // 美属萨摩亚
	CountryGU    = "GU" // 关岛
	CountryMP    = "MP" // 北马里亚纳群岛
	CountryPW_PW = "PW" // 帕劳（重复，已定义）
)

// GetCountryName 根据国家代码获取国家名称
func GetCountryName(code string) string {
	countryNames := map[string]string{
		CountryCN: "中国",
		CountryJP: "日本",
		CountryKR: "韩国",
		CountrySG: "新加坡",
		CountryTH: "泰国",
		CountryVN: "越南",
		CountryMY: "马来西亚",
		CountryID: "印度尼西亚",
		CountryPH: "菲律宾",
		CountryIN: "印度",
		CountryUS: "美国",
		CountryCA: "加拿大",
		CountryMX: "墨西哥",
		CountryBR: "巴西",
		CountryAR: "阿根廷",
		CountryGB: "英国",
		CountryDE: "德国",
		CountryFR: "法国",
		CountryIT: "意大利",
		CountryES: "西班牙",
		CountryRU: "俄罗斯",
		CountryAU: "澳大利亚",
		CountryNZ: "新西兰",
		// 可以继续添加更多国家
	}

	if name, exists := countryNames[code]; exists {
		return name
	}
	return "未知国家"
}

// IsValidCountryCode 验证国家代码是否有效
func IsValidCountryCode(code string) bool {
	validCodes := map[string]bool{
		CountryCN: true, CountryJP: true, CountryKR: true, CountrySG: true,
		CountryTH: true, CountryVN: true, CountryMY: true, CountryID: true,
		CountryPH: true, CountryIN: true, CountryUS: true, CountryCA: true,
		CountryMX: true, CountryBR: true, CountryAR: true, CountryGB: true,
		CountryDE: true, CountryFR: true, CountryIT: true, CountryES: true,
		CountryRU: true, CountryAU: true, CountryNZ: true,
		// 可以继续添加更多国家代码
	}

	return validCodes[code]
}
