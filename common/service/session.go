package service

import (
	"context"
	"fmt"

	"hotel/common/domain"
	"hotel/common/idgen"
	"hotel/common/log"
	"hotel/common/utils"
	supplierDomain "hotel/supplier/domain"

	"github.com/bytedance/sonic"
	pkgerr "github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

const (
	// SessionCacheExpiration session 缓存过期时间（秒）
	SessionCacheExpiration = 3600 // 1小时
)

// SessionService session 服务，提供 session 的获取、创建和缓存功能
type SessionService struct {
	cache *redis.Redis
}

// NewSessionService 创建新的 SessionService 实例
func NewSessionService(cache *redis.Redis) *SessionService {
	return &SessionService{
		cache: cache,
	}
}

// GetSession 获取 session，如果不存在或出错则返回空 session
func (s *SessionService) GetSession(ctx context.Context, sessionId string) (*domain.Session, error) {
	if sessionId == "" {
		out := &domain.Session{
			Id: idgen.MustNextInt64ID().String(),
		}
		log.Infoc(ctx, "GetSession with empty sessionId, and generate new sessionId: %s", out.Id)
		return out, nil
	}

	j, err := s.cache.GetCtx(ctx, s.getSessionCacheKey(sessionId))
	if err != nil {
		log.Errorc(ctx, "Failed to get session data for sessionId %s: %v", sessionId, err)
		return nil, err
	}

	ss := utils.FromJSON[domain.Session](j)
	ss.Id = sessionId
	return &ss, nil
}

func (s *SessionService) GetSellerPayloads(ctx context.Context, session *domain.Session) (supplierDomain.SellerPayloadList, error) {
	sellerPayloadsJson := session.GetSellerPayloads(ctx)
	var sellerPayloads supplierDomain.SellerPayloadList
	if err := sonic.UnmarshalString(sellerPayloadsJson, &sellerPayloads); err != nil {
		log.Errorc(ctx, "Failed to unmarshal GetSellerPayloads: %v, json: %s", err, sellerPayloadsJson)
		_ = s.DeleteSession(ctx, session.Id)
		return nil, pkgerr.Wrapf(err, "unmarshal failed from: %s", sellerPayloadsJson)
	}
	return sellerPayloads, nil
}

func (s *SessionService) GetCheckAvailResp(ctx context.Context, session *domain.Session) (*supplierDomain.CheckAvailResp, error) {
	checkAvailRespJson := session.GetCheckAvailResp(ctx)
	var checkAvailResp = new(supplierDomain.CheckAvailResp)
	if err := sonic.UnmarshalString(checkAvailRespJson, &checkAvailResp); err != nil {
		log.Errorc(ctx, "Failed to unmarshal GetCheckAvailResp: %v, json: %s", err, checkAvailRespJson)
		_ = s.DeleteSession(ctx, session.Id)
		return nil, pkgerr.Wrapf(err, "unmarshal failed from: %s", checkAvailRespJson)
	}
	return checkAvailResp, nil
}

// GetOrCreateSession 获取或创建 session，行为与 GetSession 相同
// 保留此方法以保持向后兼容性
func (s *SessionService) GetOrCreateSession(ctx context.Context, sessionId string) (*domain.Session, error) {
	return s.GetSession(ctx, sessionId)
}

// CacheSession 缓存 session
func (s *SessionService) CacheSession(ctx context.Context, sessionId string, session *domain.Session) error {
	if sessionId == "" {
		return nil // 空 sessionId 不需要缓存
	}
	ss := utils.ToJSON(session)
	log.Infoc(ctx, "CacheSession %s=%s", sessionId, ss)
	if err := s.cache.SetexCtx(ctx, s.getSessionCacheKey(sessionId), ss, SessionCacheExpiration); err != nil {
		return pkgerr.Wrapf(err, "Set session cache for sessionId %s", sessionId)
	}
	return nil
}

// DeleteSession 删除 session 缓存
func (s *SessionService) DeleteSession(ctx context.Context, sessionId string) error {
	if sessionId == "" {
		return nil
	}

	_, err := s.cache.DelCtx(ctx, s.getSessionCacheKey(sessionId))
	if err != nil {
		return pkgerr.Wrapf(err, "Delete session cache for sessionId %s", sessionId)
	}
	return nil
}

// getSessionCacheKey 生成 session 缓存键
func (s *SessionService) getSessionCacheKey(sessionId string) string {
	return fmt.Sprintf("s:%s", sessionId)
}
