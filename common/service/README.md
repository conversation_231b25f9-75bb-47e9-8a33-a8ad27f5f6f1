# Common Service Package

这个包提供了通用的服务组件，用于在不同的业务模块之间共享通用逻辑。

## SessionService

SessionService 提供了统一的 session 管理功能，包括 session 的获取、缓存和删除操作。

### 功能特性

- **统一的 session 管理**：提供一致的 session 操作接口
- **Redis 缓存支持**：使用 Redis 作为 session 存储后端
- **自动过期管理**：session 缓存自动过期（默认 1 小时）
- **错误处理**：优雅的错误处理和日志记录
- **空值处理**：正确处理空 sessionId 的情况

### 使用方法

#### 1. 创建 SessionService 实例

```go
import (
    "github.com/zeromicro/go-zero/core/stores/redis"
    commonService "hotel/common/service"
)

// 创建 Redis 客户端
redisClient, err := redis.NewRedis(redisConfig)
if err != nil {
    panic(err)
}

// 创建 SessionService
sessionService := commonService.NewSessionService(redisClient)
```

#### 2. 在服务结构体中使用

```go
type YourService struct {
    // 其他字段...
    sessionService *commonService.SessionService
}

func NewYourService(redisClient *redis.Redis) *YourService {
    return &YourService{
        // 其他初始化...
        sessionService: commonService.NewSessionService(redisClient),
    }
}
```

#### 3. 基本操作

```go
import (
    "context"
    "hotel/common/domain"
)

ctx := context.Background()

// 获取 session
session, err := service.sessionService.GetSession(ctx, sessionId)
if err != nil {
    // 处理错误
}

// 缓存 session
session := &domain.Session{
    Id: sessionId,
    Params: map[string]string{
        "key": "value",
    },
}
err = service.sessionService.CacheSession(ctx, sessionId, session)
if err != nil {
    // 处理错误
}

// 删除 session
err = service.sessionService.DeleteSession(ctx, sessionId)
if err != nil {
    // 处理错误
}
```

### API 参考

#### GetSession

```go
func (s *SessionService) GetSession(ctx context.Context, sessionId string) (*domain.Session, error)
```

获取指定的 session。如果 sessionId 为空或 session 不存在，返回空的 Session 对象。

#### GetOrCreateSession

```go
func (s *SessionService) GetOrCreateSession(ctx context.Context, sessionId string) (*domain.Session, error)
```

获取或创建 session。目前行为与 GetSession 相同，保留此方法以保持向后兼容性。

#### CacheSession

```go
func (s *SessionService) CacheSession(ctx context.Context, sessionId string, session *domain.Session) error
```

将 session 缓存到 Redis。如果 sessionId 为空，不执行任何操作。

#### DeleteSession

```go
func (s *SessionService) DeleteSession(ctx context.Context, sessionId string) error
```

从 Redis 中删除指定的 session。如果 sessionId 为空，不执行任何操作。

### 配置

- **缓存过期时间**：默认为 3600 秒（1 小时），可通过 `SessionCacheExpiration` 常量修改
- **缓存键格式**：`s:{sessionId}`

### 迁移指南

如果你之前在服务中实现了自己的 session 管理逻辑，可以按照以下步骤迁移到 SessionService：

1. 在服务结构体中添加 `sessionService` 字段
2. 在构造函数中初始化 SessionService
3. 替换现有的 session 操作调用：
   - `getSession()` → `sessionService.GetSession()`
   - `getOrCreateSession()` → `sessionService.GetOrCreateSession()`
   - `cacheSession()` → `sessionService.CacheSession()`
   - `getSessionCacheKey()` → 不再需要，内部处理
4. 移除旧的 session 相关方法

### 测试

SessionService 包含完整的单元测试，覆盖所有主要功能。运行测试：

```bash
cd common/service
go test -v
```
