package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"hotel/common/domain"
	"hotel/common/testkit"
)

func TestSessionService(t *testing.T) {
	// 使用测试 Redis
	mr, cacheConf := testkit.CreateMiniRedis()
	defer mr.Close()

	r, err := redis.NewRedis(cacheConf[0].RedisConf)
	if err != nil {
		t.Fatal(err)
	}

	sessionService := NewSessionService(r)
	ctx := context.Background()

	t.Run("GetSession with empty sessionId", func(t *testing.T) {
		session, err := sessionService.GetSession(ctx, "")
		assert.NoError(t, err)
		assert.NotNil(t, session)
		assert.Equal(t, "", session.Id)
	})

	t.Run("GetSession with non-existent sessionId", func(t *testing.T) {
		session, err := sessionService.GetSession(ctx, "non-existent")
		assert.NoError(t, err)
		assert.NotNil(t, session)
		assert.Equal(t, "", session.Id)
	})

	t.Run("CacheSession and GetSession", func(t *testing.T) {
		sessionId := "test-session-123"
		originalSession := &domain.Session{
			Id: sessionId,
			Params: map[string]string{
				"key1": "value1",
				"key2": "value2",
			},
		}

		// 缓存 session
		err := sessionService.CacheSession(ctx, sessionId, originalSession)
		assert.NoError(t, err)

		// 获取 session
		retrievedSession, err := sessionService.GetSession(ctx, sessionId)
		assert.NoError(t, err)
		assert.NotNil(t, retrievedSession)
		assert.Equal(t, sessionId, retrievedSession.Id)
		assert.Equal(t, "value1", retrievedSession.Params["key1"])
		assert.Equal(t, "value2", retrievedSession.Params["key2"])
	})

	t.Run("GetOrCreateSession", func(t *testing.T) {
		sessionId := "test-session-456"

		// 第一次调用，session 不存在
		session, err := sessionService.GetOrCreateSession(ctx, sessionId)
		assert.NoError(t, err)
		assert.NotNil(t, session)
		assert.Equal(t, "", session.Id) // 空 session

		// 缓存一个 session
		originalSession := &domain.Session{
			Id: sessionId,
			Params: map[string]string{
				"test": "data",
			},
		}
		err = sessionService.CacheSession(ctx, sessionId, originalSession)
		assert.NoError(t, err)

		// 再次调用，应该返回缓存的 session
		session, err = sessionService.GetOrCreateSession(ctx, sessionId)
		assert.NoError(t, err)
		assert.NotNil(t, session)
		assert.Equal(t, sessionId, session.Id)
		assert.Equal(t, "data", session.Params["test"])
	})

	t.Run("DeleteSession", func(t *testing.T) {
		sessionId := "test-session-789"
		originalSession := &domain.Session{
			Id: sessionId,
			Params: map[string]string{
				"temp": "data",
			},
		}

		// 缓存 session
		err := sessionService.CacheSession(ctx, sessionId, originalSession)
		assert.NoError(t, err)

		// 验证 session 存在
		session, err := sessionService.GetSession(ctx, sessionId)
		assert.NoError(t, err)
		assert.Equal(t, sessionId, session.Id)

		// 删除 session
		err = sessionService.DeleteSession(ctx, sessionId)
		assert.NoError(t, err)

		// 验证 session 已被删除
		session, err = sessionService.GetSession(ctx, sessionId)
		assert.NoError(t, err)
		assert.Equal(t, "", session.Id) // 应该返回空 session
	})

	t.Run("CacheSession with empty sessionId", func(t *testing.T) {
		session := &domain.Session{
			Id: "test",
			Params: map[string]string{
				"key": "value",
			},
		}

		// 空 sessionId 应该不做任何操作
		err := sessionService.CacheSession(ctx, "", session)
		assert.NoError(t, err)
	})

	t.Run("DeleteSession with empty sessionId", func(t *testing.T) {
		// 空 sessionId 应该不做任何操作
		err := sessionService.DeleteSession(ctx, "")
		assert.NoError(t, err)
	})
}
