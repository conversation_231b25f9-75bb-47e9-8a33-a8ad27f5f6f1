package sentry

import (
	"context"
	"fmt"
	"os"
	"time"

	"hotel/common/log"

	"github.com/getsentry/sentry-go"
)

// Config Sentry 配置
type Config struct {
	DSN         string        // Sentry DSN
	Environment string        // 环境标识：dev, test, production
	Release     string        // 版本号
	SampleRate  float64       // 采样率，0.0 到 1.0
	Timeout     time.Duration // 发送超时时间
}

// Init 初始化 Sentry
func Init(cfg Config) error {
	// 如果 DSN 为空，则不初始化 Sentry
	if cfg.DSN == "" {
		log.Info("Sentry DSN is empty, skipping Sentry initialization")
		return nil
	}

	// 设置默认值
	if cfg.Environment == "" {
		cfg.Environment = os.Getenv("ENV")
		if cfg.Environment == "" {
			cfg.Environment = "development"
		}
	}

	if cfg.SampleRate == 0 {
		cfg.SampleRate = 1.0 // 默认采样率为 100%
	}

	if cfg.Timeout == 0 {
		cfg.Timeout = 2 * time.Second
	}

	err := sentry.Init(sentry.ClientOptions{
		Dsn:              cfg.DSN,
		Environment:      cfg.Environment,
		Release:          cfg.Release,
		SampleRate:       cfg.SampleRate,
		AttachStacktrace: true,
		BeforeSend: func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			// 过滤敏感信息
			if event.Request != nil {
				// 移除敏感的请求头
				if event.Request.Headers != nil {
					delete(event.Request.Headers, "Authorization")
					delete(event.Request.Headers, "Cookie")
					delete(event.Request.Headers, "X-API-Key")
				}
			}

			// 添加自定义标签
			if event.Tags == nil {
				event.Tags = make(map[string]string)
			}
			event.Tags["component"] = "hotel-api"

			return event
		},
	})

	if err != nil {
		return fmt.Errorf("failed to initialize Sentry: %w", err)
	}

	log.Info("Sentry initialized successfully with environment: %s", cfg.Environment)
	return nil
}

// CaptureException 捕获异常并发送到 Sentry
func CaptureException(ctx context.Context, err error, tags map[string]string, extra map[string]interface{}) {
	if err == nil {
		return
	}

	// 配置 Sentry Hub
	hub := sentry.GetHubFromContext(ctx)
	if hub == nil {
		hub = sentry.CurrentHub()
	}

	hub.WithScope(func(scope *sentry.Scope) {
		// 添加标签
		for k, v := range tags {
			scope.SetTag(k, v)
		}

		// 添加额外信息
		for k, v := range extra {
			scope.SetExtra(k, v)
		}

		// 从上下文中提取请求信息
		if ctx != nil {
			if traceID := log.GetLogidFromContext(ctx); traceID != "" {
				scope.SetTag("trace_id", traceID)
			}
		}

		// 设置级别
		scope.SetLevel(sentry.LevelError)

		// 捕获异常
		hub.CaptureException(err)
	})
}

// CapturePanic 捕获 panic 并发送到 Sentry
func CapturePanic(ctx context.Context, recovered interface{}, tags map[string]string, extra map[string]interface{}) {
	if recovered == nil {
		return
	}

	// 配置 Sentry Hub
	hub := sentry.GetHubFromContext(ctx)
	if hub == nil {
		hub = sentry.CurrentHub()
	}

	hub.WithScope(func(scope *sentry.Scope) {
		// 添加标签
		for k, v := range tags {
			scope.SetTag(k, v)
		}

		// 添加额外信息
		for k, v := range extra {
			scope.SetExtra(k, v)
		}

		// 从上下文中提取请求信息
		if ctx != nil {
			if traceID := log.GetLogidFromContext(ctx); traceID != "" {
				scope.SetTag("trace_id", traceID)
			}
		}

		// 设置级别为致命错误
		scope.SetLevel(sentry.LevelFatal)

		// 将 panic 转换为错误
		var err error
		switch v := recovered.(type) {
		case error:
			err = v
		case string:
			err = fmt.Errorf("panic: %s", v)
		default:
			err = fmt.Errorf("panic: %v", v)
		}

		// 捕获异常
		hub.CaptureException(err)
	})
}

// CaptureMessage 捕获消息并发送到 Sentry
func CaptureMessage(ctx context.Context, message string, level sentry.Level, tags map[string]string, extra map[string]interface{}) {
	// 配置 Sentry Hub
	hub := sentry.GetHubFromContext(ctx)
	if hub == nil {
		hub = sentry.CurrentHub()
	}

	hub.WithScope(func(scope *sentry.Scope) {
		// 添加标签
		for k, v := range tags {
			scope.SetTag(k, v)
		}

		// 添加额外信息
		for k, v := range extra {
			scope.SetExtra(k, v)
		}

		// 从上下文中提取请求信息
		if ctx != nil {
			if traceID := log.GetLogidFromContext(ctx); traceID != "" {
				scope.SetTag("trace_id", traceID)
			}
		}

		// 设置级别
		scope.SetLevel(level)

		// 捕获消息
		hub.CaptureMessage(message)
	})
}

// Flush 刷新缓冲区，确保所有事件都发送到 Sentry
func Flush(timeout time.Duration) bool {
	return sentry.Flush(timeout)
}

// Close 关闭 Sentry 客户端
func Close() {
	Flush(2 * time.Second)
}