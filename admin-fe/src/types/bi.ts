// API接口类型定义
export interface Money {
  amount: number
  currency: string
}

export interface I18N {
  'zh-CN': string
  'en-US': string
}

export interface OrderOverview {
  totalOrders: number
  totalRevenue: Money
  totalProfit: Money
  averageOrderValue: Money
  bookingRate: number
  cancellationRate: number
  completionRate: number
  growthRate: number
}

export interface OrderTrendData {
  date: string
  orderCount: number
  revenue: Money
  profit: Money
  bookingCount: number
  cancelCount: number
}

export interface OrderStatusStat {
  status: number
  statusName: I18N
  count: number
  percentage: number
  revenue: Money
  avgDuration?: number
}

export interface CurrencyRevenue {
  currency: string
  revenue: Money
  orderCount: number
  percentage: number
}

export interface EntityRevenue {
  entityId: number
  entityName: I18N
  revenue: Money
  profit: Money
  orderCount: number
  growthRate: number
}

export interface MonthlyComparison {
  month: string
  revenue: Money
  profit: Money
  orderCount: number
  growthRate: number
}

export interface RevenueAnalysis {
  totalRevenue: Money
  totalCost: Money
  totalProfit: Money
  profitMargin: number
  revenueByCurrency: CurrencyRevenue[]
  revenueByEntity: EntityRevenue[]
  monthlyComparison: MonthlyComparison[]
}

export interface CustomerPerformance {
  customerId: number
  customerName: I18N
  totalRevenue: Money
  orderCount: number
  avgOrderValue: Money
  lastOrderDate: string
}

export interface EntityPerformance {
  entityId: number
  entityName: I18N
  totalRevenue: Money
  totalProfit: Money
  orderCount: number
  profitMargin: number
}

export interface DayPerformance {
  date: string
  revenue: Money
  orderCount: number
  profit: Money
}

export interface TopPerformers {
  topCustomers: CustomerPerformance[]
  topEntities: EntityPerformance[]
  topDays: DayPerformance[]
}

export interface RegionalStat {
  region: string
  orderCount: number
  revenue: Money
  profit: Money
  growthRate: number
}

export interface OrderAnalyticsReq {
  startDate?: string
  endDate?: string
  granularity?: 'day' | 'week' | 'month' | 'year'
  statusFilter?: number[]
  entityId?: number
}

export interface OrderAnalyticsResp {
  overview: OrderOverview
  trendData: OrderTrendData[]
  statusBreakdown: OrderStatusStat[]
  revenueAnalysis: RevenueAnalysis
  topPerformers: TopPerformers
  regionalAnalysis: RegionalStat[]
}

export interface OrderMetricsReq {
  metricType: 'overview' | 'trend' | 'status' | 'revenue'
  startDate?: string
  endDate?: string
  granularity?: 'day' | 'week' | 'month'
  filters?: Record<string, any>
}

export interface OrderMetricsResp {
  metricType: string
  data: any
  updateTime: string
}

export interface RealTimeMetricsReq {
  metrics: string[]
}

export interface RealTimeMetricsResp {
  todayOrders: number
  todayRevenue: Money
  activeBookings: number
  pendingOrders: number
  completionRate: number
  cancellationRate: number
  updateTime: string
}

export interface ExportReq {
  exportType: 'excel' | 'csv' | 'pdf'
  dataType: 'orders' | 'analytics' | 'revenue'
  startDate?: string
  endDate?: string
  filters?: Record<string, any>
  fields?: string[]
}

export interface ExportResp {
  fileUrl: string
  fileName: string
  fileSize: number
  expiryTime: string
}