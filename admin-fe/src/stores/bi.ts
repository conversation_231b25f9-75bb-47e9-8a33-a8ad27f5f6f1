import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { biApi, exportApi } from '@/api/biApi'
import type {
  OrderOverview,
  OrderTrendData,
  OrderStatusStat,
  RevenueAnalysis,
  TopPerformers,
  RealTimeMetricsResp,
  Money
} from '@/types/bi'
import { ElMessage } from 'element-plus'

export const useBIStore = defineStore('bi', () => {
  // 状态数据
  const loading = ref(false)
  const overview = ref<OrderOverview | null>(null)
  const trendData = ref<OrderTrendData[]>([])
  const statusBreakdown = ref<OrderStatusStat[]>([])
  const revenueAnalysis = ref<RevenueAnalysis | null>(null)
  const topPerformers = ref<TopPerformers | null>(null)
  const realTimeMetrics = ref<RealTimeMetricsResp | null>(null)
  
  // 过滤条件
  const dateRange = ref<[Date, Date]>([
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
    new Date()
  ])
  const selectedGranularity = ref<'day' | 'week' | 'month'>('day')
  const selectedEntity = ref<number | null>(null)
  
  // 计算属性
  const dateRangeString = computed(() => {
    return [
      dateRange.value[0].toISOString().split('T')[0],
      dateRange.value[1].toISOString().split('T')[0]
    ] as [string, string]
  })
  
  const isDataLoaded = computed(() => {
    return overview.value !== null && trendData.value.length > 0
  })
  
  const totalRevenue = computed(() => {
    return overview.value?.totalRevenue || { amount: 0, currency: 'USD' }
  })
  
  const totalProfit = computed(() => {
    return overview.value?.totalProfit || { amount: 0, currency: 'USD' }
  })
  
  const profitMargin = computed(() => {
    if (!overview.value || !overview.value.totalRevenue.amount) return 0
    return (overview.value.totalProfit.amount / overview.value.totalRevenue.amount) * 100
  })

  // Actions
  const loadOverviewData = async () => {
    try {
      const [startDate, endDate] = dateRangeString.value
      const response = await biApi.getOverview(startDate, endDate, selectedEntity.value || undefined)
      overview.value = response.data as OrderOverview
    } catch (error) {
      console.error('Failed to load overview data:', error)
      ElMessage.error('加载总览数据失败')
      throw error
    }
  }

  const loadTrendData = async () => {
    try {
      const [startDate, endDate] = dateRangeString.value
      const response = await biApi.getTrend(
        startDate, 
        endDate, 
        selectedGranularity.value,
        selectedEntity.value || undefined
      )
      trendData.value = response.data as OrderTrendData[]
    } catch (error) {
      console.error('Failed to load trend data:', error)
      ElMessage.error('加载趋势数据失败')
      throw error
    }
  }

  const loadStatusData = async () => {
    try {
      const [startDate, endDate] = dateRangeString.value
      const response = await biApi.getStatusBreakdown(startDate, endDate, selectedEntity.value || undefined)
      statusBreakdown.value = response.data as OrderStatusStat[]
    } catch (error) {
      console.error('Failed to load status data:', error)
      ElMessage.error('加载状态数据失败')
      throw error
    }
  }

  const loadRevenueData = async () => {
    try {
      const [startDate, endDate] = dateRangeString.value
      const response = await biApi.getRevenueAnalysis(startDate, endDate, selectedEntity.value || undefined)
      revenueAnalysis.value = response.data as RevenueAnalysis
    } catch (error) {
      console.error('Failed to load revenue data:', error)
      ElMessage.error('加载收入数据失败')
      throw error
    }
  }

  const loadRealTimeData = async () => {
    try {
      const response = await biApi.getRealTimeData()
      realTimeMetrics.value = response.data as RealTimeMetricsResp
    } catch (error) {
      console.error('Failed to load real-time data:', error)
      ElMessage.error('加载实时数据失败')
      throw error
    }
  }

  const loadFullAnalytics = async () => {
    try {
      const [startDate, endDate] = dateRangeString.value
      const response = await biApi.getFullAnalytics({
        startDate,
        endDate,
        granularity: selectedGranularity.value,
        entityId: selectedEntity.value || undefined
      })
      
      overview.value = response.overview
      trendData.value = response.trendData
      statusBreakdown.value = response.statusBreakdown
      revenueAnalysis.value = response.revenueAnalysis
      topPerformers.value = response.topPerformers
    } catch (error) {
      console.error('Failed to load full analytics:', error)
      ElMessage.error('加载分析数据失败')
      throw error
    }
  }

  const loadAllData = async () => {
    loading.value = true
    try {
      await Promise.all([
        loadFullAnalytics(),
        loadRealTimeData()
      ])
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      loading.value = false
    }
  }

  const refreshData = async () => {
    await loadAllData()
    ElMessage.success('数据已刷新')
  }

  // 导出功能
  const exportData = async (format: 'excel' | 'csv' | 'pdf', dataType: 'orders' | 'analytics' | 'revenue') => {
    try {
      loading.value = true
      const [startDate, endDate] = dateRangeString.value
      
      let response
      switch (dataType) {
        case 'orders':
          response = await exportApi.exportOrders(format, startDate, endDate, {
            entityId: selectedEntity.value
          })
          break
        case 'analytics':
          response = await exportApi.exportAnalytics(format, startDate, endDate)
          break
        case 'revenue':
          response = await exportApi.exportRevenue(format, startDate, endDate)
          break
        default:
          throw new Error('Unsupported data type')
      }
      
      // 下载文件
      window.open(response.fileUrl, '_blank')
      ElMessage.success(`${dataType}数据导出成功`)
      
      return response
    } catch (error) {
      console.error('Export failed:', error)
      ElMessage.error('导出失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 过滤条件更新
  const updateDateRange = (newRange: [Date, Date]) => {
    dateRange.value = newRange
  }

  const updateGranularity = (granularity: 'day' | 'week' | 'month') => {
    selectedGranularity.value = granularity
  }

  const updateEntity = (entityId: number | null) => {
    selectedEntity.value = entityId
  }

  // 数据格式化工具
  const formatMoney = (money: Money): string => {
    if (!money) return '0'
    
    const { amount, currency } = money
    
    if (amount >= 1000000) {
      return `${currency} ${(amount / 1000000).toFixed(1)}M`
    } else if (amount >= 1000) {
      return `${currency} ${(amount / 1000).toFixed(1)}K`
    }
    
    return `${currency} ${amount.toLocaleString()}`
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return new Intl.NumberFormat().format(num)
  }

  const formatPercentage = (num: number): string => {
    return `${num.toFixed(1)}%`
  }

  // 重置数据
  const resetData = () => {
    overview.value = null
    trendData.value = []
    statusBreakdown.value = []
    revenueAnalysis.value = null
    topPerformers.value = null
    realTimeMetrics.value = null
  }

  return {
    // 状态
    loading,
    overview,
    trendData,
    statusBreakdown,
    revenueAnalysis,
    topPerformers,
    realTimeMetrics,
    
    // 过滤条件
    dateRange,
    selectedGranularity,
    selectedEntity,
    
    // 计算属性
    dateRangeString,
    isDataLoaded,
    totalRevenue,
    totalProfit,
    profitMargin,
    
    // 数据加载方法
    loadOverviewData,
    loadTrendData,
    loadStatusData,
    loadRevenueData,
    loadRealTimeData,
    loadFullAnalytics,
    loadAllData,
    refreshData,
    
    // 导出方法
    exportData,
    
    // 过滤条件更新
    updateDateRange,
    updateGranularity,
    updateEntity,
    
    // 工具方法
    formatMoney,
    formatNumber,
    formatPercentage,
    resetData
  }
})