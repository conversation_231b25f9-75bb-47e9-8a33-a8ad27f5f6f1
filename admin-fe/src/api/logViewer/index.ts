import request from '@/utils/http'
import type {
  QueryLogsParams,
  QueryLogsResponse,
  GetLogDetailParams,
  GetLogDetailResponse,
  ExportLogsParams,
  ExportLogsResponse,
  GetLogStatisticsParams,
  GetLogStatisticsResponse
} from './types'

/**
 * 日志查看器 API
 */
export const logViewerApi = {
  /**
   * 查询日志列表
   */
  async queryLogs(params: QueryLogsParams): Promise<QueryLogsResponse> {
    return request.post({
      url: '/api/bi/log/QueryLogs',
      data: params
    })
  },

  /**
   * 获取日志详情
   */
  async getLogDetail(params: GetLogDetailParams): Promise<GetLogDetailResponse> {
    return request.post({
      url: '/api/bi/log/GetLog',
      data: { logID: params.logId }
    })
  },

  /**
   * 导出日志 - 暂时使用查询接口，前端处理导出
   */
  async exportLogs(params: ExportLogsParams): Promise<ExportLogsResponse> {
    // 由于后端没有导出接口，使用查询接口获取数据，前端处理导出
    const queryParams: QueryLogsParams = {
       startTime: params.startTime,
       endTime: params.endTime,
       logId: params.logId,
       sessionId: params.sessionId,
       userId: params.userId,
       entityId: params.entityId,
       bizId: params.bizId,
       apiPath: params.apiPath,
       supplier: params.supplier,
       errorCode: params.errorCode,
       inputContentKeyword: params.inputContentKeyword,
       outputContentKeyword: params.outputContentKeyword,
       page: 1,
       pageSize: 10000 // 导出时获取更多数据
     }
    
    const result = await this.queryLogs(queryParams)
    
    // 模拟导出响应
    return {
      downloadUrl: '', // 前端生成
      fileName: `logs_export_${new Date().getTime()}.${params.format}`,
      fileSize: 0 // 前端计算
    }
  },

  /**
   * 获取日志统计 - 暂时使用查询接口，前端计算统计
   */
  async getLogStatistics(params: GetLogStatisticsParams): Promise<GetLogStatisticsResponse> {
    // 由于后端没有统计接口，使用查询接口获取数据，前端计算统计
    const queryParams: QueryLogsParams = {
       startTime: params.startTime,
       endTime: params.endTime,
       page: 1,
       pageSize: 10000 // 获取足够数据用于统计
     }
    
    const result = await this.queryLogs(queryParams)
    
    // 模拟统计响应
    return {
      totalCount: result.total,
      successCount: 0, // 前端计算
      errorCount: 0, // 前端计算
      avgResponseTime: 0, // 前端计算
      groupStats: [], // 前端计算
      trendData: [] // 前端计算
    }
  }
}

export * from './types'
