/**
 * 日志查看器相关类型定义
 */

// 查询日志参数 - 与后端 LogQuery 结构保持一致
export interface QueryLogsParams {
  // 时间范围
  startTime?: string
  endTime?: string
  
  // 基础查询条件
  logId?: string
  sessionId?: string
  userId?: number
  sellerEntityId?: number
  buyerEntityId?: number
  entityId?: string
  bizId?: string
  
  // 供应商业务相关
  supplierBizId?: string
  supplierBizType?: number
  
  // API 相关
  apiInPath?: string
  apiOutSupplier?: string
  apiOutPath?: string
  apiPath?: string
  supplier?: string
  
  // 错误相关
  bizErrorCode?: string
  errorCode?: string
  
  // 输入相关搜索
  inputHeaderKey?: string
  inputHeaderValue?: string
  inputCredentialKey?: string
  inputCredentialValue?: string
  inputBodyKeyword?: string
  inputContentKeyword?: string
  
  // 输出相关搜索
  outputBodyKeyword?: string
  outputHeaderKey?: string
  outputHeaderValue?: string
  outputHttpStatusCode?: number
  outputContentKeyword?: string
  
  // 性能相关 (毫秒)
  costTimeMin?: number
  costTimeMax?: number
  
  // 分页
  page: number
  pageSize: number // 后端使用 pageSize 而不是 size
}

// 日志列表项 - 与后端 HBLog 结构保持一致
export interface LogItem {
  // 基础信息
  timestamp: string
  logId: string
  sessionId: string
  userId: number
  entityId: number
  bizId: number
  
  // API 信息
  apiIn?: {
    path: string
    bizType: string
    bizId: string
  }
  apiOut?: {
    path: string
    bizType: string
    bizId: string
    supplier: string
  }
  
  // 输入输出信息
  input?: {
    header?: Record<string, string>
    body?: string
    bodySize?: number
    credential?: Record<string, string>
  }
  output?: {
    header?: Record<string, string>
    body?: string
    bodySize?: number
    costTime?: number // 毫秒
    internalCostTime?: number // 毫秒
    httpStatusCode?: number
  }
  
  // 业务错误
  bizError?: {
    code: number
    message: string
  }
  
  // 性能信息
  costTime: number // 毫秒
  
  // 自定义键值对
  kvs?: Record<string, any>
  
  // 状态标识 (前端计算)
  status?: 'success' | 'warning' | 'error'
}

// 查询日志响应 - 与后端 QueryResult 结构保持一致
export interface QueryLogsResponse {
  logs: LogItem[]
  total: number
}

// 获取日志详情参数
export interface GetLogDetailParams {
  logId: string
}

// 日志详情 - 与后端 HBLog 结构完全一致
export interface LogDetail {
  // 基础信息
  timestamp: string
  logId: string
  sessionId: string
  userId: number
  entityId: number
  bizId: number
  
  // API 信息
  apiIn?: {
    path: string
    bizType: string
    bizId: string
  }
  apiOut?: {
    path: string
    bizType: string
    bizId: string
    supplier: string
  }
  
  // 输入信息
  input?: {
    header?: Record<string, string>
    body?: string
    bodySize?: number
    credential?: Record<string, string>
  }
  
  // 输出信息
  output?: {
    header?: Record<string, string>
    body?: string
    bodySize?: number
    costTime?: number // 毫秒
    internalCostTime?: number // 毫秒
    httpStatusCode?: number
  }
  
  // 业务错误
  bizError?: {
    code: number
    message: string
  }
  
  // 性能信息
  costTime: number // 毫秒
  
  // 自定义键值对
  kvs?: Record<string, any>
}

// 获取日志详情响应 - 后端直接返回 HBLog 对象
export interface GetLogDetailResponse extends LogDetail {
  // 后端的 GetLog 方法直接返回 *domain.HBLog，不是包装对象
}

// 导出日志参数
export interface ExportLogsParams extends QueryLogsParams {
  // 导出格式
  format: 'csv' | 'json' | 'excel'
  
  // 导出字段（CSV格式时使用）
  fields?: string[]
}

// 导出日志响应
export interface ExportLogsResponse {
  downloadUrl: string
  fileName: string
  fileSize: number
}

// 获取日志统计参数
export interface GetLogStatisticsParams {
  // 时间范围
  startTime?: string
  endTime?: string

  // 统计维度
  groupBy?: 'hour' | 'day' | 'supplier' | 'api'
}

// 分组统计
export interface GroupStat {
  name: string
  count: number
  rate: number // 百分比
}

// 趋势点
export interface TrendPoint {
  time: string
  totalCount: number
  errorCount: number
  errorRate: number
  avgCostTime: number
}

// 获取日志统计响应
export interface GetLogStatisticsResponse {
  totalCount: number
  successCount: number
  warningCount: number
  errorCount: number
  avgCostTime: number // 毫秒

  // 分组统计
  groupStats?: GroupStat[]

  // 趋势数据
  trendData?: TrendPoint[]
}
