import { request } from '@/utils/request'
import type {
  OrderAnalyticsReq,
  OrderAnalyticsResp,
  OrderMetricsReq,
  OrderMetricsResp,
  RealTimeMetricsReq,
  RealTimeMetricsResp,
  ExportReq,
  ExportResp
} from '@/types/bi'

// BI订单分析API
export const orderBIApi = {
  // 获取订单分析数据
  getOrderAnalytics(params: OrderAnalyticsReq): Promise<OrderAnalyticsResp> {
    return request.post('/api/bi/order/analytics', params)
  },

  // 获取订单指标
  getOrderMetrics(params: OrderMetricsReq): Promise<OrderMetricsResp> {
    return request.post('/api/bi/order/metrics', params)
  },

  // 获取实时指标
  getRealTimeMetrics(params: RealTimeMetricsReq): Promise<RealTimeMetricsResp> {
    return request.post('/api/bi/order/realtime', params)
  },

  // 导出数据
  exportData(params: ExportReq): Promise<ExportResp> {
    return request.post('/api/bi/export', params)
  }
}

// 便捷方法
export const biApi = {
  // 获取总览数据
  async getOverview(startDate?: string, endDate?: string, entityId?: number) {
    return orderBIApi.getOrderMetrics({
      metricType: 'overview',
      startDate,
      endDate,
      filters: entityId ? { entityId } : undefined
    })
  },

  // 获取趋势数据
  async getTrend(startDate?: string, endDate?: string, granularity: 'day' | 'week' | 'month' = 'day', entityId?: number) {
    return orderBIApi.getOrderMetrics({
      metricType: 'trend',
      startDate,
      endDate,
      granularity,
      filters: entityId ? { entityId } : undefined
    })
  },

  // 获取状态分布
  async getStatusBreakdown(startDate?: string, endDate?: string, entityId?: number) {
    return orderBIApi.getOrderMetrics({
      metricType: 'status',
      startDate,
      endDate,
      filters: entityId ? { entityId } : undefined
    })
  },

  // 获取收入分析
  async getRevenueAnalysis(startDate?: string, endDate?: string, entityId?: number) {
    return orderBIApi.getOrderMetrics({
      metricType: 'revenue',
      startDate,
      endDate,
      filters: entityId ? { entityId } : undefined
    })
  },

  // 获取实时数据
  async getRealTimeData() {
    return orderBIApi.getRealTimeMetrics({
      metrics: ['todayOrders', 'todayRevenue', 'activeBookings', 'pendingOrders', 'completionRate', 'cancellationRate']
    })
  },

  // 获取完整分析数据
  async getFullAnalytics(params: OrderAnalyticsReq) {
    return orderBIApi.getOrderAnalytics(params)
  }
}

// 导出相关API
export const exportApi = {
  // 导出订单数据
  async exportOrders(format: 'excel' | 'csv' | 'pdf', startDate?: string, endDate?: string, filters?: Record<string, any>) {
    return orderBIApi.exportData({
      exportType: format,
      dataType: 'orders',
      startDate,
      endDate,
      filters,
      fields: ['id', 'referenceNo', 'status', 'createTime', 'buyerAmount', 'buyerCurrency']
    })
  },

  // 导出分析报告
  async exportAnalytics(format: 'excel' | 'csv' | 'pdf', startDate?: string, endDate?: string) {
    return orderBIApi.exportData({
      exportType: format,
      dataType: 'analytics',
      startDate,
      endDate
    })
  },

  // 导出收入报告
  async exportRevenue(format: 'excel' | 'csv' | 'pdf', startDate?: string, endDate?: string) {
    return orderBIApi.exportData({
      exportType: format,
      dataType: 'revenue',
      startDate,
      endDate
    })
  }
}

export default {
  ...orderBIApi,
  ...biApi,
  export: exportApi
}