<template>
  <el-dialog
    v-model="dialogVisible"
    title="日志详情"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="logDetail" class="log-detail">
      <!-- 基础信息 -->
      <el-card class="info-card">
        <template #header>
          <span>基础信息</span>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="日志ID">
            {{ logDetail.logId }}
          </el-descriptions-item>
          <el-descriptions-item label="会话ID">
            {{ logDetail.sessionId }}
          </el-descriptions-item>
          <el-descriptions-item label="时间戳">
            {{ formatTime(logDetail.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ logDetail.userId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="实体ID">
            {{ logDetail.entityId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="业务ID">
            {{ logDetail.bizId || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="入口API">
            {{ logDetail.apiIn?.path || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="出口API">
            {{ logDetail.apiOut?.path || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            {{ logDetail.apiOut?.supplier || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="总耗时">
            {{ logDetail.costTime }}ms
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 状态信息 -->
      <el-card class="status-card">
        <template #header>
          <span>状态信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="HTTP状态码">
            <el-tag :type="getStatusType(logDetail.output?.httpStatusCode)">
              {{ logDetail.output?.httpStatusCode || '-' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="日志状态">
            <el-tag :type="getLogStatusType(logDetail.status)">
              {{ getLogStatusText(logDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="业务错误码" v-if="logDetail.bizError">
            <el-tag type="danger">
              {{ logDetail.bizError.code }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="错误信息" v-if="logDetail.bizError">
            <span class="error-message">{{ logDetail.bizError.message }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 请求/响应数据 -->
      <el-card class="data-card">
        <template #header>
          <div class="data-header">
            <span>请求/响应数据</span>
            <el-radio-group v-model="activeTab" size="small">
              <el-radio-button label="request">请求</el-radio-button>
              <el-radio-button label="response">响应</el-radio-button>
              <el-radio-button label="compare">对比</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        
        <div class="data-content">
          <!-- 请求数据 -->
          <div v-show="activeTab === 'request'" class="request-data">
            <el-tabs v-model="requestActiveTab" type="border-card">
              <el-tab-pane label="请求头" name="headers">
                <MonacoEditor
                  v-if="logDetail.input?.header"
                  :value="JSON.stringify(logDetail.input.header, null, 2)"
                  language="json"
                  :read-only="true"
                  height="300px"
                />
                <el-empty v-else description="无请求头数据" />
              </el-tab-pane>
              
              <el-tab-pane label="请求体" name="body">
                <MonacoEditor
                  v-if="logDetail.input?.body"
                  :value="formatJsonString(logDetail.input.body)"
                  :language="getLanguageByContent(logDetail.input.body)"
                  :read-only="true"
                  height="400px"
                />
                <el-empty v-else description="无请求体数据" />
              </el-tab-pane>
              
              <el-tab-pane label="认证信息" name="credential">
                <MonacoEditor
                  v-if="logDetail.input?.credential"
                  :value="JSON.stringify(logDetail.input.credential, null, 2)"
                  language="json"
                  :read-only="true"
                  height="300px"
                />
                <el-empty v-else description="无认证信息" />
              </el-tab-pane>
            </el-tabs>
          </div>
          
          <!-- 响应数据 -->
          <div v-show="activeTab === 'response'" class="response-data">
            <el-tabs v-model="responseActiveTab" type="border-card">
              <el-tab-pane label="响应头" name="headers">
                <MonacoEditor
                  v-if="logDetail.output?.header"
                  :value="JSON.stringify(logDetail.output.header, null, 2)"
                  language="json"
                  :read-only="true"
                  height="300px"
                />
                <el-empty v-else description="无响应头数据" />
              </el-tab-pane>
              
              <el-tab-pane label="响应体" name="body">
                <MonacoEditor
                  v-if="logDetail.output?.body"
                  :value="formatJsonString(logDetail.output.body)"
                  :language="getLanguageByContent(logDetail.output.body)"
                  :read-only="true"
                  height="400px"
                />
                <el-empty v-else description="无响应体数据" />
              </el-tab-pane>
              
              <el-tab-pane label="性能信息" name="performance">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="响应耗时">
                    {{ logDetail.output?.costTime || 0 }}ms
                  </el-descriptions-item>
                  <el-descriptions-item label="内部耗时">
                    {{ logDetail.output?.internalCostTime || 0 }}ms
                  </el-descriptions-item>
                  <el-descriptions-item label="请求体大小">
                    {{ formatBytes(logDetail.inputBodySize) }}
                  </el-descriptions-item>
                  <el-descriptions-item label="响应体大小">
                    {{ formatBytes(logDetail.outputBodySize) }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-tab-pane>
            </el-tabs>
          </div>
          
          <!-- 对比视图 -->
          <div v-show="activeTab === 'compare'" class="compare-data">
            <el-row :gutter="20">
              <el-col :span="12">
                <h4>请求数据</h4>
                <MonacoEditor
                  :value="getRequestData()"
                  language="json"
                  :read-only="true"
                  height="500px"
                />
              </el-col>
              <el-col :span="12">
                <h4>响应数据</h4>
                <MonacoEditor
                  :value="getResponseData()"
                  language="json"
                  :read-only="true"
                  height="500px"
                />
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>
      
      <!-- 自定义字段 -->
      <el-card v-if="logDetail.kvs && Object.keys(logDetail.kvs).length > 0" class="kvs-card">
        <template #header>
          <span>自定义字段</span>
        </template>
        <MonacoEditor
          :value="JSON.stringify(logDetail.kvs, null, 2)"
          language="json"
          :read-only="true"
          height="200px"
        />
      </el-card>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleCopyLogId">复制日志ID</el-button>
        <el-button type="success" @click="handleExportSingle">导出此日志</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import MonacoEditor from '@/components/MonacoEditor/index.vue'
import { logViewerApi } from '@/api/logViewer'
import type { LogDetail } from '@/api/logViewer/types'

// Props
interface Props {
  visible: boolean
  logId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const logDetail = ref<LogDetail | null>(null)
const activeTab = ref('request')
const requestActiveTab = ref('headers')
const responseActiveTab = ref('headers')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听器
watch(() => props.logId, (newLogId) => {
  if (newLogId && props.visible) {
    loadLogDetail()
  }
})

watch(() => props.visible, (visible) => {
  if (visible && props.logId) {
    loadLogDetail()
  }
})

// 方法
const loadLogDetail = async () => {
  if (!props.logId) return
  
  try {
    loading.value = true
    const response = await logViewerApi.getLogDetail({ logId: props.logId })
    logDetail.value = response.log
  } catch (error) {
    console.error('加载日志详情失败:', error)
    ElMessage.error('加载日志详情失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  logDetail.value = null
}

const handleCopyLogId = async () => {
  if (!props.logId) return
  
  try {
    await navigator.clipboard.writeText(props.logId)
    ElMessage.success('日志ID已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleExportSingle = () => {
  // TODO: 实现单个日志导出
  ElMessage.info('单个日志导出功能开发中')
}

// 工具方法
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatJsonString = (str: string) => {
  try {
    const parsed = JSON.parse(str)
    return JSON.stringify(parsed, null, 2)
  } catch {
    return str
  }
}

const getLanguageByContent = (content: string) => {
  try {
    JSON.parse(content)
    return 'json'
  } catch {
    if (content.includes('<') && content.includes('>')) {
      return 'xml'
    }
    return 'text'
  }
}

const getRequestData = () => {
  if (!logDetail.value?.input) return '{}'
  return JSON.stringify({
    headers: logDetail.value.input.header || {},
    body: logDetail.value.input.body ? formatJsonString(logDetail.value.input.body) : '',
    credential: logDetail.value.input.credential || {}
  }, null, 2)
}

const getResponseData = () => {
  if (!logDetail.value?.output) return '{}'
  return JSON.stringify({
    headers: logDetail.value.output.header || {},
    body: logDetail.value.output.body ? formatJsonString(logDetail.value.output.body) : '',
    statusCode: logDetail.value.output.httpStatusCode,
    costTime: logDetail.value.output.costTime
  }, null, 2)
}

const getStatusType = (statusCode: number | undefined) => {
  if (!statusCode) return 'info'
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400) return 'danger'
  return 'info'
}

const getLogStatusType = (status: string) => {
  switch (status) {
    case 'success': return 'success'
    case 'warning': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getLogStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'warning': return '警告'
    case 'error': return '错误'
    default: return '未知'
  }
}
</script>

<style scoped lang="scss">
.loading-container {
  padding: 20px;
}

.log-detail {
  .info-card,
  .status-card,
  .data-card,
  .kvs-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .error-message {
    color: var(--el-color-danger);
    font-weight: 500;
  }
  
  .compare-data {
    h4 {
      margin-bottom: 10px;
      color: var(--el-text-color-primary);
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
