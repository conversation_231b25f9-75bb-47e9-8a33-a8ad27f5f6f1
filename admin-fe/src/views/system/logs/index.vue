<template>
  <div class="logs-viewer">
    <!-- 统计概览区域 -->
    <div class="statistics-section" v-if="showStatistics">
      <LogStatistics :logs="logs" :loading="loading" :time-range="timeRange" />
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <template #header>
          <div class="card-header">
            <span>日志查询</span>
            <div>
              <el-button @click="toggleStatistics">
                {{ showStatistics ? '隐藏统计' : '显示统计' }}
              </el-button>
              <el-button type="primary" @click="toggleAdvancedSearch">
                {{ showAdvancedSearch ? '简单搜索' : '高级搜索' }}
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 基础搜索 -->
        <el-form :model="searchForm" :inline="true" class="search-form">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
              @change="handleTimeRangeChange"
            />
          </el-form-item>
          
          <el-form-item label="日志ID">
            <el-input
              v-model="searchForm.logId"
              placeholder="请输入日志ID"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="会话ID">
            <el-input
              v-model="searchForm.sessionId"
              placeholder="请输入会话ID"
              clearable
            />
          </el-form-item>
          
          <el-form-item label="供应商">
            <el-select
              v-model="searchForm.supplier"
              placeholder="请选择供应商"
              clearable
            >
              <el-option label="Dida" value="dida" />
              <el-option label="Ctrip" value="ctrip" />
              <el-option label="Expedia" value="expedia" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleExport">导出</el-button>
          </el-form-item>
        </el-form>
        
        <!-- 高级搜索 -->
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-divider content-position="left">高级搜索</el-divider>
          <el-form :model="searchForm" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="用户ID">
                  <el-input v-model="searchForm.userId" placeholder="请输入用户ID" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="API路径">
                  <el-input v-model="searchForm.apiPath" placeholder="请输入API路径" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="实体ID">
                  <el-input v-model="searchForm.entityId" placeholder="请输入实体ID" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="业务ID">
                  <el-input v-model="searchForm.bizId" placeholder="请输入业务ID" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="错误码">
                  <el-input v-model="searchForm.errorCode" placeholder="请输入错误码" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="耗时范围(ms)">
                  <el-input-number
                    v-model="costTimeMin"
                    :min="0"
                    placeholder="最小耗时"
                    style="width: 45%"
                  />
                  <span style="margin: 0 8px">-</span>
                  <el-input-number
                    v-model="costTimeMax"
                    :min="0"
                    placeholder="最大耗时"
                    style="width: 45%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="输入内容关键词">
                  <el-input v-model="searchForm.inputContentKeyword" placeholder="请输入输入内容关键词" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="输出内容关键词">
                  <el-input v-model="searchForm.outputContentKeyword" placeholder="请输入输出内容关键词" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-card>
    </div>
    
    <!-- 日志列表区域 -->
    <div class="table-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>日志列表 (共 {{ total }} 条)</span>
            <div>
              <el-button @click="handleRefresh" :loading="loading">刷新</el-button>
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                @change="handleAutoRefreshChange"
              />
            </div>
          </div>
        </template>
        
        <el-table
          :data="logs"
          :loading="loading"
          stripe
          border
          height="600"
          @row-click="handleRowClick"
          row-class-name="log-row"
        >
          <el-table-column prop="timestamp" label="时间" width="180" sortable>
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="logId" label="日志ID" width="120" show-overflow-tooltip />
          
          <el-table-column prop="sessionId" label="会话ID" width="120" show-overflow-tooltip />
          
          <el-table-column label="入口API" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.apiIn?.path || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="供应商" width="100">
            <template #default="{ row }">
              {{ row.apiOut?.supplier || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="出口API" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.apiOut?.path || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="状态码" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.output?.httpStatusCode)">
                {{ row.output?.httpStatusCode || '-' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="costTime" label="耗时(ms)" width="100" align="right" sortable />
          
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getLogStatusType(row.status)">
                {{ getLogStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="请求预览" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              {{ getInputPreview(row) }}
            </template>
          </el-table-column>
          
          <el-table-column label="响应预览" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              {{ getOutputPreview(row) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click.stop="viewDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 日志详情弹窗 -->
    <LogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-id="selectedLogId"
      @refresh="handleRefresh"
    />
    
    <!-- 导出弹窗 -->
    <ExportDialog
      v-model:visible="exportDialogVisible"
      :search-params="searchForm"
      @export-success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import LogDetailDialog from './components/LogDetailDialog.vue'
import ExportDialog from './components/ExportDialog.vue'
import LogStatistics from './components/LogStatistics.vue'
import { logViewerApi } from '@/api/logViewer'
import type { LogItem, QueryLogsParams } from '@/api/logViewer/types'

// 响应式数据
const loading = ref(false)
const logs = ref<LogItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const showAdvancedSearch = ref(false)
const showStatistics = ref(false)
const autoRefresh = ref(false)
const detailDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const selectedLogId = ref('')

// 时间范围
const timeRange = ref<[string, string]>(['', ''])

// 搜索表单
const searchForm = reactive<QueryLogsParams>({
  page: 1,
  pageSize: 20,
  startTime: '',
  endTime: '',
  logId: '',
  sessionId: '',
  userId: undefined,
  entityId: '',
  bizId: '',
  apiPath: '',
  supplier: '',
  errorCode: '',
  inputContentKeyword: '',
  outputContentKeyword: ''
})

// 耗时范围
const costTimeMin = ref<number>()
const costTimeMax = ref<number>()

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  // 设置默认时间范围为最近1小时
  const now = new Date()
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
  const defaultTimeRange: [string, string] = [
    oneHourAgo.toISOString(),
    now.toISOString()
  ]
  timeRange.value = defaultTimeRange
  handleTimeRangeChange(defaultTimeRange)
  
  // 初始加载数据
  loadLogs()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 方法
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

const toggleStatistics = () => {
  showStatistics.value = !showStatistics.value
}

const handleTimeRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

const handleSearch = () => {
  currentPage.value = 1
  searchForm.page = 1
  loadLogs()
}

const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    pageSize: 20,
    startTime: '',
    endTime: '',
    logId: '',
    sessionId: '',
    userId: undefined,
    entityId: '',
    bizId: '',
    apiPath: '',
    supplier: '',
    errorCode: '',
    inputContentKeyword: '',
    outputContentKeyword: ''
  })
  timeRange.value = ['', '']
  costTimeMin.value = undefined
  costTimeMax.value = undefined
  currentPage.value = 1
  loadLogs()
}

const handleRefresh = () => {
  loadLogs()
}

const handleAutoRefreshChange = (val: string | number | boolean) => {
  const value = Boolean(val)
  if (value) {
    refreshTimer = setInterval(() => {
      loadLogs()
    }, 10000) // 10秒刷新一次
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchForm.pageSize = size
  currentPage.value = 1
  searchForm.page = 1
  loadLogs()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  searchForm.page = page
  loadLogs()
}

const handleRowClick = (row: LogItem) => {
  viewDetail(row)
}

const viewDetail = (row: LogItem) => {
  selectedLogId.value = row.logId
  detailDialogVisible.value = true
}

const handleExport = () => {
  exportDialogVisible.value = true
}

const handleExportSuccess = () => {
  ElMessage.success('导出成功')
}

// 加载日志数据
const loadLogs = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = { ...searchForm }
    
    // 处理耗时范围
    if (costTimeMin.value !== undefined) {
      params.costTimeMin = costTimeMin.value
    }
    if (costTimeMax.value !== undefined) {
      params.costTimeMax = costTimeMax.value
    }
    
    const response = await logViewerApi.queryLogs(params)
    logs.value = response.logs
    total.value = response.total
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('加载日志失败')
  } finally {
    loading.value = false
  }
}

// 工具方法
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getStatusType = (statusCode: number | undefined) => {
  if (!statusCode) return 'info'
  if (statusCode >= 200 && statusCode < 300) return 'success'
  if (statusCode >= 300 && statusCode < 400) return 'warning'
  if (statusCode >= 400) return 'danger'
  return 'info'
}

const getInputPreview = (row: LogItem) => {
  if (row.input?.body) {
    const body = typeof row.input.body === 'string' ? row.input.body : JSON.stringify(row.input.body)
    return body.length > 100 ? body.substring(0, 100) + '...' : body
  }
  return '-'
}

const getOutputPreview = (row: LogItem) => {
  if (row.output?.body) {
    const body = typeof row.output.body === 'string' ? row.output.body : JSON.stringify(row.output.body)
    return body.length > 100 ? body.substring(0, 100) + '...' : body
  }
  return '-'
}

const getLogStatusType = (status: string) => {
  switch (status) {
    case 'success': return 'success'
    case 'warning': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

const getLogStatusText = (status: string) => {
  switch (status) {
    case 'success': return '成功'
    case 'warning': return '警告'
    case 'error': return '错误'
    default: return '未知'
  }
}
</script>

<style scoped lang="scss">
.logs-viewer {
  padding: 20px;

  .statistics-section {
    margin-bottom: 20px;
  }

  .search-section {
    margin-bottom: 20px;
    
    .search-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .search-form {
        margin-bottom: 0;
      }
      
      .advanced-search {
        margin-top: 20px;
      }
    }
  }
  
  .table-section {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      text-align: right;
    }
  }
}

:deep(.log-row) {
  cursor: pointer;
  
  &:hover {
    background-color: var(--el-table-row-hover-bg-color);
  }
}
</style>
