<template>
  <div class="login">
    <LoginLeftView></LoginLeftView>

    <div class="right-wrap">
      <div class="top-right-wrap">
        <div class="btn theme-btn" @click="toggleTheme">
          <i class="iconfont-sys">
            {{ isDark ? '&#xe6b5;' : '&#xe725;' }}
          </i>
        </div>
        <ElDropdown @command="changeLanguage" popper-class="langDropDownStyle">
          <div class="btn language-btn">
            <i class="iconfont-sys icon-language">&#xe611;</i>
          </div>
          <template #dropdown>
            <ElDropdownMenu>
              <div v-for="lang in languageOptions" :key="lang.value" class="lang-btn-item">
                <ElDropdownItem
                  :command="lang.value"
                  :class="{ 'is-selected': locale === lang.value }"
                >
                  <span class="menu-txt">{{ lang.label }}</span>
                  <i v-if="locale === lang.value" class="iconfont-sys icon-check">&#xe621;</i>
                </ElDropdownItem>
              </div>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
      <div class="header">
        <ArtLogo class="icon" />
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('login.title') }}</h3>
          <p class="sub-title">{{ $t('login.subTitle') }}</p>
          <ElForm
            ref="formRef"
            :model="formData"
            :rules="rules"
            @keyup.enter="handleSubmit"
            style="margin-top: 25px"
          >
            <ElFormItem prop="email">
              <ElInput :placeholder="$t('login.placeholder[0]')" v-model.trim="formData.email" />
            </ElFormItem>
            <ElFormItem prop="password">
              <ElInput
                :placeholder="$t('login.placeholder[1]')"
                v-model.trim="formData.password"
                type="password"
                radius="8px"
                autocomplete="off"
                show-password
              />
            </ElFormItem>
            <div v-if="!disableSlider" class="drag-verify">
              <div class="drag-verify-content" :class="{ error: !isPassing && isClickPass }">
                <ArtDragVerify
                  ref="dragVerify"
                  v-model:value="isPassing"
                  :text="$t('login.sliderText')"
                  textColor="var(--art-gray-800)"
                  :successText="$t('login.sliderSuccessText')"
                  :progressBarBg="getCssVar('--el-color-primary')"
                  background="var(--art-gray-200)"
                  handlerBg="var(--art-main-bg-color)"
                />
              </div>
              <p class="error-text" :class="{ 'show-error-text': !isPassing && isClickPass }">{{
                $t('login.placeholder[2]')
              }}</p>
            </div>

            <div class="forget-password">
              <ElCheckbox v-model="formData.rememberPassword">{{
                $t('login.rememberPwd')
              }}</ElCheckbox>
              <RouterLink :to="RoutesAlias.ForgetPassword">{{ $t('login.forgetPwd') }}</RouterLink>
            </div>

            <div style="margin-top: 30px">
              <ElButton
                class="login-btn"
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                v-ripple
              >
                {{ $t('login.btnText') }}
              </ElButton>
            </div>

            <div class="footer">
              <p>
                {{ $t('login.noAccount') }}
                <RouterLink :to="RoutesAlias.Register">{{ $t('login.register') }}</RouterLink>
              </p>
            </div>
          </ElForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
  import { RoutesAlias } from '@/router/routesAlias'
  import { ElNotification, ElMessage } from 'element-plus'
  import { useUserStore } from '@/store/modules/user'
  import { getCssVar } from '@/utils/ui'
  import { languageOptions } from '@/locales'
  import { LanguageEnum, SystemThemeEnum } from '@/enums/appEnum'
  import { useI18n } from 'vue-i18n'
  import { HttpError } from '@/utils/http/error'

  defineOptions({ name: 'Login' })

  const { t } = useI18n()
  import { useSettingStore } from '@/store/modules/setting'
  import type { FormInstance, FormRules } from 'element-plus'

  type AccountKey = 'super' | 'admin' | 'user'

  export interface Account {
    key: AccountKey
    label: string
    userName: string
    password: string
    roles: string[]
  }

  // 从环境变量读取测试账号信息（仅开发环境使用）
  const accounts = computed<Account[]>(() => [
    {
      key: 'super',
      label: t('login.roles.super'),
      userName: import.meta.env.VITE_TEST_SUPER_EMAIL || '<EMAIL>',
      password: import.meta.env.VITE_TEST_SUPER_PASSWORD || 'super-password',
      roles: ['R_SUPER']
    },
    {
      key: 'admin',
      label: t('login.roles.admin'),
      userName: import.meta.env.VITE_TEST_ADMIN_EMAIL || '<EMAIL>',
      password: import.meta.env.VITE_TEST_ADMIN_PASSWORD || 'admin-password',
      roles: ['R_ADMIN']
    },
    {
      key: 'user',
      label: t('login.roles.user'),
      userName: import.meta.env.VITE_TEST_USER_EMAIL || '<EMAIL>',
      password: import.meta.env.VITE_TEST_USER_PASSWORD || 'user-password',
      roles: ['R_USER']
    }
  ])

  const settingStore = useSettingStore()
  const { isDark, systemThemeType } = storeToRefs(settingStore)

  const dragVerify = ref()

  const userStore = useUserStore()
  const router = useRouter()
  const isPassing = ref(false)
  const isClickPass = ref(false)

  const systemName = AppConfig.systemInfo.name
  const formRef = ref<FormInstance>()

  const formData = reactive({
    email: '',
    password: '',
    ttl: undefined,
    rememberPassword: true
  })

  const rules = computed<FormRules>(() => ({
    email: [{ required: true, message: t('login.placeholder[0]'), trigger: 'blur' }],
    password: [{ required: true, message: t('login.placeholder[1]'), trigger: 'blur' }]
  }))

  const loading = ref(false)

  const disableSlider = import.meta.env.VITE_DISABLE_SLIDER === 'true'
  if (disableSlider) isPassing.value = true

  onMounted(() => {
    // 仅在非 E2E 环境下自动填充账号，E2E 测试时不覆盖 Cypress 输入
    if (import.meta.env.MODE !== 'e2e') {
      setupAccount('super')
    }
  })

  // 设置账号
  const setupAccount = (key: AccountKey) => {
    const selectedAccount = accounts.value.find((account: Account) => account.key === key)
    formData.email = selectedAccount?.userName ?? ''
    formData.password = selectedAccount?.password ?? ''
  }

  // 登录
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      // 表单验证
      const valid = await formRef.value.validate()
      if (!valid) return

      // 拖拽验证
      if (!isPassing.value) {
        isClickPass.value = true
        return
      }

      loading.value = true

      // 登录请求
      const { email, password, ttl } = formData
      const loginRes = await UserService.login({
        email,
        password,
        ttl
      })

      // 验证token
      if (!loginRes.token) {
        throw new Error('Login failed - no token received')
      }

      // 存储token和用户信息
      userStore.setToken(loginRes.token)
      userStore.setLoginStatus(true)
      // 兼容后端未返回user的情况，自动拉取用户信息
      let patchedUser = null;
      if (loginRes.user) {
        // 结构兼容：将email映射，userName保持一致
        patchedUser = {
          ...loginRes.user,
          email: loginRes.user.email || '',
          userName: loginRes.user.userName || '',
          avatar: loginRes.user.profile?.avatar || '',
          phone: loginRes.user.profile?.mobile || '',
        };
        console.log('[Login] 原始user:', loginRes.user)
        console.log('[Login] 结构兼容后user:', patchedUser)
      }
      if (patchedUser && patchedUser.email) {
        userStore.setUserInfo(patchedUser)
        console.log('[Login] setUserInfo', patchedUser)
      } else {
        // 自动拉取
        try {
          const userInfo = await UserService.getUserInfo()
          console.log('[Login] fetch userInfo', userInfo)
          userStore.setUserInfo(userInfo)
        } catch (e) {
          console.error('[Login] fetch userInfo failed', e)
        }
      }
      // nextTick 确保响应式刷新
      await nextTick()
      // 手动patch: 强制写入localStorage，兼容E2E
      try {
        localStorage.setItem('pinia-userStore', JSON.stringify(userStore.$state))
        console.log('[Login] patch localStorage pinia-userStore', userStore.$state)
        // E2E专用token写入
        if (import.meta.env.MODE === 'e2e') {
          localStorage.setItem('e2e_token', loginRes.token)
          console.log('[Login] patch localStorage e2e_token', loginRes.token)
          // 新增：E2E专用事件通知
          window.dispatchEvent(new CustomEvent('e2e_token', { detail: loginRes.token }))
        }
      } catch (e) {
        console.error('[Login] patch localStorage失败', e)
      }
      // 轮询等待 accessToken 存在再跳转
      await new Promise((resolve, reject) => {
        const start = Date.now();
        function check() {
          const userStoreRaw = localStorage.getItem('pinia-userStore');
          if (userStoreRaw) {
            const accessToken = JSON.parse(userStoreRaw).accessToken;
            if (accessToken && typeof accessToken === 'string' && accessToken.length > 0) {
              return resolve(null);
            }
          }
          if (Date.now() - start > 3000) {
            return reject(new Error('accessToken not found in localStorage after 3s'));
          }
          setTimeout(check, 100);
        }
        check();
      });
      // 登录成功处理
      showLoginSuccessNotice()
      router.push('/')
    } catch (error) {
      // 处理 HttpError
      if (error instanceof HttpError) {
        console.error('[Login] HttpError:', error, error.message, error.code, error.data)
        ElMessage.error(error.message || '登录失败，请稍后重试')
      } else {
        // 处理非 HttpError
        console.error('[Login] Unexpected error:', error)
        ElMessage.error('登录失败，请稍后重试')
      }
    } finally {
      loading.value = false
      // 滑块reset安全调用
      if (dragVerify.value) dragVerify.value.reset()
    }
  }

  // 登录成功提示
  const showLoginSuccessNotice = () => {
    setTimeout(() => {
      const userInfo = userStore.getUserInfo
      const userName = userInfo?.userName || ''
      const email = userInfo?.email || ''

      let welcomeMessage = t('login.success.message')
      if (userName && email) {
        welcomeMessage = `${t('login.success.message')}，${userName}(${email})`
      } else if (userName) {
        welcomeMessage = `${t('login.success.message')}，${userName}`
      } else if (email) {
        welcomeMessage = `${t('login.success.message')}，${email}`
      }

      ElNotification({
        title: t('login.success.title'),
        type: 'success',
        duration: 2500,
        zIndex: 10000,
        message: welcomeMessage
      })
    }, 150)
  }

  // 切换语言
  const { locale } = useI18n()

  const changeLanguage = (lang: LanguageEnum) => {
    if (locale.value === lang) return
    locale.value = lang
    userStore.setLanguage(lang)
  }

  // 切换主题
  import { useTheme } from '@/composables/useTheme'
  import { UserService } from '@/api/usersApi'

  const toggleTheme = () => {
    let { LIGHT, DARK } = SystemThemeEnum
    useTheme().switchThemeStyles(systemThemeType.value === LIGHT ? DARK : LIGHT)
  }
</script>

<style lang="scss" scoped>
  @use './index';
</style>
