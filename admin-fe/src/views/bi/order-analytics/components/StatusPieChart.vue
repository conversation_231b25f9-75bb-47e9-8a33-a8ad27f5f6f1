<template>
  <div class="status-pie-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
    </div>
    <div ref="chartContainer" :style="{ height: height + 'px' }"></div>
    <div class="status-legend">
      <div 
        v-for="(item, index) in data" 
        :key="item.status"
        class="legend-item"
      >
        <div 
          class="legend-color" 
          :style="{ backgroundColor: getStatusColor(index) }"
        ></div>
        <span class="legend-label">{{ item.statusName }}</span>
        <span class="legend-value">{{ item.count }} ({{ item.percentage.toFixed(1) }}%)</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, PropType } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

interface StatusDataItem {
  status: number
  statusName: string
  count: number
  percentage: number
  revenue?: { amount: number; currency: string }
}

const props = defineProps({
  title: {
    type: String,
    default: '订单状态分布'
  },
  height: {
    type: Number,
    default: 300
  },
  data: {
    type: Array as PropType<StatusDataItem[]>,
    default: () => []
  }
})

const chartContainer = ref<HTMLElement>()
let chart: ECharts | null = null

// 状态颜色配置
const statusColors = [
  '#409EFF', // 蓝色
  '#67C23A', // 绿色  
  '#E6A23C', // 橙色
  '#F56C6C', // 红色
  '#909399', // 灰色
  '#9C27B0', // 紫色
  '#FF9800', // 深橙色
  '#795548'  // 棕色
]

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

const initChart = () => {
  if (!chartContainer.value) return
  
  chart = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chart || !props.data.length) return
  
  const chartData = props.data.map((item, index) => ({
    name: item.statusName,
    value: item.count,
    itemStyle: {
      color: statusColors[index % statusColors.length]
    }
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const dataItem = props.data.find(item => item.statusName === params.name)
        let tooltip = `${params.name}<br/>`
        tooltip += `订单数: ${params.value} (${params.percent}%)`
        
        if (dataItem?.revenue) {
          const revenue = dataItem.revenue
          tooltip += `<br/>收入: $${revenue.amount.toLocaleString()} ${revenue.currency}`
        }
        
        return tooltip
      }
    },
    legend: {
      show: false
    },
    series: [{
      type: 'pie',
      radius: ['40%', '80%'],
      center: ['50%', '50%'],
      data: chartData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}: {c}',
        fontSize: 12,
        color: '#606266'
      },
      labelLine: {
        show: true,
        length: 10,
        length2: 20,
        lineStyle: {
          color: '#E4E7ED'
        }
      }
    }]
  }
  
  chart.setOption(option, true)
}

const handleResize = () => {
  chart?.resize()
}

const getStatusColor = (index: number) => {
  return statusColors[index % statusColors.length]
}
</script>

<style lang="scss" scoped>
.status-pie-chart {
  .chart-header {
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .status-legend {
    margin-top: 16px;
    
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
      
      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        margin-right: 8px;
        flex-shrink: 0;
      }
      
      .legend-label {
        color: #303133;
        margin-right: 8px;
        min-width: 60px;
      }
      
      .legend-value {
        color: #909399;
        margin-left: auto;
      }
    }
  }
}
</style>