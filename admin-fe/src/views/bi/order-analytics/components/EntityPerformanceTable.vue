<template>
  <div class="entity-performance-table">
    <div class="table-header">
      <h3>{{ title }}</h3>
      <div class="table-controls">
        <el-input
          v-model="searchText"
          placeholder="搜索实体"
          prefix-icon="Search"
          size="small"
          style="width: 200px"
          clearable
        />
        <el-select
          v-model="sortBy"
          placeholder="排序方式"
          size="small"
          style="width: 120px; margin-left: 12px"
          @change="sortData"
        >
          <el-option label="收入" value="revenue" />
          <el-option label="利润" value="profit" />
          <el-option label="订单数" value="orderCount" />
          <el-option label="利润率" value="profitMargin" />
        </el-select>
      </div>
    </div>
    
    <el-table
      :data="filteredData"
      :height="height"
      stripe
      highlight-current-row
      @sort-change="handleSortChange"
    >
      <el-table-column
        prop="entityName"
        label="实体名称"
        min-width="120"
        show-overflow-tooltip
      >
        <template #default="{ row, $index }">
          <div class="entity-cell">
            <div class="entity-rank">{{ $index + 1 }}</div>
            <div class="entity-info">
              <div class="entity-name">{{ getEntityName(row.entityName) }}</div>
              <div class="entity-id">ID: {{ row.entityId }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="orderCount"
        label="订单数"
        width="100"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-tag size="small">{{ formatNumber(row.orderCount) }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="revenue"
        label="收入"
        width="120"
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="money-cell">
            <span class="money-amount">{{ formatMoney(row.totalRevenue) }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="profit"
        label="利润"
        width="120"
        sortable="custom"
      >
        <template #default="{ row }">
          <div class="money-cell">
            <span class="money-amount profit">{{ formatMoney(row.totalProfit) }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="profitMargin"
        label="利润率"
        width="100"
        sortable="custom"
      >
        <template #default="{ row }">
          <el-progress
            :percentage="Math.min(row.profitMargin, 100)"
            :color="getProgressColor(row.profitMargin)"
            :show-text="false"
            style="margin-bottom: 4px"
          />
          <div class="percentage-text" :class="getProfitMarginClass(row.profitMargin)">
            {{ row.profitMargin.toFixed(1) }}%
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="growthRate"
        label="增长率"
        width="100"
        v-if="showGrowthRate"
      >
        <template #default="{ row }">
          <div class="growth-cell" :class="getGrowthClass(row.growthRate)">
            <el-icon v-if="row.growthRate > 0"><ArrowUp /></el-icon>
            <el-icon v-else-if="row.growthRate < 0"><ArrowDown /></el-icon>
            <span>{{ Math.abs(row.growthRate || 0).toFixed(1) }}%</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            text
            @click="viewDetails(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, PropType } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

interface EntityPerformanceItem {
  entityId: number
  entityName: string | { [key: string]: string }
  totalRevenue: { amount: number; currency: string }
  totalProfit: { amount: number; currency: string }
  orderCount: number
  profitMargin: number
  growthRate?: number
}

const props = defineProps({
  title: {
    type: String,
    default: '实体表现排行'
  },
  height: {
    type: Number,
    default: 400
  },
  data: {
    type: Array as PropType<EntityPerformanceItem[]>,
    default: () => []
  },
  showGrowthRate: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view-details'])

const searchText = ref('')
const sortBy = ref('revenue')
const sortOrder = ref<'ascending' | 'descending'>('descending')

const filteredData = computed(() => {
  let result = props.data.filter(item => {
    if (!searchText.value) return true
    const entityName = getEntityName(item.entityName).toLowerCase()
    return entityName.includes(searchText.value.toLowerCase()) ||
           item.entityId.toString().includes(searchText.value)
  })
  
  // 排序
  result.sort((a, b) => {
    let valueA: number, valueB: number
    
    switch (sortBy.value) {
      case 'revenue':
        valueA = a.totalRevenue.amount
        valueB = b.totalRevenue.amount
        break
      case 'profit':
        valueA = a.totalProfit.amount
        valueB = b.totalProfit.amount
        break
      case 'orderCount':
        valueA = a.orderCount
        valueB = b.orderCount
        break
      case 'profitMargin':
        valueA = a.profitMargin
        valueB = b.profitMargin
        break
      default:
        valueA = a.totalRevenue.amount
        valueB = b.totalRevenue.amount
    }
    
    return sortOrder.value === 'ascending' ? valueA - valueB : valueB - valueA
  })
  
  return result
})

const getEntityName = (entityName: string | { [key: string]: string }) => {
  if (typeof entityName === 'string') {
    return entityName
  }
  return entityName['zh-CN'] || entityName['en-US'] || Object.values(entityName)[0] || ''
}

const formatNumber = (num: number) => {
  return new Intl.NumberFormat().format(num)
}

const formatMoney = (money: { amount: number; currency: string }) => {
  if (!money) return '0'
  
  const { amount, currency } = money
  
  if (amount >= 1000000) {
    return `${currency} ${(amount / 1000000).toFixed(1)}M`
  } else if (amount >= 1000) {
    return `${currency} ${(amount / 1000).toFixed(1)}K`
  }
  
  return `${currency} ${amount.toLocaleString()}`
}

const getProgressColor = (percentage: number) => {
  if (percentage >= 20) return '#67C23A'
  if (percentage >= 10) return '#E6A23C'
  return '#F56C6C'
}

const getProfitMarginClass = (margin: number) => {
  if (margin >= 20) return 'high'
  if (margin >= 10) return 'medium'
  return 'low'
}

const getGrowthClass = (rate: number) => {
  if (!rate) return 'neutral'
  return rate > 0 ? 'positive' : 'negative'
}

const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  if (order) {
    sortBy.value = prop
    sortOrder.value = order as 'ascending' | 'descending'
  }
}

const sortData = () => {
  // 触发重新计算 filteredData
}

const viewDetails = (row: EntityPerformanceItem) => {
  emit('view-details', row)
}
</script>

<style lang="scss" scoped>
.entity-performance-table {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 500;
    }
    
    .table-controls {
      display: flex;
      align-items: center;
    }
  }
  
  .entity-cell {
    display: flex;
    align-items: center;
    
    .entity-rank {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #F2F6FC;
      color: #606266;
      font-size: 12px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      
      &:nth-child(1) {
        background: #FFD700;
        color: white;
      }
      
      &:nth-child(2) {
        background: #C0C0C0;
        color: white;
      }
      
      &:nth-child(3) {
        background: #CD7F32;
        color: white;
      }
    }
    
    .entity-info {
      .entity-name {
        font-weight: 500;
        color: #303133;
        margin-bottom: 2px;
      }
      
      .entity-id {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .money-cell {
    .money-amount {
      font-weight: 500;
      
      &.profit {
        color: #67C23A;
      }
    }
  }
  
  .percentage-text {
    font-size: 12px;
    text-align: center;
    
    &.high {
      color: #67C23A;
    }
    
    &.medium {
      color: #E6A23C;
    }
    
    &.low {
      color: #F56C6C;
    }
  }
  
  .growth-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    
    &.positive {
      color: #67C23A;
    }
    
    &.negative {
      color: #F56C6C;
    }
    
    &.neutral {
      color: #909399;
    }
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

:deep(.el-table) {
  .el-table__row:nth-child(1) .entity-rank {
    background: #FFD700 !important;
    color: white !important;
  }
  
  .el-table__row:nth-child(2) .entity-rank {
    background: #C0C0C0 !important;
    color: white !important;
  }
  
  .el-table__row:nth-child(3) .entity-rank {
    background: #CD7F32 !important;
    color: white !important;
  }
}
</style>