<template>
  <div class="metric-card-component">
    <div class="metric-icon" :class="iconClass">
      <component :is="icon" class="icon" />
    </div>
    <div class="metric-content">
      <div class="metric-value">
        {{ formattedValue }}
      </div>
      <div class="metric-label">{{ label }}</div>
      <div class="metric-extra">
        <div v-if="growth !== undefined" class="metric-growth" :class="getGrowthClass(growth)">
          <el-icon v-if="growth > 0"><ArrowUp /></el-icon>
          <el-icon v-else-if="growth < 0"><ArrowDown /></el-icon>
          <span>{{ Math.abs(growth).toFixed(1) }}%</span>
        </div>
        <div v-if="subtitle" class="metric-subtitle">{{ subtitle }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  value: {
    type: [Number, Object] as PropType<number | { amount: number; currency: string }>,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  icon: {
    type: [String, Object],
    required: true
  },
  iconClass: {
    type: String,
    default: ''
  },
  growth: {
    type: Number,
    default: undefined
  },
  subtitle: {
    type: String,
    default: ''
  },
  format: {
    type: String as PropType<'number' | 'currency' | 'percentage'>,
    default: 'number'
  }
})

const formattedValue = computed(() => {
  const val = props.value
  
  if (typeof val === 'object' && val.amount !== undefined) {
    // 金额对象
    return formatCurrency(val.amount, val.currency)
  }
  
  if (typeof val === 'number') {
    switch (props.format) {
      case 'currency':
        return formatCurrency(val, 'USD')
      case 'percentage':
        return `${val.toFixed(1)}%`
      case 'number':
      default:
        return formatNumber(val)
    }
  }
  
  return String(val)
})

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return new Intl.NumberFormat().format(num)
}

const formatCurrency = (amount: number, currency: string) => {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  } catch {
    return `${currency} ${formatNumber(amount)}`
  }
}

const getGrowthClass = (rate: number) => {
  return rate > 0 ? 'positive' : rate < 0 ? 'negative' : 'neutral'
}
</script>

<style lang="scss" scoped>
.metric-card-component {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #F0F0F0;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .metric-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    position: relative;
    
    .icon {
      font-size: 28px;
      color: white;
    }
    
    &.orders {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.revenue {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.profit {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.completion {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
    
    &.growth {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    &.users {
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }
  }
  
  .metric-content {
    flex: 1;
    
    .metric-value {
      font-size: 32px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 4px;
      line-height: 1.2;
    }
    
    .metric-label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .metric-extra {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .metric-growth {
        font-size: 12px;
        display: flex;
        align-items: center;
        font-weight: 500;
        
        &.positive {
          color: #67C23A;
        }
        
        &.negative {
          color: #F56C6C;
        }
        
        &.neutral {
          color: #909399;
        }
        
        .el-icon {
          margin-right: 4px;
          font-size: 14px;
        }
      }
      
      .metric-subtitle {
        font-size: 12px;
        color: #C0C4CC;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .metric-card-component {
    padding: 16px;
    
    .metric-icon {
      width: 48px;
      height: 48px;
      margin-right: 16px;
      
      .icon {
        font-size: 24px;
      }
    }
    
    .metric-content {
      .metric-value {
        font-size: 24px;
      }
    }
  }
}
</style>