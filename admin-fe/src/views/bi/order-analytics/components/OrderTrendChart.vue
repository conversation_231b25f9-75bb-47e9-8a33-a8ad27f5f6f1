<template>
  <div class="order-trend-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-radio-group v-model="currentMetric" size="small" @change="updateChart">
          <el-radio-button value="orderCount">订单数</el-radio-button>
          <el-radio-button value="revenue">收入</el-radio-button>
          <el-radio-button value="profit">利润</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div ref="chartContainer" :style="{ height: height + 'px' }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, PropType } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

interface TrendDataItem {
  date: string
  orderCount: number
  revenue: { amount: number; currency: string }
  profit: { amount: number; currency: string }
}

const props = defineProps({
  title: {
    type: String,
    default: '订单趋势'
  },
  height: {
    type: Number,
    default: 400
  },
  data: {
    type: Array as PropType<TrendDataItem[]>,
    default: () => []
  },
  metric: {
    type: String,
    default: 'orderCount'
  }
})

const emit = defineEmits(['metric-change'])

const chartContainer = ref<HTMLElement>()
const currentMetric = ref(props.metric)
let chart: ECharts | null = null

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', handleResize)
})

watch(() => props.data, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => props.metric, (newMetric) => {
  currentMetric.value = newMetric
  updateChart()
})

const initChart = () => {
  if (!chartContainer.value) return
  
  chart = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chart || !props.data.length) return
  
  const dates = props.data.map(item => item.date)
  let values: number[] = []
  let yAxisName = ''
  let seriesName = ''
  
  switch (currentMetric.value) {
    case 'orderCount':
      values = props.data.map(item => item.orderCount)
      yAxisName = '订单数'
      seriesName = '订单数量'
      break
    case 'revenue':
      values = props.data.map(item => item.revenue.amount)
      yAxisName = '收入 (USD)'
      seriesName = '收入金额'
      break
    case 'profit':
      values = props.data.map(item => item.profit.amount)
      yAxisName = '利润 (USD)'
      seriesName = '利润金额'
      break
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0]
        const dataItem = props.data[param.dataIndex]
        let value = ''
        
        switch (currentMetric.value) {
          case 'orderCount':
            value = `${param.value} 单`
            break
          case 'revenue':
            value = `$${param.value.toLocaleString()}`
            break
          case 'profit':
            value = `$${param.value.toLocaleString()}`
            break
        }
        
        return `${param.axisValue}<br/>${seriesName}: ${value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameTextStyle: {
        color: '#606266'
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisLabel: {
        color: '#606266',
        formatter: (value: number) => {
          if (currentMetric.value === 'orderCount') {
            return value.toString()
          }
          return `$${(value / 1000).toFixed(0)}K`
        }
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC'
        }
      }
    },
    series: [{
      name: seriesName,
      type: 'line',
      smooth: true,
      data: values,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1,
            color: 'rgba(64, 158, 255, 0.05)'
          }]
        }
      },
      lineStyle: {
        color: '#409EFF',
        width: 3
      },
      itemStyle: {
        color: '#409EFF',
        borderColor: '#FFF',
        borderWidth: 2
      },
      emphasis: {
        itemStyle: {
          color: '#409EFF',
          borderColor: '#FFF',
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor: 'rgba(64, 158, 255, 0.3)'
        }
      }
    }]
  }
  
  chart.setOption(option, true)
  emit('metric-change', currentMetric.value)
}

const handleResize = () => {
  chart?.resize()
}
</script>

<style lang="scss" scoped>
.order-trend-chart {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>