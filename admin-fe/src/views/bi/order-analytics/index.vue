<template>
  <div class="order-bi-dashboard">
    <div class="bi-header">
      <h2>订单数据分析</h2>
      <div class="filter-controls">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onDateRangeChange"
          style="width: 280px"
        />
        <el-select
          v-model="selectedGranularity"
          placeholder="时间粒度"
          @change="onGranularityChange"
          style="width: 120px; margin-left: 12px"
        >
          <el-option label="按天" value="day" />
          <el-option label="按周" value="week" />
          <el-option label="按月" value="month" />
        </el-select>
        <el-select
          v-model="selectedEntity"
          placeholder="实体筛选"
          clearable
          @change="onEntityChange"
          style="width: 150px; margin-left: 12px"
        >
          <el-option label="全部实体" :value="null" />
          <el-option
            v-for="entity in entityOptions"
            :key="entity.value"
            :label="entity.label"
            :value="entity.value"
          />
        </el-select>
        <el-button type="primary" @click="refreshData" style="margin-left: 12px">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 总览指标卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="6">
        <div class="metric-card">
          <div class="metric-icon orders">
            <el-icon><Document /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatNumber(overview.totalOrders) }}</div>
            <div class="metric-label">总订单数</div>
            <div class="metric-growth" :class="getGrowthClass(overview.growthRate)">
              <el-icon v-if="overview.growthRate > 0"><ArrowUp /></el-icon>
              <el-icon v-else-if="overview.growthRate < 0"><ArrowDown /></el-icon>
              {{ Math.abs(overview.growthRate).toFixed(1) }}%
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="metric-card">
          <div class="metric-icon revenue">
            <el-icon><Money /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatMoney(overview.totalRevenue) }}</div>
            <div class="metric-label">总收入</div>
            <div class="metric-sub">{{ overview.totalRevenue?.currency || 'USD' }}</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="metric-card">
          <div class="metric-icon profit">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatMoney(overview.totalProfit) }}</div>
            <div class="metric-label">总利润</div>
            <div class="metric-sub">利润率: {{ overview.profitMargin?.toFixed(1) || 0 }}%</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="metric-card">
          <div class="metric-icon completion">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ overview.completionRate?.toFixed(1) || 0 }}%</div>
            <div class="metric-label">完成率</div>
            <div class="metric-sub">取消率: {{ overview.cancellationRate?.toFixed(1) || 0 }}%</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mt-20">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
              <el-radio-group v-model="trendMetric" size="small">
                <el-radio-button value="orderCount">订单数</el-radio-button>
                <el-radio-button value="revenue">收入</el-radio-button>
                <el-radio-button value="profit">利润</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div id="trend-chart" style="height: 400px"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <template #header>订单状态分布</template>
          <div id="status-chart" style="height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <el-card>
          <template #header>按币种收入分布</template>
          <div id="currency-chart" style="height: 300px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>实体表现排行</template>
          <el-table :data="topEntities" height="300" show-summary>
            <el-table-column prop="entityName" label="实体名称" />
            <el-table-column prop="orderCount" label="订单数" />
            <el-table-column prop="revenue" label="收入" :formatter="formatRevenueColumn" />
            <el-table-column prop="profitMargin" label="利润率" :formatter="formatPercentColumn" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据导出 -->
    <el-row class="mt-20">
      <el-col :span="24">
        <el-card>
          <template #header>数据导出</template>
          <div class="export-controls">
            <el-button @click="exportData('excel', 'orders')">导出订单数据(Excel)</el-button>
            <el-button @click="exportData('csv', 'analytics')">导出分析数据(CSV)</el-button>
            <el-button @click="exportData('pdf', 'revenue')">导出收入报告(PDF)</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Money, TrendCharts, CircleCheck, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

defineOptions({ name: 'OrderBI' })

// 响应式数据
const dateRange = ref<[Date, Date]>([
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
  new Date()
])
const selectedGranularity = ref('day')
const selectedEntity = ref<number | null>(null)
const trendMetric = ref('orderCount')

// 数据状态
const loading = ref(false)
const overview = reactive({
  totalOrders: 0,
  totalRevenue: { amount: 0, currency: 'USD' },
  totalProfit: { amount: 0, currency: 'USD' },
  completionRate: 0,
  cancellationRate: 0,
  growthRate: 0,
  profitMargin: 0
})

const trendData = ref([])
const statusBreakdown = ref([])
const currencyRevenue = ref([])
const topEntities = ref([])

const entityOptions = ref([
  { label: '实体1', value: 1 },
  { label: '实体2', value: 2 },
  { label: '实体3', value: 3 }
])

// 图表实例
let trendChart: ECharts | null = null
let statusChart: ECharts | null = null
let currencyChart: ECharts | null = null

// 生命周期
onMounted(() => {
  initCharts()
  loadData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeCharts)
})

// 监听数据变化重绘图表
watch([trendData, statusBreakdown, currencyRevenue], () => {
  nextTick(() => {
    updateCharts()
  })
})

watch(trendMetric, () => {
  updateTrendChart()
})

// 方法
const initCharts = () => {
  nextTick(() => {
    // 趋势图
    const trendElement = document.getElementById('trend-chart')
    if (trendElement) {
      trendChart = echarts.init(trendElement)
    }
    
    // 状态饼图  
    const statusElement = document.getElementById('status-chart')
    if (statusElement) {
      statusChart = echarts.init(statusElement)
    }
    
    // 币种图
    const currencyElement = document.getElementById('currency-chart')
    if (currencyElement) {
      currencyChart = echarts.init(currencyElement)
    }
  })
}

const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadOverviewData(),
      loadTrendData(),
      loadStatusData(),
      loadCurrencyData(),
      loadEntityData()
    ])
  } catch (error) {
    console.error('Failed to load data:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

const loadOverviewData = async () => {
  // 模拟API调用
  const mockData = {
    totalOrders: 1250,
    totalRevenue: { amount: 2500000, currency: 'USD' },
    totalProfit: { amount: 375000, currency: 'USD' },
    completionRate: 85.6,
    cancellationRate: 8.2,
    growthRate: 12.5,
    profitMargin: 15.0
  }
  
  Object.assign(overview, mockData)
}

const loadTrendData = async () => {
  // 模拟趋势数据
  const mockTrend = []
  const startDate = dateRange.value[0]
  for (let i = 0; i < 30; i++) {
    const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
    mockTrend.push({
      date: date.toISOString().split('T')[0],
      orderCount: Math.floor(Math.random() * 50) + 20,
      revenue: { amount: Math.floor(Math.random() * 100000) + 50000, currency: 'USD' },
      profit: { amount: Math.floor(Math.random() * 15000) + 7500, currency: 'USD' }
    })
  }
  trendData.value = mockTrend
}

const loadStatusData = async () => {
  statusBreakdown.value = [
    { status: 1, statusName: '已创建', count: 120, percentage: 9.6 },
    { status: 2, statusName: '已支付', count: 200, percentage: 16.0 },
    { status: 4, statusName: '已确认', count: 680, percentage: 54.4 },
    { status: 5, statusName: '已完成', value: 5, statusName: '已完成', count: 150, percentage: 12.0 },
    { status: 6, statusName: '已取消', count: 100, percentage: 8.0 }
  ]
}

const loadCurrencyData = async () => {
  currencyRevenue.value = [
    { currency: 'USD', revenue: { amount: 1500000, currency: 'USD' }, percentage: 60.0 },
    { currency: 'EUR', revenue: { amount: 600000, currency: 'EUR' }, percentage: 24.0 },
    { currency: 'CNY', revenue: { amount: 400000, currency: 'CNY' }, percentage: 16.0 }
  ]
}

const loadEntityData = async () => {
  topEntities.value = [
    { entityName: '实体A', orderCount: 450, revenue: { amount: 900000, currency: 'USD' }, profitMargin: 18.5 },
    { entityName: '实体B', orderCount: 380, revenue: { amount: 760000, currency: 'USD' }, profitMargin: 15.2 },
    { entityName: '实体C', orderCount: 320, revenue: { amount: 640000, currency: 'USD' }, profitMargin: 12.8 },
    { entityName: '实体D', orderCount: 100, revenue: { amount: 200000, currency: 'USD' }, profitMargin: 10.5 }
  ]
}

const updateCharts = () => {  
  updateTrendChart()
  updateStatusChart()
  updateCurrencyChart()
}

const updateTrendChart = () => {
  if (!trendChart || !trendData.value.length) return
  
  const dates = trendData.value.map(item => item.date)
  let values = []
  let yAxisName = ''
  
  switch (trendMetric.value) {
    case 'orderCount':
      values = trendData.value.map(item => item.orderCount)
      yAxisName = '订单数'
      break
    case 'revenue':
      values = trendData.value.map(item => item.revenue.amount)
      yAxisName = '收入 (USD)'
      break
    case 'profit':
      values = trendData.value.map(item => item.profit.amount)
      yAxisName = '利润 (USD)'
      break
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: yAxisName
    },
    series: [{
      data: values,
      type: 'line',
      smooth: true,
      areaStyle: {
        opacity: 0.3
      }
    }]
  }
  
  trendChart.setOption(option)
}

const updateStatusChart = () => {
  if (!statusChart || !statusBreakdown.value.length) return
  
  const data = statusBreakdown.value.map(item => ({
    name: item.statusName,
    value: item.count
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '订单状态',
      type: 'pie',
      radius: '80%',
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  statusChart.setOption(option)
}

const updateCurrencyChart = () => {
  if (!currencyChart || !currencyRevenue.value.length) return
  
  const data = currencyRevenue.value.map(item => ({
    name: item.currency,
    value: item.revenue.amount
  }))
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  currencyChart.setOption(option)
}

const resizeCharts = () => {
  trendChart?.resize()
  statusChart?.resize()
  currencyChart?.resize()
}

// 事件处理
const onDateRangeChange = () => {
  loadData()
}

const onGranularityChange = () => {
  loadTrendData()
}

const onEntityChange = () => {
  loadData()
}

const refreshData = () => {
  loadData()
}

const exportData = async (exportType: string, dataType: string) => {
  try {
    ElMessage.success(`正在导出${dataType}数据为${exportType}格式...`)
    // 这里应该调用实际的导出API
    // const response = await exportAPI({ exportType, dataType, startDate, endDate })
    // window.open(response.fileUrl)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 格式化函数
const formatNumber = (num: number) => {  
  return new Intl.NumberFormat().format(num)
}

const formatMoney = (money: { amount: number, currency: string }) => {
  if (!money) return '0'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: money.currency
  }).format(money.amount)
}

const formatRevenueColumn = (row: any) => {
  return formatMoney(row.revenue)
}

const formatPercentColumn = (row: any) => {
  return `${row.profitMargin.toFixed(1)}%`
}

const getGrowthClass = (rate: number) => {
  return rate > 0 ? 'positive' : rate < 0 ? 'negative' : 'neutral'
}
</script>

<style lang="scss" scoped>
.order-bi-dashboard {
  padding: 20px;
  
  .bi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
    
    .filter-controls {
      display: flex;
      align-items: center;
    }
  }
  
  .overview-cards {
    margin-bottom: 20px;
    
    .metric-card {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      display: flex;
      align-items: center;
      
      .metric-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          font-size: 24px;
          color: white;
        }
        
        &.orders {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.revenue {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.profit {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.completion {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .metric-content {
        flex: 1;
        
        .metric-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .metric-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .metric-sub {
          font-size: 12px;
          color: #C0C4CC;
        }
        
        .metric-growth {
          font-size: 12px;
          display: flex;
          align-items: center;
          
          &.positive {
            color: #67C23A;
          }
          
          &.negative {
            color: #F56C6C;
          }
          
          &.neutral {
            color: #909399;
          }
          
          .el-icon {
            margin-right: 2px;
          }
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .export-controls {
    display: flex;
    gap: 12px;
  }
  
  .mt-20 {
    margin-top: 20px;
  }
}
</style>