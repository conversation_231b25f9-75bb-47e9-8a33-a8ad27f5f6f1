# 【示例】环境变量配置文件
# 复制此文件为 .env.local 并根据实际情况修改配置值

# ==================== 基础配置 ====================

#  版本号
VITE_VERSION = *******

# 端口号
VITE_PORT = 3037

# 权限模式（ frontend ｜ backend ）
VITE_ACCESS_MODE = backend

# 跨域请求时是否携带 Cookie（开启前需确保后端支持）
VITE_WITH_CREDENTIALS = false

# 是否打开路由信息
VITE_OPEN_ROUTE_INFO = false

# 锁屏加密密钥
VITE_LOCK_ENCRYPT_KEY = your-lock-encrypt-key

# 禁用滑块验证（开发环境）
VITE_DISABLE_SLIDER = true

# 网站地址前缀
VITE_BASE_URL = /

# API 地址前缀
VITE_API_URL = http://localhost:8080

# Delete console
VITE_DROP_CONSOLE = false

# ==================== 测试账号配置 ====================
# 注意：这些账号仅用于开发和测试环境，生产环境请禁用

# 超级管理员账号
VITE_TEST_SUPER_EMAIL = <EMAIL>
VITE_TEST_SUPER_PASSWORD = your-super-password

# 普通管理员账号
VITE_TEST_ADMIN_EMAIL = <EMAIL>
VITE_TEST_ADMIN_PASSWORD = admin-password

# 普通用户账号
VITE_TEST_USER_EMAIL = <EMAIL>
VITE_TEST_USER_PASSWORD = user-password

# ==================== 安全注意事项 ====================
# 1. 此文件包含敏感信息，请勿提交到版本控制系统
# 2. 生产环境请使用强密码
# 3. 定期更换密码和密钥
# 4. 生产环境建议禁用测试账号功能