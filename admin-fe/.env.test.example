# 测试环境配置文件示例
# 复制此文件为 .env.test 并填入实际的测试账号信息
# 注意：不要在此文件中填入真实的敏感信息

# 测试用户账号配置
# 管理员账号
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=your-admin-password-here

# 租户账号
TEST_TENANT_EMAIL=<EMAIL>
TEST_TENANT_PASSWORD=your-tenant-password-here

# 客户账号
TEST_CUSTOMER_EMAIL=<EMAIL>
TEST_CUSTOMER_PASSWORD=your-customer-password-here

# API配置
TEST_API_BASE_URL=http://localhost:8888

# 其他测试配置
# TEST_TIMEOUT=10000
# TEST_RETRY_COUNT=3