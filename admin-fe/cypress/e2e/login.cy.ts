describe('登录流程', () => {
  it('应成功登录并写入localStorage', () => {
    // 从环境变量获取测试账号信息
    const testEmail = Cypress.env('TEST_ADMIN_EMAIL') || '<EMAIL>'
    const testPassword = Cypress.env('TEST_ADMIN_PASSWORD') || 'defaultpassword'
    
    // 捕获页面console日志
    cy.on('window:before:load', win => {
      win.console.log = (...args) => {
        Cypress.log({ name: 'console.log', message: args })
      }
      win.console.error = (...args) => {
        Cypress.log({ name: 'console.error', message: args })
      }
    })
    // 捕获登录接口响应
    cy.intercept('POST', '/api/auth/login').as('loginApi')
    cy.visit('/#/auth/login')
    cy.wait(1000)
    cy.get('input[placeholder]').first().clear().type(testEmail)
    cy.get('input[type="password"]').clear().type(testPassword)
    // 滑块验证码：pointer事件多步拖动（E2E已关闭滑块）
    cy.get('.login-btn').click()
    cy.wait('@loginApi').then(({ response }) => {
      Cypress.log({ name: 'loginApi response', message: response })
    })
    cy.get('body').then($body => {
      const errors = $body.find('.el-form-item__error, .el-message, .el-notification__content').toArray().map(e => e.innerText)
      if (errors.length) {
        Cypress.log({ name: '页面错误提示', message: errors })
      }
    })
    cy.url().should('not.include', '/auth/login')
    cy.contains('欢迎回来').should('exist')
    cy.window().then(win => {
      const keys = Object.keys(win.localStorage)
      const userKey = keys.find(k => k.includes('user'))
      expect(userKey, 'userStore key').to.exist
      const userStore = win.localStorage.getItem(userKey!)
      expect(userStore, 'userStore value').to.be.a('string').and.not.empty
    })
  })
})