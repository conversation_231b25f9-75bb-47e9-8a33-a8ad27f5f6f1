{"comment": "Cypress 环境变量配置示例文件", "note": "复制此文件为 cypress.env.json 并填入实际的测试账号信息", "security_warning": "此文件包含测试账号信息，请勿提交到版本控制系统", "TEST_ADMIN_EMAIL": "<EMAIL>", "TEST_ADMIN_PASSWORD": "test-admin-password", "TEST_TENANT_EMAIL": "<EMAIL>", "TEST_TENANT_PASSWORD": "test-tenant-password", "TEST_CUSTOMER_EMAIL": "<EMAIL>", "TEST_CUSTOMER_PASSWORD": "test-customer-password", "TEST_API_BASE_URL": "http://localhost:8888", "TEST_FRONTEND_URL": "http://localhost:3037", "ENVIRONMENT": "test", "TIMEOUT": 10000, "RETRY_ATTEMPTS": 3, "setup_instructions": ["1. 复制此文件为 cypress.env.json", "2. 填入实际的测试账号信息", "3. 确保 cypress.env.json 已添加到 .gitignore", "4. 运行测试前确保后端服务已启动"]}