# 测试环境配置指南

## 概述

为了提高安全性，我们已经移除了测试代码中的硬编码账号密码。现在所有敏感信息都通过环境变量进行配置。

## 环境变量配置

### 1. 创建测试环境配置文件

在 `admin-fe` 目录下创建 `.env.test` 文件：

```bash
cp .env.test.example .env.test
```

### 2. 配置测试账号信息

编辑 `.env.test` 文件，设置以下环境变量：

```env
# 测试用户账号配置
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=your-admin-password

TEST_TENANT_EMAIL=<EMAIL>
TEST_TENANT_PASSWORD=your-tenant-password

TEST_CUSTOMER_EMAIL=<EMAIL>
TEST_CUSTOMER_PASSWORD=your-customer-password

# API配置
TEST_API_BASE_URL=http://localhost:8888
```

### 3. Cypress 测试配置

对于 Cypress 测试，需要在 `cypress.env.json` 中配置环境变量：

```json
{
  "TEST_ADMIN_EMAIL": "<EMAIL>",
  "TEST_ADMIN_PASSWORD": "your-admin-password",
  "TEST_API_BASE_URL": "http://localhost:8888"
}
```

## 安全注意事项

1. **不要提交敏感信息**：`.env.test` 和 `cypress.env.json` 文件已添加到 `.gitignore` 中，确保不会被提交到版本控制系统。

2. **使用测试专用账号**：建议为测试环境创建专用的测试账号，不要使用生产环境的真实账号。

3. **定期更换密码**：定期更换测试账号的密码，特别是在团队成员变动时。

4. **环境隔离**：确保测试环境与生产环境完全隔离。

## 运行测试

### E2E 测试

```bash
# 运行所有 E2E 测试
npm run test:e2e

# 运行特定测试文件
node tests/e2e/test_cache_mechanism.js
node tests/e2e/test_starling_i18n.js
```

### Cypress 测试

```bash
# 运行 Cypress 测试
npm run cypress:run

# 打开 Cypress 界面
npm run cypress:open
```

### Playwright 测试

```bash
# 运行 Playwright 测试
npm run test:playwright
```

## 故障排除

### 环境变量未加载

如果测试失败并提示使用默认值，请检查：

1. `.env.test` 文件是否存在且格式正确
2. 环境变量名称是否拼写正确
3. 文件路径是否正确

### 登录失败

如果测试中登录失败，请检查：

1. 测试账号是否存在且激活
2. 密码是否正确
3. API 服务是否正常运行
4. 网络连接是否正常

## 最佳实践

1. **使用环境变量**：所有敏感信息都应通过环境变量配置，不要硬编码在代码中。

2. **提供默认值**：为环境变量提供合理的默认值，确保测试在缺少配置时仍能运行。

3. **文档化配置**：清楚地文档化所有需要的环境变量及其用途。

4. **验证配置**：在测试开始前验证必需的环境变量是否已设置。

5. **分离环境**：为不同的测试环境（开发、测试、生产）使用不同的配置文件。