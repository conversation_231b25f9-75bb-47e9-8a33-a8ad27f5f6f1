/**
 * 环境变量加载工具
 * 用于在测试中安全地加载敏感配置信息
 */

import * as dotenv from 'dotenv'
import * as path from 'path'

// 加载测试环境配置
dotenv.config({ path: path.resolve(__dirname, '../../../.env.test') })

export const testConfig = {
  // API配置
  apiBaseUrl: process.env.TEST_API_BASE_URL || 'http://localhost:8888',
  
  // 测试用户配置
  users: {
    admin: {
      email: process.env.TEST_ADMIN_EMAIL || '<EMAIL>',
      password: process.env.TEST_ADMIN_PASSWORD || 'defaultpassword'
    },
    tenant: {
      email: process.env.TEST_TENANT_EMAIL || '<EMAIL>',
      password: process.env.TEST_TENANT_PASSWORD || 'defaultpassword'
    },
    customer: {
      email: process.env.TEST_CUSTOMER_EMAIL || '<EMAIL>',
      password: process.env.TEST_CUSTOMER_PASSWORD || 'defaultpassword'
    }
  }
}

/**
 * 验证必需的环境变量是否已设置
 */
export function validateTestConfig(): void {
  const requiredVars = [
    'TEST_ADMIN_EMAIL',
    'TEST_ADMIN_PASSWORD'
  ]
  
  const missing = requiredVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    console.warn(`⚠️  警告：以下测试环境变量未设置，将使用默认值：${missing.join(', ')}`)
    console.warn('请创建 .env.test 文件并设置相应的环境变量')
  }
}

// 自动验证配置
validateTestConfig()