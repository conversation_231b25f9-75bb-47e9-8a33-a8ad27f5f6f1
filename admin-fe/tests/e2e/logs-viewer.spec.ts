import { test, expect } from '@playwright/test'

test.describe('日志查看器', () => {
  test.beforeEach(async ({ page }) => {
    // 假设需要登录
    await page.goto('/login')
    // 这里应该添加登录逻辑
    // await page.fill('[data-testid="username"]', 'admin')
    // await page.fill('[data-testid="password"]', 'password')
    // await page.click('[data-testid="login-button"]')
    
    // 导航到日志查看器页面
    await page.goto('/system/logs')
  })

  test('应该显示日志查看器页面', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('.logs-viewer')).toBeVisible()
    
    // 检查搜索区域
    await expect(page.locator('.search-section')).toBeVisible()
    
    // 检查表格区域
    await expect(page.locator('.table-section')).toBeVisible()
  })

  test('应该能够进行基础搜索', async ({ page }) => {
    // 输入日志ID
    await page.fill('input[placeholder="请输入日志ID"]', 'test-log-id')
    
    // 点击搜索按钮
    await page.click('button:has-text("搜索")')
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 检查是否有加载状态
    await expect(page.locator('.el-table')).toBeVisible()
  })

  test('应该能够切换高级搜索', async ({ page }) => {
    // 点击高级搜索按钮
    await page.click('button:has-text("高级搜索")')
    
    // 检查高级搜索区域是否显示
    await expect(page.locator('.advanced-search')).toBeVisible()
    
    // 再次点击切换回简单搜索
    await page.click('button:has-text("简单搜索")')
    
    // 检查高级搜索区域是否隐藏
    await expect(page.locator('.advanced-search')).toBeHidden()
  })

  test('应该能够显示/隐藏统计信息', async ({ page }) => {
    // 点击显示统计按钮
    await page.click('button:has-text("显示统计")')
    
    // 检查统计区域是否显示
    await expect(page.locator('.statistics-section')).toBeVisible()
    
    // 检查统计卡片
    await expect(page.locator('.stat-card')).toHaveCount(4)
    
    // 再次点击隐藏统计
    await page.click('button:has-text("隐藏统计")')
    
    // 检查统计区域是否隐藏
    await expect(page.locator('.statistics-section')).toBeHidden()
  })

  test('应该能够打开日志详情', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr', { timeout: 5000 })
    
    // 点击第一行
    await page.click('.el-table tbody tr:first-child')
    
    // 检查详情弹窗是否打开
    await expect(page.locator('.el-dialog:has-text("日志详情")')).toBeVisible()
    
    // 检查基础信息区域
    await expect(page.locator('.info-card')).toBeVisible()
    
    // 检查数据区域
    await expect(page.locator('.data-card')).toBeVisible()
  })

  test('应该能够切换请求/响应标签', async ({ page }) => {
    // 先打开日志详情
    await page.waitForSelector('.el-table tbody tr', { timeout: 5000 })
    await page.click('.el-table tbody tr:first-child')
    
    // 等待弹窗打开
    await expect(page.locator('.el-dialog:has-text("日志详情")')).toBeVisible()
    
    // 点击响应标签
    await page.click('label:has-text("响应")')
    
    // 检查响应数据是否显示
    await expect(page.locator('.response-data')).toBeVisible()
    
    // 点击对比标签
    await page.click('label:has-text("对比")')
    
    // 检查对比视图是否显示
    await expect(page.locator('.compare-data')).toBeVisible()
  })

  test('应该能够打开导出弹窗', async ({ page }) => {
    // 点击导出按钮
    await page.click('button:has-text("导出")')
    
    // 检查导出弹窗是否打开
    await expect(page.locator('.el-dialog:has-text("导出日志")')).toBeVisible()
    
    // 检查导出格式选项
    await expect(page.locator('label:has-text("CSV格式")')).toBeVisible()
    await expect(page.locator('label:has-text("JSON格式")')).toBeVisible()
  })

  test('应该能够设置时间范围', async ({ page }) => {
    // 点击时间范围选择器
    await page.click('.el-date-editor')
    
    // 等待日期选择器打开
    await page.waitForSelector('.el-picker-panel', { timeout: 2000 })
    
    // 选择今天
    await page.click('.el-picker-panel .today')
    
    // 检查搜索表单是否更新
    // 这里可以添加更具体的验证逻辑
  })

  test('应该能够切换自动刷新', async ({ page }) => {
    // 找到自动刷新开关
    const autoRefreshSwitch = page.locator('.el-switch:has-text("自动刷新")')
    
    // 点击开启自动刷新
    await autoRefreshSwitch.click()
    
    // 等待一段时间，检查是否有刷新行为
    await page.waitForTimeout(2000)
    
    // 再次点击关闭自动刷新
    await autoRefreshSwitch.click()
  })

  test('应该能够进行分页操作', async ({ page }) => {
    // 等待分页器加载
    await page.waitForSelector('.el-pagination', { timeout: 5000 })
    
    // 检查分页器是否存在
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 如果有多页，测试翻页
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      await nextButton.click()
      await page.waitForTimeout(1000)
    }
  })
})

test.describe('日志查看器 - 错误处理', () => {
  test('应该处理网络错误', async ({ page }) => {
    // 模拟网络错误
    await page.route('**/api/bi/log/**', route => {
      route.abort('failed')
    })
    
    await page.goto('/system/logs')
    
    // 检查是否显示错误信息
    await expect(page.locator('.el-message--error')).toBeVisible({ timeout: 5000 })
  })

  test('应该处理空数据', async ({ page }) => {
    // 模拟空数据响应
    await page.route('**/api/bi/log/QueryLogs', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          logs: [],
          total: 0,
          page: 1,
          size: 20
        })
      })
    })
    
    await page.goto('/system/logs')
    
    // 检查空状态
    await expect(page.locator('.el-table__empty-block')).toBeVisible({ timeout: 5000 })
  })
})
