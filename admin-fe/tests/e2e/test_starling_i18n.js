/**
 * Starling多语言功能端到端测试
 */

const assert = require('assert')
const axios = require('axios')

// 测试配置
const API_BASE_URL = process.env.TEST_API_BASE_URL || 'http://localhost:8888'
const TEST_USER = {
  email: process.env.TEST_ADMIN_EMAIL || '<EMAIL>',
  password: process.env.TEST_ADMIN_PASSWORD || 'defaultpassword'
}

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 全局变量
let authToken = ''
let sessionId = ''

/**
 * 登录获取token
 */
async function login() {
  try {
    console.log('🔐 正在登录...')
    const response = await api.post('/api/user/login', {
      email: TEST_USER.email,
      password: TEST_USER.password
    })

    const { data } = response.data
    authToken = data.token
    sessionId = data.sessionId

    // 设置默认请求头
    api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
    api.defaults.headers.common['X-Session-Id'] = sessionId

    console.log('✅ 登录成功')
    return true
  } catch (error) {
    console.error('❌ 登录失败:', error.response?.data || error.message)
    return false
  }
}

/**
 * 测试InternalHomepage接口的starlingMap
 */
async function testInternalHomepageStarlingMap() {
  console.log('\n📋 测试InternalHomepage接口的starlingMap...')
  
  try {
    const response = await api.post('/api/view/internalHomepage', {})
    const { data } = response.data

    // 验证响应结构
    assert(data.starlingMap, '应该包含starlingMap字段')
    assert(typeof data.starlingMap === 'object', 'starlingMap应该是对象类型')

    const starlingMap = data.starlingMap
    const keys = Object.keys(starlingMap)
    
    console.log(`📊 StarlingMap统计信息:`)
    console.log(`   - 总条目数: ${keys.length}`)
    
    if (keys.length > 0) {
      // 分析空间和应用
      const spaces = new Set()
      const applications = new Set()
      
      keys.forEach(key => {
        const parts = key.split('.')
        if (parts.length >= 2) {
          spaces.add(parts[0])
          if (parts.length >= 3) {
            applications.add(parts[0] + '.' + parts[1])
          }
        }
      })
      
      console.log(`   - 空间数: ${spaces.size}`)
      console.log(`   - 应用数: ${applications.size}`)
      console.log(`   - 空间列表: ${Array.from(spaces).join(', ')}`)
      
      // 验证关键的多语言条目
      const requiredKeys = [
        'common.button.search',
        'common.button.add',
        'common.button.edit',
        'common.button.delete',
        'booking.order.title',
        'booking.order.orderId'
      ]
      
      console.log('\n🔍 验证关键多语言条目:')
      for (const key of requiredKeys) {
        if (starlingMap[key]) {
          const item = starlingMap[key]
          console.log(`   ✅ ${key}:`)
          console.log(`      - 中文: ${item.zh || '无'}`)
          console.log(`      - 英文: ${item.en || '无'}`)
          console.log(`      - 阿拉伯文: ${item.ar || '无'}`)
        } else {
          console.log(`   ❌ ${key}: 缺失`)
        }
      }
      
      // 验证订单相关的多语言条目
      console.log('\n📦 验证订单相关多语言条目:')
      const orderKeys = keys.filter(key => key.startsWith('booking.order.'))
      console.log(`   - 订单相关条目数: ${orderKeys.length}`)
      
      orderKeys.slice(0, 5).forEach(key => {
        const item = starlingMap[key]
        console.log(`   - ${key}: ${item.zh || item.en || '无文本'}`)
      })
    }

    console.log('✅ InternalHomepage starlingMap测试通过')
    return starlingMap
  } catch (error) {
    console.error('❌ InternalHomepage starlingMap测试失败:', error.response?.data || error.message)
    throw error
  }
}

/**
 * 测试starling配置管理接口
 */
async function testStarlingManagement() {
  console.log('\n⚙️ 测试starling配置管理接口...')
  
  try {
    // 测试列表接口
    const listResponse = await api.post('/api/content/listStarling', {
      application: 'admin-fe',
      page: {
        pageNum: 1,
        pageSize: 10
      }
    })
    
    const { data: listData } = listResponse.data
    assert(listData.table, '应该包含table字段')
    assert(Array.isArray(listData.table.rows), 'table.rows应该是数组')
    
    console.log(`📋 Starling配置列表:`)
    console.log(`   - 总条目数: ${listData.table.rows.length}`)
    
    if (listData.table.rows.length > 0) {
      const firstItem = listData.table.rows[0]
      console.log(`   - 示例条目:`)
      console.log(`     - ID: ${firstItem.id}`)
      console.log(`     - 应用: ${firstItem.application}`)
      console.log(`     - 空间: ${firstItem.space}`)
      console.log(`     - 键名: ${firstItem.key}`)
      console.log(`     - 中文: ${firstItem.content?.zh || '无'}`)
      console.log(`     - 英文: ${firstItem.content?.en || '无'}`)
    }
    
    console.log('✅ Starling配置管理接口测试通过')
    return listData
  } catch (error) {
    console.error('❌ Starling配置管理接口测试失败:', error.response?.data || error.message)
    throw error
  }
}

/**
 * 测试多语言文本的完整性
 */
async function testI18nCompleteness(starlingMap) {
  console.log('\n🌐 测试多语言文本完整性...')
  
  const stats = {
    total: 0,
    hasZh: 0,
    hasEn: 0,
    hasAr: 0,
    incomplete: []
  }
  
  Object.entries(starlingMap).forEach(([key, value]) => {
    stats.total++
    
    if (value.zh) stats.hasZh++
    if (value.en) stats.hasEn++
    if (value.ar) stats.hasAr++
    
    // 检查是否缺少中文或英文（必需的语言）
    if (!value.zh || !value.en) {
      stats.incomplete.push({
        key,
        missingZh: !value.zh,
        missingEn: !value.en
      })
    }
  })
  
  console.log(`📊 多语言完整性统计:`)
  console.log(`   - 总条目数: ${stats.total}`)
  console.log(`   - 有中文: ${stats.hasZh} (${(stats.hasZh/stats.total*100).toFixed(1)}%)`)
  console.log(`   - 有英文: ${stats.hasEn} (${(stats.hasEn/stats.total*100).toFixed(1)}%)`)
  console.log(`   - 有阿拉伯文: ${stats.hasAr} (${(stats.hasAr/stats.total*100).toFixed(1)}%)`)
  console.log(`   - 不完整条目: ${stats.incomplete.length}`)
  
  if (stats.incomplete.length > 0) {
    console.log('\n⚠️ 不完整的多语言条目:')
    stats.incomplete.slice(0, 5).forEach(item => {
      const missing = []
      if (item.missingZh) missing.push('中文')
      if (item.missingEn) missing.push('英文')
      console.log(`   - ${item.key}: 缺少 ${missing.join(', ')}`)
    })
    
    if (stats.incomplete.length > 5) {
      console.log(`   ... 还有 ${stats.incomplete.length - 5} 个不完整条目`)
    }
  }
  
  console.log('✅ 多语言完整性测试完成')
  return stats
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始Starling多语言功能测试\n')
  
  try {
    // 1. 登录
    const loginSuccess = await login()
    if (!loginSuccess) {
      throw new Error('登录失败，无法继续测试')
    }
    
    // 2. 测试InternalHomepage的starlingMap
    const starlingMap = await testInternalHomepageStarlingMap()
    
    // 3. 测试starling配置管理
    await testStarlingManagement()
    
    // 4. 测试多语言完整性
    await testI18nCompleteness(starlingMap)
    
    console.log('\n🎉 所有测试通过！')
    
  } catch (error) {
    console.error('\n💥 测试失败:', error.message)
    process.exit(1)
  }
}

// 运行测试
if (require.main === module) {
  runTests()
}

module.exports = {
  runTests,
  testInternalHomepageStarlingMap,
  testStarlingManagement,
  testI18nCompleteness
}
