# 前端安全配置指南

## 概述

本文档说明如何在前端项目中安全地配置环境变量和测试账号，避免硬编码敏感信息。

## 环境变量配置

### 1. 环境变量文件结构

```
admin-fe/
├── .env                    # 通用环境变量
├── .env.dev               # 开发环境变量
├── .env.uat               # UAT环境变量
├── .env.prd               # 生产环境变量
├── .env.example           # 环境变量示例文件（可提交）
├── .env.local             # 本地开发覆盖（不提交）
└── .env.*.local           # 各环境本地覆盖（不提交）
```

### 2. 测试账号配置

在 `.env.local` 或相应环境的 `.env.*.local` 文件中配置：

```bash
# 测试账号配置（仅开发/测试环境）
VITE_TEST_SUPER_EMAIL=<EMAIL>
VITE_TEST_SUPER_PASSWORD=your-super-password
VITE_TEST_ADMIN_EMAIL=<EMAIL>
VITE_TEST_ADMIN_PASSWORD=admin-password
VITE_TEST_USER_EMAIL=<EMAIL>
VITE_TEST_USER_PASSWORD=user-password
```

### 3. 代码中的使用方式

```typescript
// 从环境变量读取测试账号信息
const accounts = computed<Account[]>(() => [
  {
    key: 'super',
    label: t('login.roles.super'),
    userName: import.meta.env.VITE_TEST_SUPER_EMAIL || '<EMAIL>',
    password: import.meta.env.VITE_TEST_SUPER_PASSWORD || 'super-password',
    roles: ['R_SUPER']
  },
  // ... 其他账号
])
```

## 安全最佳实践

### 1. 文件权限控制

- ✅ **可以提交**: `.env.example`、`.env.dev`、`.env.uat`、`.env.prd`（不包含敏感信息）
- ❌ **禁止提交**: `.env.local`、`.env.*.local`、`cypress.env.json`（包含敏感信息）

### 2. 密码策略

- 开发环境：使用简单密码便于开发
- 测试环境：使用中等强度密码
- 生产环境：使用强密码，定期轮换

### 3. 环境隔离

```bash
# 开发环境
VITE_TEST_SUPER_EMAIL=<EMAIL>
VITE_TEST_SUPER_PASSWORD=dev123456

# 测试环境
VITE_TEST_SUPER_EMAIL=<EMAIL>
VITE_TEST_SUPER_PASSWORD=test@2024

# 生产环境（建议禁用测试账号）
# VITE_TEST_SUPER_EMAIL=
# VITE_TEST_SUPER_PASSWORD=
```

## 部署配置

### 1. Docker 部署

```dockerfile
# Dockerfile
FROM node:18-alpine

# 复制环境变量文件
COPY .env.example .env

# 构建时传入环境变量
ARG VITE_TEST_SUPER_EMAIL
ARG VITE_TEST_SUPER_PASSWORD
ENV VITE_TEST_SUPER_EMAIL=$VITE_TEST_SUPER_EMAIL
ENV VITE_TEST_SUPER_PASSWORD=$VITE_TEST_SUPER_PASSWORD

# 构建应用
RUN npm run build
```

### 2. Kubernetes 部署

```yaml
# k8s-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: frontend-test-accounts
type: Opaque
data:
  VITE_TEST_SUPER_EMAIL: <base64-encoded-email>
  VITE_TEST_SUPER_PASSWORD: <base64-encoded-password>

---
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend-app
spec:
  template:
    spec:
      containers:
      - name: frontend
        image: frontend:latest
        envFrom:
        - secretRef:
            name: frontend-test-accounts
```

### 3. CI/CD 配置

```yaml
# .github/workflows/deploy.yml
name: Deploy Frontend

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build with secrets
      env:
        VITE_TEST_SUPER_EMAIL: ${{ secrets.VITE_TEST_SUPER_EMAIL }}
        VITE_TEST_SUPER_PASSWORD: ${{ secrets.VITE_TEST_SUPER_PASSWORD }}
      run: |
        npm ci
        npm run build
```

## 安全检查清单

### 开发阶段

- [ ] 创建 `.env.example` 文件，包含所有必要的环境变量占位符
- [ ] 更新 `.gitignore`，确保敏感文件不被提交
- [ ] 代码中移除所有硬编码的账号信息
- [ ] 使用环境变量读取敏感配置

### 测试阶段

- [ ] 验证不同环境下的配置是否正确
- [ ] 确认测试账号在各环境中正常工作
- [ ] 检查是否有敏感信息泄露到日志中

### 部署阶段

- [ ] 生产环境使用强密码
- [ ] 配置适当的访问控制
- [ ] 设置监控和告警
- [ ] 定期轮换密码和密钥

## 应急响应

### 密码泄露处理

1. **立即行动**
   - 更改所有相关密码
   - 撤销相关访问权限
   - 检查访问日志

2. **通知相关人员**
   - 通知安全团队
   - 通知相关开发人员
   - 记录事件详情

3. **预防措施**
   - 加强密码策略
   - 增加访问监控
   - 定期安全审计

## 相关文档

- [后端安全配置指南](../../docs/SECURITY_CONFIGURATION.md)
- [环境变量最佳实践](./ENVIRONMENT_VARIABLES.md)
- [部署指南](./DEPLOYMENT.md)