package docs

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/types"
	"hotel/user/cache"
	"hotel/user/domain"
	"hotel/user/mysql"
)

// PerformanceTestSuite 性能测试套件
type PerformanceTestSuite struct {
	db           sqlx.SqlConn
	rds          redis.Redis
	userDao      *mysql.UserDao
	optimizedDao *mysql.OptimizedUserDao
	userCache    cache.UserCache
}

// BenchmarkResult 基准测试结果
type BenchmarkResult struct {
	TestName        string        `json:"test_name"`
	TotalRequests   int           `json:"total_requests"`
	SuccessRequests int           `json:"success_requests"`
	FailedRequests  int           `json:"failed_requests"`
	TotalDuration   time.Duration `json:"total_duration"`
	AvgDuration     time.Duration `json:"avg_duration"`
	MinDuration     time.Duration `json:"min_duration"`
	MaxDuration     time.Duration `json:"max_duration"`
	QPS             float64       `json:"qps"`
	P95Duration     time.Duration `json:"p95_duration"`
	P99Duration     time.Duration `json:"p99_duration"`
}

// TestLoginPerformance 测试登录性能
func (suite *PerformanceTestSuite) TestLoginPerformance(t *testing.T) {
	ctx := context.Background()
	
	// 准备测试数据
	testUsers := suite.prepareTestUsers(t, 100)
	
	t.Run("Original Login Performance", func(t *testing.T) {
		result := suite.benchmarkOriginalLogin(ctx, testUsers, 1000)
		t.Logf("Original Login - QPS: %.2f, Avg: %v, P95: %v", 
			result.QPS, result.AvgDuration, result.P95Duration)
		
		// 性能目标：QPS > 100, 平均响应时间 < 100ms
		assert.Greater(t, result.QPS, 100.0, "QPS should be greater than 100")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(100), "Average response time should be less than 100ms")
	})
	
	t.Run("Optimized Login Performance", func(t *testing.T) {
		result := suite.benchmarkOptimizedLogin(ctx, testUsers, 1000)
		t.Logf("Optimized Login - QPS: %.2f, Avg: %v, P95: %v", 
			result.QPS, result.AvgDuration, result.P95Duration)
		
		// 优化后的性能目标：QPS > 200, 平均响应时间 < 50ms
		assert.Greater(t, result.QPS, 200.0, "Optimized QPS should be greater than 200")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(50), "Optimized average response time should be less than 50ms")
	})
}

// TestCachePerformance 测试缓存性能
func (suite *PerformanceTestSuite) TestCachePerformance(t *testing.T) {
	ctx := context.Background()
	
	t.Run("Cache Hit Performance", func(t *testing.T) {
		// 预热缓存
		testUser := suite.createTestUser()
		suite.userCache.SetUser(ctx, testUser.ID, testUser)
		
		result := suite.benchmarkCacheHit(ctx, testUser.ID, 1000)
		t.Logf("Cache Hit - QPS: %.2f, Avg: %v", result.QPS, result.AvgDuration)
		
		// 缓存命中性能目标：QPS > 1000, 平均响应时间 < 5ms
		assert.Greater(t, result.QPS, 1000.0, "Cache hit QPS should be greater than 1000")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(5), "Cache hit response time should be less than 5ms")
	})
	
	t.Run("Cache Miss Performance", func(t *testing.T) {
		result := suite.benchmarkCacheMiss(ctx, 1000)
		t.Logf("Cache Miss - QPS: %.2f, Avg: %v", result.QPS, result.AvgDuration)
		
		// 缓存未命中性能目标：QPS > 100, 平均响应时间 < 50ms
		assert.Greater(t, result.QPS, 100.0, "Cache miss QPS should be greater than 100")
		assert.Less(t, result.AvgDuration.Milliseconds(), int64(50), "Cache miss response time should be less than 50ms")
	})
}

// TestConcurrentLogin 测试并发登录
func (suite *PerformanceTestSuite) TestConcurrentLogin(t *testing.T) {
	ctx := context.Background()
	testUsers := suite.prepareTestUsers(t, 10)
	
	concurrencyLevels := []int{10, 50, 100, 200}
	
	for _, concurrency := range concurrencyLevels {
		t.Run(fmt.Sprintf("Concurrent Login - %d goroutines", concurrency), func(t *testing.T) {
			result := suite.benchmarkConcurrentLogin(ctx, testUsers, concurrency, 100)
			t.Logf("Concurrency %d - QPS: %.2f, Avg: %v, Success Rate: %.2f%%", 
				concurrency, result.QPS, result.AvgDuration, 
				float64(result.SuccessRequests)/float64(result.TotalRequests)*100)
			
			// 并发性能目标：成功率 > 95%
			successRate := float64(result.SuccessRequests) / float64(result.TotalRequests)
			assert.Greater(t, successRate, 0.95, "Success rate should be greater than 95%")
		})
	}
}

// benchmarkOriginalLogin 基准测试原始登录方法
func (suite *PerformanceTestSuite) benchmarkOriginalLogin(ctx context.Context, users []*domain.User, requests int) *BenchmarkResult {
	durations := make([]time.Duration, 0, requests)
	successCount := 0
	failedCount := 0
	
	startTime := time.Now()
	
	for i := 0; i < requests; i++ {
		user := users[i%len(users)]
		
		reqStart := time.Now()
		_, err := suite.userDao.GetUserByKey(ctx, user.Key)
		reqDuration := time.Since(reqStart)
		
		durations = append(durations, reqDuration)
		
		if err != nil {
			failedCount++
		} else {
			successCount++
		}
	}
	
	totalDuration := time.Since(startTime)
	
	return suite.calculateBenchmarkResult("Original Login", requests, successCount, failedCount, totalDuration, durations)
}

// benchmarkOptimizedLogin 基准测试优化后的登录方法
func (suite *PerformanceTestSuite) benchmarkOptimizedLogin(ctx context.Context, users []*domain.User, requests int) *BenchmarkResult {
	durations := make([]time.Duration, 0, requests)
	successCount := 0
	failedCount := 0
	
	startTime := time.Now()
	
	for i := 0; i < requests; i++ {
		user := users[i%len(users)]
		
		reqStart := time.Now()
		_, err := suite.optimizedDao.GetUserByKeyOptimized(ctx, user.Key)
		reqDuration := time.Since(reqStart)
		
		durations = append(durations, reqDuration)
		
		if err != nil {
			failedCount++
		} else {
			successCount++
		}
	}
	
	totalDuration := time.Since(startTime)
	
	return suite.calculateBenchmarkResult("Optimized Login", requests, successCount, failedCount, totalDuration, durations)
}

// benchmarkCacheHit 基准测试缓存命中
func (suite *PerformanceTestSuite) benchmarkCacheHit(ctx context.Context, userID types.ID, requests int) *BenchmarkResult {
	durations := make([]time.Duration, 0, requests)
	successCount := 0
	failedCount := 0
	
	startTime := time.Now()
	
	for i := 0; i < requests; i++ {
		reqStart := time.Now()
		_, err := suite.userCache.GetUser(ctx, userID)
		reqDuration := time.Since(reqStart)
		
		durations = append(durations, reqDuration)
		
		if err != nil {
			failedCount++
		} else {
			successCount++
		}
	}
	
	totalDuration := time.Since(startTime)
	
	return suite.calculateBenchmarkResult("Cache Hit", requests, successCount, failedCount, totalDuration, durations)
}

// benchmarkCacheMiss 基准测试缓存未命中
func (suite *PerformanceTestSuite) benchmarkCacheMiss(ctx context.Context, requests int) *BenchmarkResult {
	durations := make([]time.Duration, 0, requests)
	successCount := 0
	failedCount := 0
	
	startTime := time.Now()
	
	for i := 0; i < requests; i++ {
		// 使用不存在的用户ID
		nonExistentID := types.ID(999999 + i)
		
		reqStart := time.Now()
		_, err := suite.userCache.GetUser(ctx, nonExistentID)
		reqDuration := time.Since(reqStart)
		
		durations = append(durations, reqDuration)
		
		if err != nil && err == redis.Nil {
			successCount++ // 缓存未命中是预期的
		} else {
			failedCount++
		}
	}
	
	totalDuration := time.Since(startTime)
	
	return suite.calculateBenchmarkResult("Cache Miss", requests, successCount, failedCount, totalDuration, durations)
}

// benchmarkConcurrentLogin 基准测试并发登录
func (suite *PerformanceTestSuite) benchmarkConcurrentLogin(ctx context.Context, users []*domain.User, concurrency, requestsPerGoroutine int) *BenchmarkResult {
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	allDurations := make([]time.Duration, 0, concurrency*requestsPerGoroutine)
	totalSuccess := 0
	totalFailed := 0
	
	startTime := time.Now()
	
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			
			localDurations := make([]time.Duration, 0, requestsPerGoroutine)
			localSuccess := 0
			localFailed := 0
			
			for j := 0; j < requestsPerGoroutine; j++ {
				user := users[(goroutineID*requestsPerGoroutine+j)%len(users)]
				
				reqStart := time.Now()
				_, err := suite.optimizedDao.GetUserByKeyOptimized(ctx, user.Key)
				reqDuration := time.Since(reqStart)
				
				localDurations = append(localDurations, reqDuration)
				
				if err != nil {
					localFailed++
				} else {
					localSuccess++
				}
			}
			
			mu.Lock()
			allDurations = append(allDurations, localDurations...)
			totalSuccess += localSuccess
			totalFailed += localFailed
			mu.Unlock()
		}(i)
	}
	
	wg.Wait()
	totalDuration := time.Since(startTime)
	totalRequests := concurrency * requestsPerGoroutine
	
	return suite.calculateBenchmarkResult("Concurrent Login", totalRequests, totalSuccess, totalFailed, totalDuration, allDurations)
}

// calculateBenchmarkResult 计算基准测试结果
func (suite *PerformanceTestSuite) calculateBenchmarkResult(testName string, totalRequests, successRequests, failedRequests int, totalDuration time.Duration, durations []time.Duration) *BenchmarkResult {
	if len(durations) == 0 {
		return &BenchmarkResult{
			TestName:        testName,
			TotalRequests:   totalRequests,
			SuccessRequests: successRequests,
			FailedRequests:  failedRequests,
			TotalDuration:   totalDuration,
		}
	}
	
	// 计算平均值
	var totalDur time.Duration
	minDur := durations[0]
	maxDur := durations[0]
	
	for _, dur := range durations {
		totalDur += dur
		if dur < minDur {
			minDur = dur
		}
		if dur > maxDur {
			maxDur = dur
		}
	}
	
	avgDur := totalDur / time.Duration(len(durations))
	
	// 计算QPS
	qps := float64(successRequests) / totalDuration.Seconds()
	
	// 计算P95和P99
	sortedDurations := make([]time.Duration, len(durations))
	copy(sortedDurations, durations)
	// 这里应该排序，简化处理
	p95Index := int(float64(len(sortedDurations)) * 0.95)
	p99Index := int(float64(len(sortedDurations)) * 0.99)
	
	if p95Index >= len(sortedDurations) {
		p95Index = len(sortedDurations) - 1
	}
	if p99Index >= len(sortedDurations) {
		p99Index = len(sortedDurations) - 1
	}
	
	return &BenchmarkResult{
		TestName:        testName,
		TotalRequests:   totalRequests,
		SuccessRequests: successRequests,
		FailedRequests:  failedRequests,
		TotalDuration:   totalDuration,
		AvgDuration:     avgDur,
		MinDuration:     minDur,
		MaxDuration:     maxDur,
		QPS:             qps,
		P95Duration:     sortedDurations[p95Index],
		P99Duration:     sortedDurations[p99Index],
	}
}

// prepareTestUsers 准备测试用户数据
func (suite *PerformanceTestSuite) prepareTestUsers(t *testing.T, count int) []*domain.User {
	users := make([]*domain.User, count)
	for i := 0; i < count; i++ {
		users[i] = suite.createTestUser()
		users[i].Key = fmt.Sprintf("<EMAIL>", i)
	}
	return users
}

// createTestUser 创建测试用户
func (suite *PerformanceTestSuite) createTestUser() *domain.User {
	return &domain.User{
		UserBasic: &domain.UserBasic{
			ID:       types.ID(1),
			Key:      "<EMAIL>",
			Username: "testuser",
			Secret:   "$2a$10$hash", // bcrypt hash
		},
		UserEntityConnections: []*domain.UserEntityConnection{},
	}
}
