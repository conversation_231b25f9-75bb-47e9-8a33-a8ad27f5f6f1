# User模块SQL查询分析与性能优化方案

## 1. 概述

本文档分析了user模块中的SQL查询复杂性，特别是登录认证链路的性能瓶颈，并提供了详细的优化方案。

## 2. 核心表结构分析

### 2.1 主要数据表

| 表名 | 主要字段 | 用途 |
|------|----------|------|
| `user` | id, username, key, secret, profile | 用户基本信息 |
| `user_role` | user_id, role_id, entity_id, status | 用户角色关联 |
| `role` | id, name, scope, privileges | 角色定义 |
| `entity` | id, type, name, root_entity_id, parent_entity_id | 实体层级结构 |
| `entity_user_link` | entity_id, user_id, status | 实体用户关联 |
| `entity_entity_link` | buyer_entity_id, seller_entity_id | 实体间关联 |

### 2.2 表关系图

```
user (1) -----> (N) user_role (N) -----> (1) role
  |                    |
  |                    v
  |               entity (层级结构)
  |                    |
  v                    v
entity_user_link <-> entity_entity_link
```

## 3. 当前SQL查询分析

### 3.1 登录认证流程查询

#### 3.1.1 用户基本信息查询
```sql
-- FindOneByKey 查询
SELECT id, username, key, secret, profile, is_deleted, create_time, update_time 
FROM user 
WHERE `key` = ? 
LIMIT 1
```

#### 3.1.2 用户完整信息查询（GetUser方法）
当前实现使用多个串行查询：

```sql
-- 1. 查询用户角色关联
SELECT id, user_id, role_id, entity_id, status, expire_time, create_time, update_time
FROM user_role 
WHERE user_id = ?

-- 2. 批量查询用户信息
SELECT id, username, key, secret, profile, is_deleted, create_time, update_time
FROM user 
WHERE id IN (?)

-- 3. 批量查询角色信息
SELECT id, name, scope, privileges, description, is_deleted, create_time, update_time
FROM role 
WHERE id IN (?)

-- 4. 批量查询实体信息
SELECT id, type, name, root_entity_id, parent_entity_id, profile, distribution_config, 
       finance_config, supplier_config, status, is_deleted, create_time, update_time
FROM entity 
WHERE id IN (?)

-- 5. 查询实体用户关联
SELECT id, entity_id, user_id, status, seller_in_node_rule_id, seller_out_node_rule_id,
       buyer_in_node_rule_id, buyer_out_node_rule_id, seller_in_credential_id, rate_limit
FROM entity_user_link 
WHERE user_id = ? AND entity_id IN (?)
```

## 4. 性能瓶颈分析

### 4.1 主要问题

1. **多次数据库往返**：登录时需要执行5-6个独立查询
2. **缺少索引优化**：某些查询字段可能缺少合适的索引
3. **JSON字段解析开销**：profile、privileges等JSON字段的序列化/反序列化
4. **实体层级查询复杂**：需要查询实体的祖先关系
5. **无缓存机制**：每次请求都需要查询数据库

### 4.2 查询性能指标（需要测量）

- 登录查询总耗时：目标 < 100ms
- 单个查询耗时：目标 < 20ms
- 数据库连接数：监控峰值使用情况
- 缓存命中率：目标 > 80%

## 5. 优化方案

### 5.1 数据库索引优化

#### 5.1.1 必需索引
```sql
-- user表索引
CREATE INDEX idx_user_key ON user(key);
CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_deleted ON user(is_deleted);

-- user_role表索引
CREATE INDEX idx_user_role_user_id ON user_role(user_id);
CREATE INDEX idx_user_role_entity_id ON user_role(entity_id);
CREATE INDEX idx_user_role_role_id ON user_role(role_id);
CREATE INDEX idx_user_role_status ON user_role(status);
CREATE INDEX idx_user_role_composite ON user_role(user_id, entity_id, status);

-- entity表索引
CREATE INDEX idx_entity_root_id ON entity(root_entity_id);
CREATE INDEX idx_entity_parent_id ON entity(parent_entity_id);
CREATE INDEX idx_entity_type ON entity(type);
CREATE INDEX idx_entity_status ON entity(status);

-- entity_user_link表索引
CREATE INDEX idx_entity_user_link_user_id ON entity_user_link(user_id);
CREATE INDEX idx_entity_user_link_entity_id ON entity_user_link(entity_id);
CREATE INDEX idx_entity_user_link_composite ON entity_user_link(user_id, entity_id);

-- role表索引
CREATE INDEX idx_role_name ON role(name);
CREATE INDEX idx_role_scope ON role(scope);
```

### 5.2 SQL查询优化

#### 5.2.1 优化后的GetUser查询
将多个查询合并为一个JOIN查询：

```sql
SELECT 
    u.id as user_id, u.username, u.key, u.secret, u.profile as user_profile,
    u.create_time as user_create_time,
    ur.id as user_role_id, ur.role_id, ur.entity_id, ur.status as user_role_status,
    ur.expire_time,
    r.id as role_id, r.name as role_name, r.scope, r.privileges, r.description as role_desc,
    e.id as entity_id, e.type as entity_type, e.name as entity_name,
    e.root_entity_id, e.parent_entity_id, e.profile as entity_profile,
    e.distribution_config, e.finance_config, e.supplier_config,
    eul.status as link_status, eul.seller_in_node_rule_id, eul.seller_out_node_rule_id,
    eul.buyer_in_node_rule_id, eul.buyer_out_node_rule_id, eul.rate_limit
FROM user u
LEFT JOIN user_role ur ON u.id = ur.user_id AND ur.status = 1
LEFT JOIN role r ON ur.role_id = r.id AND r.is_deleted = 0
LEFT JOIN entity e ON ur.entity_id = e.id AND e.is_deleted = 0
LEFT JOIN entity_user_link eul ON u.id = eul.user_id AND ur.entity_id = eul.entity_id
WHERE u.id = ? AND u.is_deleted = 0
```

#### 5.2.2 登录优化查询
```sql
-- 优化的登录查询，一次性获取必要信息
SELECT 
    u.id, u.username, u.key, u.secret, u.profile,
    COUNT(ur.id) as role_count
FROM user u
LEFT JOIN user_role ur ON u.id = ur.user_id AND ur.status = 1
WHERE u.key = ? AND u.is_deleted = 0
GROUP BY u.id, u.username, u.key, u.secret, u.profile
LIMIT 1
```

### 5.3 缓存策略

#### 5.3.1 Redis缓存设计
```go
// 缓存键设计
const (
    UserCacheKeyPrefix = "user:"
    UserRoleCacheKeyPrefix = "user_role:"
    EntityCacheKeyPrefix = "entity:"
    CacheTTL = 30 * time.Minute
)

// 缓存结构
type CachedUser struct {
    User *domain.User `json:"user"`
    CachedAt time.Time `json:"cached_at"`
}
```

#### 5.3.2 缓存实现策略
1. **用户基本信息缓存**：缓存30分钟
2. **用户角色关联缓存**：缓存15分钟
3. **实体信息缓存**：缓存1小时（变更较少）
4. **缓存更新策略**：写入时失效相关缓存

## 6. 实施计划

### 6.1 第一阶段（高优先级 - 1周内）
- [ ] 添加必需的数据库索引
- [ ] 实现基本的Redis缓存
- [ ] 优化FindOneByKey查询
- [ ] 添加查询性能监控

### 6.2 第二阶段（中优先级 - 2-3周内）
- [ ] 重构GetUser方法使用JOIN查询
- [ ] 实现用户会话缓存
- [ ] 优化JSON字段处理
- [ ] 添加缓存命中率监控

### 6.3 第三阶段（低优先级 - 1-2月内）
- [ ] 实现读写分离
- [ ] 优化实体层级查询
- [ ] 实现查询结果预聚合
- [ ] 考虑数据库分片方案

## 7. 监控指标

### 7.1 性能指标
- 登录接口响应时间
- 数据库查询耗时
- 缓存命中率
- 数据库连接池使用率

### 7.2 业务指标
- 登录成功率
- 用户并发数
- 错误率统计

## 8. 风险评估

### 8.1 实施风险
- 索引添加可能影响写入性能
- 缓存一致性问题
- 查询逻辑变更可能引入bug

### 8.2 缓解措施
- 在低峰期添加索引
- 实现缓存版本控制
- 充分的单元测试和集成测试
- 灰度发布策略

## 9. 测试计划

### 9.1 性能测试
- 基准测试：记录优化前的性能指标
- 压力测试：模拟高并发登录场景
- 对比测试：优化前后性能对比

### 9.2 功能测试
- 登录功能回归测试
- 权限验证测试
- 缓存一致性测试
