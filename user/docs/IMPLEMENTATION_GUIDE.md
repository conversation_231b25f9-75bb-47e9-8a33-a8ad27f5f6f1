# User模块SQL优化实施指南

## 1. 实施概述

本指南提供了user模块SQL查询优化的详细实施步骤，包括数据库索引优化、查询重构、缓存实现等。

## 2. 实施前准备

### 2.1 环境准备
- [ ] 确保有测试环境进行验证
- [ ] 备份生产数据库
- [ ] 准备Redis缓存服务
- [ ] 安装性能监控工具

### 2.2 基线测试
```bash
# 执行基线性能测试
go test -v ./user/docs -run TestLoginPerformance
```

## 3. 第一阶段：数据库索引优化（1周内完成）

### 3.1 执行索引创建脚本
```bash
# 在测试环境执行
mysql -u username -p database_name < user/docs/database_indexes.sql

# 验证索引创建
mysql -u username -p -e "SHOW INDEX FROM user; SHOW INDEX FROM user_role;"
```

### 3.2 验证索引效果
```sql
-- 检查登录查询的执行计划
EXPLAIN SELECT * FROM user WHERE `key` = '<EMAIL>' AND is_deleted = 0;

-- 检查用户角色查询的执行计划
EXPLAIN SELECT * FROM user_role WHERE user_id = 1 AND status = 1;
```

### 3.3 监控索引性能
```sql
-- 查看慢查询日志
SELECT * FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- 查看索引使用情况
SELECT * FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE OBJECT_SCHEMA = 'your_database_name';
```

## 4. 第二阶段：查询优化（2-3周内完成）

### 4.1 部署优化后的DAO层
```go
// 在user/mysql/dao.go中添加
func NewOptimizedDao(conn sqlx.SqlConn) *OptimizedDao {
    return &OptimizedDao{
        conn: conn,
        OptimizedUserDao: &OptimizedUserDao{conn: conn},
    }
}
```

### 4.2 更新服务层
```go
// 在api/service中集成优化后的认证服务
func NewServices(c config.Config) *Services {
    // ... 现有代码
    
    optimizedDao := mysql.NewOptimizedDao(sqlConn)
    optimizedAuth := service.NewOptimizedAuthService(jwt, userSrv, rds, optimizedDao.OptimizedUserDao)
    
    return &Services{
        Auth: optimizedAuth,
        // ... 其他服务
    }
}
```

### 4.3 A/B测试配置
```yaml
# 在配置文件中添加特性开关
Features:
  OptimizedAuth: true
  CacheEnabled: true
  PerformanceLogging: true
```

## 5. 第三阶段：缓存实现（2-3周内完成）

### 5.1 Redis配置
```yaml
# config.yaml
Redis:
  Host: "localhost:6379"
  Type: "node"
  Pass: ""
  DB: 0
  MaxRetries: 3
  PoolSize: 10
```

### 5.2 缓存集成
```go
// 在main.go中初始化缓存
rds := redis.MustNewRedis(c.Redis)
userCache := cache.NewUserCache(rds)
```

### 5.3 缓存预热脚本
```go
// scripts/cache_warmup.go
func WarmupUserCache(ctx context.Context, userSrv *service.UserService) error {
    // 获取活跃用户列表
    activeUsers, err := userSrv.GetActiveUsers(ctx, 1000)
    if err != nil {
        return err
    }
    
    // 预热缓存
    for _, user := range activeUsers {
        userSrv.PreloadUserCache(ctx, user.ID)
    }
    
    return nil
}
```

## 6. 性能测试与验证

### 6.1 单元测试
```bash
# 运行性能测试
go test -v ./user/docs -run TestPerformance -timeout 30m

# 运行并发测试
go test -v ./user/docs -run TestConcurrent -timeout 30m
```

### 6.2 压力测试
```bash
# 使用wrk进行压力测试
wrk -t12 -c400 -d30s --script=login.lua http://localhost:8080/login

# login.lua脚本内容
wrk.method = "POST"
wrk.body = '{"email":"<EMAIL>","password":"password123"}'
wrk.headers["Content-Type"] = "application/json"
```

### 6.3 监控指标
```bash
# 监控数据库性能
mysqladmin -u root -p processlist
mysqladmin -u root -p extended-status | grep -i select

# 监控Redis性能
redis-cli info stats
redis-cli info memory
```

## 7. 生产环境部署

### 7.1 灰度发布策略
```yaml
# 部署配置
Deployment:
  Strategy: "canary"
  CanaryPercentage: 10  # 开始时只有10%流量使用优化版本
  RolloutDuration: "24h"
  HealthCheckInterval: "30s"
```

### 7.2 回滚计划
```bash
# 如果出现问题，快速回滚
kubectl rollout undo deployment/hotel-api

# 或者通过特性开关回滚
curl -X POST http://admin-api/features/OptimizedAuth/disable
```

### 7.3 监控告警
```yaml
# 告警规则
alerts:
  - name: "login_response_time_high"
    condition: "avg_response_time > 100ms"
    action: "notify_team"
  
  - name: "cache_hit_rate_low"
    condition: "cache_hit_rate < 80%"
    action: "investigate"
  
  - name: "login_error_rate_high"
    condition: "error_rate > 5%"
    action: "rollback"
```

## 8. 性能目标与验收标准

### 8.1 性能目标
| 指标 | 优化前 | 优化后目标 | 验收标准 |
|------|--------|------------|----------|
| 登录响应时间 | ~200ms | <50ms | P95 < 100ms |
| 登录QPS | ~50 | >200 | 峰值 >500 |
| 缓存命中率 | 0% | >80% | 稳定 >75% |
| 数据库连接数 | 高 | 降低50% | 峰值 <100 |

### 8.2 验收测试
```bash
# 执行完整的验收测试套件
go test -v ./user/acceptance -tags=acceptance

# 性能回归测试
go test -v ./user/performance -bench=. -benchtime=30s
```

## 9. 运维监控

### 9.1 关键指标监控
```sql
-- 创建监控视图
CREATE VIEW user_performance_metrics AS
SELECT 
    DATE(create_time) as date,
    COUNT(*) as daily_logins,
    AVG(response_time) as avg_response_time
FROM audit_log 
WHERE action_type = 'LOGIN'
GROUP BY DATE(create_time);
```

### 9.2 日志配置
```yaml
# logrus配置
Logging:
  Level: "info"
  Format: "json"
  Fields:
    - "user_id"
    - "response_time"
    - "cache_hit"
    - "query_count"
```

### 9.3 定期维护
```bash
# 每周执行的维护脚本
#!/bin/bash

# 1. 分析慢查询
mysql -e "SELECT * FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 7 DAY);"

# 2. 清理过期缓存
redis-cli EVAL "return redis.call('del', unpack(redis.call('keys', 'user:*')))" 0

# 3. 优化表
mysql -e "OPTIMIZE TABLE user, user_role, entity;"

# 4. 更新统计信息
mysql -e "ANALYZE TABLE user, user_role, entity;"
```

## 10. 故障排查

### 10.1 常见问题
1. **缓存穿透**：大量请求不存在的用户
   - 解决：实现空值缓存，TTL设置较短

2. **缓存雪崩**：缓存同时失效
   - 解决：设置随机TTL，实现缓存预热

3. **数据库连接池耗尽**：高并发时连接不够
   - 解决：调整连接池大小，实现连接复用

### 10.2 性能调优
```sql
-- 查看当前连接状态
SHOW PROCESSLIST;

-- 查看InnoDB状态
SHOW ENGINE INNODB STATUS;

-- 查看表锁状态
SHOW OPEN TABLES WHERE In_use > 0;
```

## 11. 后续优化计划

### 11.1 短期优化（1-2月）
- [ ] 实现读写分离
- [ ] 优化JSON字段查询
- [ ] 实现分布式缓存

### 11.2 长期规划（3-6月）
- [ ] 考虑数据库分片
- [ ] 实现CQRS模式
- [ ] 微服务拆分

## 12. 总结

通过本次优化，预期可以实现：
- 登录响应时间降低75%
- 系统QPS提升4倍
- 数据库负载降低50%
- 用户体验显著提升

关键成功因素：
1. 充分的测试验证
2. 渐进式部署策略
3. 完善的监控体系
4. 快速回滚能力
