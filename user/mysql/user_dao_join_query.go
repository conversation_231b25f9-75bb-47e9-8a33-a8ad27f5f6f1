package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"hotel/common/types"
	"hotel/user/domain"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// UserWithRoleEntity 优化查询的结果结构
type UserWithRoleEntity struct {
	// User fields
	UserID         int64     `db:"user_id"`
	Username       string    `db:"username"`
	Key            string    `db:"key"`
	Secret         string    `db:"secret"`
	UserProfile    string    `db:"user_profile"`
	UserCreateTime time.Time `db:"user_create_time"`

	// UserRole fields
	UserRoleID     sql.NullInt64 `db:"user_role_id"`
	RoleID         sql.NullInt64 `db:"role_id"`
	EntityID       sql.NullInt64 `db:"entity_id"`
	UserRoleStatus sql.NullInt64 `db:"user_role_status"`
	ExpireTime     sql.NullTime  `db:"expire_time"`

	// Role fields
	RoleName        sql.NullString `db:"role_name"`
	RoleScope       sql.NullString `db:"role_scope"`
	RolePrivileges  sql.NullString `db:"role_privileges"`
	RoleDescription sql.NullString `db:"role_desc"`

	// Entity fields
	EntityType         sql.NullInt64  `db:"entity_type"`
	EntityName         sql.NullString `db:"entity_name"`
	RootEntityID       sql.NullInt64  `db:"root_entity_id"`
	ParentEntityID     sql.NullInt64  `db:"parent_entity_id"`
	EntityProfile      sql.NullString `db:"entity_profile"`
	DistributionConfig sql.NullString `db:"distribution_config"`
	FinanceConfig      sql.NullString `db:"finance_config"`
	SupplierConfig     sql.NullString `db:"supplier_config"`

	// EntityUserLink fields
	LinkStatus          sql.NullInt64  `db:"link_status"`
	SellerInNodeRuleID  sql.NullInt64  `db:"seller_in_node_rule_id"`
	SellerOutNodeRuleID sql.NullInt64  `db:"seller_out_node_rule_id"`
	BuyerInNodeRuleID   sql.NullInt64  `db:"buyer_in_node_rule_id"`
	BuyerOutNodeRuleID  sql.NullInt64  `db:"buyer_out_node_rule_id"`
	RateLimit           sql.NullString `db:"rate_limit"`
}

// GetUserOptimized 优化后的用户查询方法 - 使用单个JOIN查询
func (d *Dao) GetUserOptimized(ctx context.Context, userID int64) (*domain.User, error) {
	query := `
		SELECT 
			u.id as user_id, u.username, u.key, u.secret, u.profile as user_profile,
			u.create_time as user_create_time,
			ur.id as user_role_id, ur.role_id, ur.entity_id, ur.status as user_role_status,
			ur.expire_time,
			r.name as role_name, r.scope as role_scope, r.privileges as role_privileges, 
			r.description as role_desc,
			e.type as entity_type, e.name as entity_name,
			e.root_entity_id, e.parent_entity_id, e.profile as entity_profile,
			e.distribution_config, e.finance_config, e.supplier_config,
			eul.status as link_status, eul.seller_in_node_rule_id, eul.seller_out_node_rule_id,
			eul.buyer_in_node_rule_id, eul.buyer_out_node_rule_id, eul.rate_limit
		FROM user u
		LEFT JOIN user_role ur ON u.id = ur.user_id AND ur.status = 1
		LEFT JOIN role r ON ur.role_id = r.id AND r.is_deleted = 0
		LEFT JOIN entity e ON ur.entity_id = e.id AND e.is_deleted = 0
		LEFT JOIN entity_user_link eul ON u.id = eul.user_id AND ur.entity_id = eul.entity_id
		WHERE u.id = ? AND u.is_deleted = 0
	`

	var results []UserWithRoleEntity
	err := d.conn.QueryRowsCtx(ctx, &results, query, userID)
	if err != nil {
		return nil, err
	}

	if len(results) == 0 {
		return nil, sqlx.ErrNotFound
	}

	return d.convertToUser(results), nil
}

// GetUserByKeyOptimized 优化后的根据key查询用户方法
func (d *Dao) GetUserByKeyOptimized(ctx context.Context, key string) (*domain.User, error) {
	// 首先快速查询用户基本信息
	userQuery := `
		SELECT id, username, ` + "`key`" + `, secret, profile, create_time
		FROM user
		WHERE ` + "`key`" + ` = ? AND is_deleted = 0
		LIMIT 1
	`

	var user struct {
		ID         int64     `db:"id"`
		Username   string    `db:"username"`
		Key        string    `db:"key"`
		Secret     string    `db:"secret"`
		Profile    string    `db:"profile"`
		CreateTime time.Time `db:"create_time"`
	}

	err := d.conn.QueryRowCtx(ctx, &user, userQuery, key)
	if err != nil {
		return nil, err
	}

	// 然后获取完整的用户信息
	return d.GetUserOptimized(ctx, user.ID)
}

// GetUserBasicByKey 仅获取用户基本信息（用于登录验证）
func (d *Dao) GetUserBasicByKey(ctx context.Context, key string) (*domain.UserBasic, error) {
	query := `
		SELECT id, username, ` + "`key`" + `, secret, profile, create_time
		FROM user
		WHERE ` + "`key`" + ` = ? AND is_deleted = 0
		LIMIT 1
	`

	var result struct {
		ID         int64     `db:"id"`
		Username   string    `db:"username"`
		Key        string    `db:"key"`
		Secret     string    `db:"secret"`
		Profile    string    `db:"profile"`
		CreateTime time.Time `db:"create_time"`
	}

	err := d.conn.QueryRowCtx(ctx, &result, query, key)
	if err != nil {
		return nil, err
	}

	var profile *domain.UserProfile
	if result.Profile != "" {
		if err := json.Unmarshal([]byte(result.Profile), &profile); err != nil {
			// 如果JSON解析失败，记录日志但不中断流程
			profile = nil
		}
	}

	return &domain.UserBasic{
		ID:         types.ID(result.ID),
		Key:        result.Key,
		Username:   result.Username,
		Secret:     result.Secret,
		Profile:    profile,
		CreateTime: result.CreateTime,
	}, nil
}

// GetUsersByEntityIDsOptimized 优化后的根据实体ID批量查询用户
func (d *Dao) GetUsersByEntityIDsOptimized(ctx context.Context, entityIDs []int64) ([]*domain.User, error) {
	if len(entityIDs) == 0 {
		return nil, nil
	}

	// 构建IN查询
	query := `
		SELECT DISTINCT
			u.id as user_id, u.username, u.key, u.secret, u.profile as user_profile,
			u.create_time as user_create_time,
			ur.id as user_role_id, ur.role_id, ur.entity_id, ur.status as user_role_status,
			ur.expire_time,
			r.name as role_name, r.scope as role_scope, r.privileges as role_privileges, 
			r.description as role_desc,
			e.type as entity_type, e.name as entity_name,
			e.root_entity_id, e.parent_entity_id, e.profile as entity_profile,
			e.distribution_config, e.finance_config, e.supplier_config,
			eul.status as link_status, eul.seller_in_node_rule_id, eul.seller_out_node_rule_id,
			eul.buyer_in_node_rule_id, eul.buyer_out_node_rule_id, eul.rate_limit
		FROM user u
		INNER JOIN user_role ur ON u.id = ur.user_id AND ur.status = 1
		LEFT JOIN role r ON ur.role_id = r.id AND r.is_deleted = 0
		LEFT JOIN entity e ON ur.entity_id = e.id AND e.is_deleted = 0
		LEFT JOIN entity_user_link eul ON u.id = eul.user_id AND ur.entity_id = eul.entity_id
		WHERE ur.entity_id IN (` + d.buildInClause(len(entityIDs)) + `) 
		AND u.is_deleted = 0
		ORDER BY u.id, ur.entity_id
	`

	args := make([]interface{}, len(entityIDs))
	for i, id := range entityIDs {
		args[i] = id
	}

	var results []UserWithRoleEntity
	err := d.conn.QueryRowsCtx(ctx, &results, query, args...)
	if err != nil {
		return nil, err
	}

	return d.convertToUsers(results), nil
}

// convertToUser 将查询结果转换为domain.User
func (d *Dao) convertToUser(results []UserWithRoleEntity) *domain.User {
	if len(results) == 0 {
		return nil
	}

	first := results[0]

	// 构建用户基本信息
	var userProfile *domain.UserProfile
	if first.UserProfile != "" {
		json.Unmarshal([]byte(first.UserProfile), &userProfile)
	}

	userBasic := &domain.UserBasic{
		ID:         types.ID(first.UserID),
		Key:        first.Key,
		Username:   first.Username,
		Secret:     first.Secret,
		Profile:    userProfile,
		CreateTime: first.UserCreateTime,
	}

	// 构建用户实体连接
	var connections []*domain.UserEntityConnection
	entityMap := make(map[int64]*domain.UserEntityConnection)

	for _, result := range results {
		if !result.EntityID.Valid {
			continue
		}

		entityID := result.EntityID.Int64

		// 如果实体连接不存在，创建新的
		if _, exists := entityMap[entityID]; !exists {
			var entityProfile domain.EntityProfile
			if result.EntityProfile.Valid && result.EntityProfile.String != "" {
				json.Unmarshal([]byte(result.EntityProfile.String), &entityProfile)
			}

			entity := &domain.Entity{
				ID:             types.ID(entityID),
				Type:           domain.EntityType(result.EntityType.Int64),
				Name:           result.EntityName.String,
				RootEntityID:   types.ID(result.RootEntityID.Int64),
				ParentEntityID: types.ID(result.ParentEntityID.Int64),
				Profile:        entityProfile,
			}

			connection := &domain.UserEntityConnection{
				Entity: entity,
				Roles:  []*domain.UserRole{},
			}

			entityMap[entityID] = connection
			connections = append(connections, connection)
		}

		// 添加角色信息
		if result.RoleID.Valid {
			var privileges []domain.PrivilegeCode
			if result.RolePrivileges.Valid && result.RolePrivileges.String != "" {
				json.Unmarshal([]byte(result.RolePrivileges.String), &privileges)
			}

			role := &domain.Role{
				ID:         result.RoleID.Int64,
				Name:       result.RoleName.String,
				Scope:      domain.Scope(result.RoleScope.String),
				Privileges: privileges,
			}

			userRole := &domain.UserRole{
				Role: role,
			}

			entityMap[entityID].Roles = append(entityMap[entityID].Roles, userRole)
		}
	}

	return &domain.User{
		UserBasic:             userBasic,
		UserEntityConnections: connections,
	}
}

// convertToUsers 将查询结果转换为多个domain.User
func (d *Dao) convertToUsers(results []UserWithRoleEntity) []*domain.User {
	userMap := make(map[int64][]UserWithRoleEntity)

	// 按用户ID分组
	for _, result := range results {
		userID := result.UserID
		userMap[userID] = append(userMap[userID], result)
	}

	var users []*domain.User
	for _, userResults := range userMap {
		if user := d.convertToUser(userResults); user != nil {
			users = append(users, user)
		}
	}

	return users
}

// buildInClause 构建IN查询的占位符
func (d *Dao) buildInClause(count int) string {
	if count == 0 {
		return ""
	}

	result := "?"
	for i := 1; i < count; i++ {
		result += ",?"
	}
	return result
}

// GetUserRoleCountByEntityID 获取实体下的用户角色数量（用于分页）
func (d *Dao) GetUserRoleCountByEntityID(ctx context.Context, entityID int64) (int64, error) {
	query := `
		SELECT COUNT(DISTINCT ur.user_id)
		FROM user_role ur
		INNER JOIN user u ON ur.user_id = u.id
		WHERE ur.entity_id = ? AND ur.status = 1 AND u.is_deleted = 0
	`

	var count int64
	err := d.conn.QueryRowCtx(ctx, &count, query, entityID)
	return count, err
}

// GetUsersByEntityIDWithPagination 分页查询实体下的用户
func (d *Dao) GetUsersByEntityIDWithPagination(ctx context.Context, entityID int64, offset, limit int) ([]*domain.User, error) {
	query := `
		SELECT 
			u.id as user_id, u.username, u.key, u.secret, u.profile as user_profile,
			u.create_time as user_create_time,
			ur.id as user_role_id, ur.role_id, ur.entity_id, ur.status as user_role_status,
			ur.expire_time,
			r.name as role_name, r.scope as role_scope, r.privileges as role_privileges, 
			r.description as role_desc,
			e.type as entity_type, e.name as entity_name,
			e.root_entity_id, e.parent_entity_id, e.profile as entity_profile,
			e.distribution_config, e.finance_config, e.supplier_config,
			eul.status as link_status, eul.seller_in_node_rule_id, eul.seller_out_node_rule_id,
			eul.buyer_in_node_rule_id, eul.buyer_out_node_rule_id, eul.rate_limit
		FROM user u
		INNER JOIN user_role ur ON u.id = ur.user_id AND ur.status = 1
		LEFT JOIN role r ON ur.role_id = r.id AND r.is_deleted = 0
		LEFT JOIN entity e ON ur.entity_id = e.id AND e.is_deleted = 0
		LEFT JOIN entity_user_link eul ON u.id = eul.user_id AND ur.entity_id = eul.entity_id
		WHERE ur.entity_id = ? AND u.is_deleted = 0
		ORDER BY u.id
		LIMIT ? OFFSET ?
	`

	var results []UserWithRoleEntity
	err := d.conn.QueryRowsCtx(ctx, &results, query, entityID, limit, offset)
	if err != nil {
		return nil, err
	}

	return d.convertToUsers(results), nil
}
