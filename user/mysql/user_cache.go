package mysql

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/bytedance/sonic"
	"github.com/spf13/cast"
	"time"

	"hotel/common/types"
	"hotel/user/domain"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

// 缓存键前缀
const (
	UserCacheKeyPrefix   = "user:"
	UserKey2IdPrefix     = "user_key2id:"
	UserBasicKeyPrefix   = "user_basic:"
	UserRoleKeyPrefix    = "user_role:"
	EntityKeyPrefix      = "entity:"
	UserSessionKeyPrefix = "user_session:"

	// 缓存TTL
	UserCacheTTL      = 30 * time.Minute
	UserBasicCacheTTL = 15 * time.Minute
	UserRoleCacheTTL  = 15 * time.Minute
	EntityCacheTTL    = 60 * time.Minute
	UserSessionTTL    = 24 * time.Hour
)

type UserCache struct {
	rds *redis.Redis
}

// NewUserCache 创建用户缓存实例
func NewUserCache(rds *redis.Redis) *UserCache {
	return &UserCache{
		rds: rds,
	}
}

// CachedUserBasic 缓存的用户基本信息结构
type CachedUserBasic struct {
	User     *domain.UserBasic `json:"user"`
	CachedAt time.Time         `json:"cached_at"`
}

// CachedUser 缓存的完整用户信息结构
type CachedUser struct {
	User     *domain.User `json:"user"`
	CachedAt time.Time    `json:"cached_at"`
}

// CachedEntity 缓存的实体信息结构
type CachedEntity struct {
	Entity   *domain.Entity `json:"entity"`
	CachedAt time.Time      `json:"cached_at"`
}

func (c *UserCache) UserKey2Id(ctx context.Context, key string) (types.ID, error) {
	cacheKey := UserKey2IdPrefix + key
	val, err := c.rds.GetCtx(ctx, cacheKey)
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	return types.ID(cast.ToInt64(val)), nil
}

// GetUserBasic 获取用户基本信息缓存
func (c *UserCache) GetUserBasic(ctx context.Context, key string) (*domain.UserBasic, error) {
	cacheKey := UserBasicKeyPrefix + key

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedUserBasic
	if err = sonic.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	// 检查缓存是否过期（双重保险）
	if time.Since(cached.CachedAt) > UserBasicCacheTTL {
		_, _ = c.rds.DelCtx(ctx, cacheKey)
		return nil, redis.Nil
	}

	return cached.User, nil
}

// SetUserBasic 设置用户基本信息缓存
func (c *UserCache) SetUserBasic(ctx context.Context, key string, user *domain.UserBasic) error {
	cacheKey := UserBasicKeyPrefix + key

	cached := CachedUserBasic{
		User:     user,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(UserBasicCacheTTL.Seconds()))
}

// DelUserBasic 删除用户基本信息缓存
func (c *UserCache) DelUserBasic(ctx context.Context, key string) error {
	cacheKey := UserBasicKeyPrefix + key
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// GetUser 获取完整用户信息缓存
func (c *UserCache) GetUser(ctx context.Context, userID types.ID) (*domain.User, error) {
	cacheKey := UserCacheKeyPrefix + userID.String()

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedUser
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	// 检查缓存是否过期
	if time.Since(cached.CachedAt) > UserCacheTTL {
		_, _ = c.rds.DelCtx(ctx, cacheKey)
		return nil, nil
	}

	return cached.User, nil
}
func (c *UserCache) GetUserByKey(ctx context.Context, key string) (*domain.User, error) {
	userID, err := c.UserKey2Id(ctx, key)
	if err != nil {
		return nil, err
	}
	if userID == 0 {
		return nil, nil
	}
	return c.GetUser(ctx, userID)
}

// SetUser 设置完整用户信息缓存
func (c *UserCache) SetUser(ctx context.Context, userID types.ID, user *domain.User) error {
	cacheKey := UserCacheKeyPrefix + userID.String()

	cached := CachedUser{
		User:     user,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(UserCacheTTL.Seconds()))
}

// DelUser 删除完整用户信息缓存
func (c *UserCache) DelUser(ctx context.Context, userID types.ID) error {
	cacheKey := UserCacheKeyPrefix + userID.String()
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// GetUserSession 获取用户会话缓存
func (c *UserCache) GetUserSession(ctx context.Context, token string) (*domain.User, error) {
	cacheKey := UserSessionKeyPrefix + token

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedUser
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	return cached.User, nil
}

// SetUserSession 设置用户会话缓存
func (c *UserCache) SetUserSession(ctx context.Context, token string, user *domain.User) error {
	cacheKey := UserSessionKeyPrefix + token

	cached := CachedUser{
		User:     user,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(UserSessionTTL.Seconds()))
}

// DelUserSession 删除用户会话缓存
func (c *UserCache) DelUserSession(ctx context.Context, token string) error {
	cacheKey := UserSessionKeyPrefix + token
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// GetEntity 获取实体信息缓存
func (c *UserCache) GetEntity(ctx context.Context, entityID types.ID) (*domain.Entity, error) {
	cacheKey := EntityKeyPrefix + entityID.String()

	val, err := c.rds.GetCtx(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var cached CachedEntity
	if err := json.Unmarshal([]byte(val), &cached); err != nil {
		return nil, err
	}

	// 检查缓存是否过期
	if time.Since(cached.CachedAt) > EntityCacheTTL {
		_, _ = c.rds.DelCtx(ctx, cacheKey)
		return nil, redis.Nil
	}

	return cached.Entity, nil
}

// SetEntity 设置实体信息缓存
func (c *UserCache) SetEntity(ctx context.Context, entityID types.ID, entity *domain.Entity) error {
	cacheKey := EntityKeyPrefix + entityID.String()

	cached := CachedEntity{
		Entity:   entity,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return err
	}

	return c.rds.SetexCtx(ctx, cacheKey, string(data), int(EntityCacheTTL.Seconds()))
}

// DelEntity 删除实体信息缓存
func (c *UserCache) DelEntity(ctx context.Context, entityID types.ID) error {
	cacheKey := EntityKeyPrefix + entityID.String()
	_, err := c.rds.DelCtx(ctx, cacheKey)
	return err
}

// DelUserRelatedCache 删除用户相关的所有缓存
func (c *UserCache) DelUserRelatedCache(ctx context.Context, userID types.ID) error {
	// 删除用户完整信息缓存
	userKey := UserCacheKeyPrefix + userID.String()

	// 删除用户基本信息缓存（需要先获取用户的key）
	// 这里可能需要额外的查询来获取用户的key，或者维护一个反向映射

	keys := []string{userKey}

	_, err := c.rds.DelCtx(ctx, keys...)
	return err
}

// GetUsersBatch 批量获取用户信息
func (c *UserCache) GetUsersBatch(ctx context.Context, userIDs []types.ID) (map[types.ID]*domain.User, []types.ID, error) {
	found := make(map[types.ID]*domain.User)
	missing := make([]types.ID, 0)

	for _, userID := range userIDs {
		user, err := c.GetUser(ctx, userID)
		if errors.Is(err, redis.Nil) {
			missing = append(missing, userID)
		} else if err != nil {
			return nil, nil, err
		} else {
			found[userID] = user
		}
	}

	return found, missing, nil
}

// SetUsersBatch 批量设置用户信息缓存
func (c *UserCache) SetUsersBatch(ctx context.Context, users map[types.ID]*domain.User) error {
	for userID, user := range users {
		if err := c.SetUser(ctx, userID, user); err != nil {
			return err
		}
	}
	return nil
}
