package api

import (
	"context"
	"fmt"
	"strconv"

	"hotel/common/bizerr"
	"hotel/common/quota"
	ruleMySQL "hotel/rule/mysql"
	userDomain "hotel/user/domain"
)

// QuotaAPI 配额管理 API
type QuotaAPI struct {
	quotaService *quota.QuotaRuleService
}

// NewQuotaAPI 创建配额 API 实例
func NewQuotaAPI(ruleDao *ruleMySQL.Dao) *QuotaAPI {
	return &QuotaAPI{
		quotaService: quota.NewQuotaRuleService(ruleDao),
	}
}

// CreateQuotaRuleRequest 创建配额规则请求
type CreateQuotaRuleRequest struct {
	EntityID     string                `json:"entityId" binding:"required"`
	EntityType   userDomain.EntityType `json:"entityType" binding:"required"`
	QuotaType    string                `json:"quotaType" binding:"required"`
	Limit        int64                 `json:"limit" binding:"required,min=1"`
	Interval     string                `json:"interval" binding:"required"`
	ResourcePath string                `json:"resourcePath"`
}

// CreateQuotaRuleResponse 创建配额规则响应
type CreateQuotaRuleResponse struct {
	Message string `json:"message"`
}

// UpdateQuotaRuleRequest 更新配额规则请求
type UpdateQuotaRuleRequest struct {
	EntityID     string                `json:"entityId" binding:"required"`
	EntityType   userDomain.EntityType `json:"entityType" binding:"required"`
	QuotaType    string                `json:"quotaType" binding:"required"`
	Limit        int64                 `json:"limit" binding:"required,min=1"`
	Interval     string                `json:"interval" binding:"required"`
	ResourcePath string                `json:"resourcePath"`
}

// UpdateQuotaRuleResponse 更新配额规则响应
type UpdateQuotaRuleResponse struct {
	Message string `json:"message"`
}

// GetQuotaRuleRequest 获取配额规则请求
type GetQuotaRuleRequest struct {
	EntityID   string                `json:"entityId" binding:"required"`
	EntityType userDomain.EntityType `json:"entityType" binding:"required"`
	QuotaType  string                `json:"quotaType" binding:"required"`
}

// QuotaRuleResponse 配额规则响应
type QuotaRuleResponse struct {
	QuotaType    string `json:"quotaType"`
	Limit        int64  `json:"limit"`
	Interval     string `json:"interval"`
	ResourcePath string `json:"resourcePath"`
}

// DeleteQuotaRuleRequest 删除配额规则请求
type DeleteQuotaRuleRequest struct {
	EntityID   string                `json:"entityId" binding:"required"`
	EntityType userDomain.EntityType `json:"entityType" binding:"required"`
	QuotaType  string                `json:"quotaType" binding:"required"`
}

// DeleteQuotaRuleResponse 删除配额规则响应
type DeleteQuotaRuleResponse struct {
	Message string `json:"message"`
}

// ListQuotaRulesRequest 列出配额规则请求
type ListQuotaRulesRequest struct {
	EntityID   string                `json:"entityId" binding:"required"`
	EntityType userDomain.EntityType `json:"entityType" binding:"required"`
}

// ListQuotaRulesResponse 列出配额规则响应
type ListQuotaRulesResponse struct {
	Rules []QuotaRuleResponse `json:"rules"`
}

// CreateQuotaRule 创建配额规则
// @Summary 创建配额规则
// @Description 为指定实体创建配额限制规则
// @Tags 配额管理
// @Accept json
// @Produce json
// @Param request body CreateQuotaRuleRequest true "创建配额规则请求"
// @Success 200 {object} httphelper.Response{data=string} "创建成功"
// @Failure 400 {object} httphelper.Response "请求参数错误"
// @Failure 500 {object} httphelper.Response "内部服务器错误"
// @Router /api/v1/quota/rules [post]
func (api *QuotaAPI) CreateQuotaRule(ctx context.Context, req *CreateQuotaRuleRequest) (*CreateQuotaRuleResponse, error) {
	// 验证实体类型
	if !api.isValidEntityType(req.EntityType) {
		return nil, bizerr.NewHTTP(400, "Invalid entity type", 400)
	}

	config := quota.QuotaConfig{
		QuotaType:    req.QuotaType,
		Limit:        req.Limit,
		Interval:     req.Interval,
		ResourcePath: req.ResourcePath,
	}

	err := api.quotaService.CreateQuotaRule(ctx, req.EntityID, req.EntityType, config)
	if err != nil {
		return nil, bizerr.NewHTTP(500, fmt.Sprintf("Failed to create quota rule: %v", err), 500)
	}

	return &CreateQuotaRuleResponse{
		Message: "Quota rule created successfully",
	}, nil
}

// GetQuotaRule 获取配额规则
// @Summary 获取配额规则
// @Description 获取指定实体的配额规则配置
// @Tags 配额管理
// @Accept json
// @Produce json
// @Param entityId path string true "实体ID"
// @Param entityType query string true "实体类型" Enums(Platform,Tenant,Customer,Extranet,Supplier)
// @Param quotaType query string true "配额类型"
// @Success 200 {object} httphelper.Response{data=QuotaRuleResponse} "获取成功"
// @Failure 400 {object} httphelper.Response "请求参数错误"
// @Failure 404 {object} httphelper.Response "配额规则不存在"
// @Failure 500 {object} httphelper.Response "内部服务器错误"
// @Router /api/v1/quota/rules/{entityId} [get]
func (api *QuotaAPI) GetQuotaRule(ctx context.Context, req *GetQuotaRuleRequest) (*QuotaRuleResponse, error) {
	if req.EntityID == "" {
		return nil, bizerr.NewHTTP(400, "Entity ID is required", 400)
	}

	if req.QuotaType == "" {
		return nil, bizerr.NewHTTP(400, "Quota type is required", 400)
	}

	config, err := api.quotaService.GetQuotaConfig(ctx, req.EntityID, req.EntityType, req.QuotaType)
	if err != nil {
		return nil, bizerr.NewHTTP(500, fmt.Sprintf("Failed to get quota rule: %v", err), 500)
	}

	if config == nil {
		return nil, bizerr.NewHTTP(404, "Quota rule not found", 404)
	}

	return &QuotaRuleResponse{
		QuotaType:    config.QuotaType,
		Limit:        config.Limit,
		Interval:     config.Interval,
		ResourcePath: config.ResourcePath,
	}, nil
}

// UpdateQuotaRule 更新配额规则
// @Summary 更新配额规则
// @Description 更新指定实体的配额规则配置
// @Tags 配额管理
// @Accept json
// @Produce json
// @Param entityId path string true "实体ID"
// @Param entityType query string true "实体类型" Enums(Platform,Tenant,Customer,Extranet,Supplier)
// @Param request body UpdateQuotaRuleRequest true "更新配额规则请求"
// @Success 200 {object} httphelper.Response{data=string} "更新成功"
// @Failure 400 {object} httphelper.Response "请求参数错误"
// @Failure 500 {object} httphelper.Response "内部服务器错误"
// @Router /api/v1/quota/rules/{entityId} [put]
func (api *QuotaAPI) UpdateQuotaRule(ctx context.Context, req *UpdateQuotaRuleRequest) (*UpdateQuotaRuleResponse, error) {
	if req.EntityID == "" {
		return nil, bizerr.NewHTTP(400, "Entity ID is required", 400)
	}

	if !api.isValidEntityType(req.EntityType) {
		return nil, bizerr.NewHTTP(400, "Invalid entity type", 400)
	}

	config := quota.QuotaConfig{
		QuotaType:    req.QuotaType,
		Limit:        req.Limit,
		Interval:     req.Interval,
		ResourcePath: req.ResourcePath,
	}

	err := api.quotaService.UpdateQuotaRule(ctx, req.EntityID, req.EntityType, config)
	if err != nil {
		return nil, bizerr.NewHTTP(500, fmt.Sprintf("Failed to update quota rule: %v", err), 500)
	}

	return &UpdateQuotaRuleResponse{
		Message: "Quota rule updated successfully",
	}, nil
}

// DeleteQuotaRule 删除配额规则
// @Summary 删除配额规则
// @Description 删除指定实体的配额规则
// @Tags 配额管理
// @Accept json
// @Produce json
// @Param entityId path string true "实体ID"
// @Param entityType query string true "实体类型" Enums(Platform,Tenant,Customer,Extranet,Supplier)
// @Param quotaType query string true "配额类型"
// @Success 200 {object} httphelper.Response{data=string} "删除成功"
// @Failure 400 {object} httphelper.Response "请求参数错误"
// @Failure 500 {object} httphelper.Response "内部服务器错误"
// @Router /api/v1/quota/rules/{entityId} [delete]
func (api *QuotaAPI) DeleteQuotaRule(ctx context.Context, req *DeleteQuotaRuleRequest) (*DeleteQuotaRuleResponse, error) {
	if req.EntityID == "" {
		return nil, bizerr.NewHTTP(400, "Entity ID is required", 400)
	}

	if req.QuotaType == "" {
		return nil, bizerr.NewHTTP(400, "Quota type is required", 400)
	}

	if !api.isValidEntityType(req.EntityType) {
		return nil, bizerr.NewHTTP(400, "Invalid entity type", 400)
	}

	err := api.quotaService.DeleteQuotaRule(ctx, req.EntityID, req.EntityType, req.QuotaType)
	if err != nil {
		return nil, bizerr.NewHTTP(500, fmt.Sprintf("Failed to delete quota rule: %v", err), 500)
	}

	return &DeleteQuotaRuleResponse{
		Message: "Quota rule deleted successfully",
	}, nil
}

// ListQuotaRules 列出配额规则
// @Summary 列出配额规则
// @Description 列出指定实体的所有配额规则
// @Tags 配额管理
// @Accept json
// @Produce json
// @Param entityId path string true "实体ID"
// @Param entityType query string true "实体类型" Enums(Platform,Tenant,Customer,Extranet,Supplier)
// @Success 200 {object} httphelper.Response{data=[]QuotaRuleResponse} "获取成功"
// @Failure 400 {object} httphelper.Response "请求参数错误"
// @Failure 500 {object} httphelper.Response "内部服务器错误"
// @Router /api/v1/quota/rules/{entityId}/list [get]
func (api *QuotaAPI) ListQuotaRules(ctx context.Context, req *ListQuotaRulesRequest) (*ListQuotaRulesResponse, error) {
	if req.EntityID == "" {
		return nil, bizerr.NewHTTP(400, "Entity ID is required", 400)
	}

	if !api.isValidEntityType(req.EntityType) {
		return nil, bizerr.NewHTTP(400, "Invalid entity type", 400)
	}

	configs, err := api.quotaService.ListQuotaRules(ctx, req.EntityID, req.EntityType)
	if err != nil {
		return nil, bizerr.NewHTTP(500, fmt.Sprintf("Failed to list quota rules: %v", err), 500)
	}

	var responses []QuotaRuleResponse
	for _, config := range configs {
		responses = append(responses, QuotaRuleResponse{
			QuotaType:    config.QuotaType,
			Limit:        config.Limit,
			Interval:     config.Interval,
			ResourcePath: config.ResourcePath,
		})
	}

	return &ListQuotaRulesResponse{
		Rules: responses,
	}, nil
}

// isValidEntityType 验证实体类型是否有效
func (api *QuotaAPI) isValidEntityType(entityType userDomain.EntityType) bool {
	validTypes := []userDomain.EntityType{
		userDomain.EntityTypePlatform,
		userDomain.EntityTypeTenant,
		userDomain.EntityTypeCustomer,
		userDomain.EntityTypeExtranet,
		userDomain.EntityTypeSupplier,
	}

	for _, validType := range validTypes {
		if entityType.HasType(validType) {
			return true
		}
	}
	return false
}

// parseEntityType 解析实体类型字符串
func (api *QuotaAPI) parseEntityType(entityTypeStr string) (userDomain.EntityType, error) {
	switch entityTypeStr {
	case "Platform":
		return userDomain.EntityTypePlatform, nil
	case "Tenant":
		return userDomain.EntityTypeTenant, nil
	case "Customer":
		return userDomain.EntityTypeCustomer, nil
	case "Extranet":
		return userDomain.EntityTypeExtranet, nil
	case "Supplier":
		return userDomain.EntityTypeSupplier, nil
	default:
		// 尝试解析为数字
		if entityTypeInt, err := strconv.ParseUint(entityTypeStr, 10, 64); err == nil {
			return userDomain.EntityType(entityTypeInt), nil
		}
		return 0, fmt.Errorf("invalid entity type: %s", entityTypeStr)
	}
}