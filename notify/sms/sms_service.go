package sms

import (
	"context"
	"fmt"

	"hotel/notify/domain"
)

// SMSService 短信服务
type SMSService struct {
	// 这里可以集成具体的短信服务提供商，如阿里云、腾讯云等
	provider string
	config   *SMSConfig
}

// SMSConfig 短信配置
type SMSConfig struct {
	Provider    string `json:"provider"`     // 服务提供商
	AccessKeyID string `json:"access_key_id"` // 访问密钥ID
	SecretKey   string `json:"secret_key"`    // 密钥
	Region      string `json:"region"`       // 区域
	SignName    string `json:"sign_name"`    // 签名
}

// NewSMSService 创建短信服务
func NewSMSService(config *SMSConfig) *SMSService {
	return &SMSService{
		provider: config.Provider,
		config:   config,
	}
}

// SendSMS 发送短信
func (s *SMSService) SendSMS(ctx context.Context, req *domain.SMSRequest) error {
	// 参数验证
	if req == nil {
		return fmt.Errorf("SMS request cannot be nil")
	}
	if req.To == "" {
		return fmt.Errorf("recipient phone number is required")
	}
	if req.Content == "" {
		return fmt.Errorf("SMS content is required")
	}

	// 这里是模拟实现，实际项目中需要集成真实的短信服务
	// 例如：阿里云短信、腾讯云短信、AWS SNS等
	fmt.Printf("[SMS] Sending to %s: %s\n", req.To, req.Content)

	// 模拟发送成功
	return nil
}

// ValidatePhoneNumber 验证手机号格式
func (s *SMSService) ValidatePhoneNumber(phone string) bool {
	// 简单的手机号验证，实际项目中可以使用更复杂的正则表达式
	if len(phone) < 10 || len(phone) > 15 {
		return false
	}
	
	// 检查是否全为数字（简化实现）
	for _, char := range phone {
		if char < '0' || char > '9' {
			// 允许 + 号开头（国际号码）
			if char != '+' || phone[0] != '+' {
				return false
			}
		}
	}
	
	return true
}