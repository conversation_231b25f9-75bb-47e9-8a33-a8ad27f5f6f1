package mysql

import (
	"context"
	"database/sql"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// Notification 通知记录
type Notification struct {
	Id           int64     `db:"id"`
	Type         string    `db:"type"`          // 通知类型：email, sms, push
	Scenario     string    `db:"scenario"`      // 通知场景
	Recipient    string    `db:"recipient"`     // 接收者
	Subject      string    `db:"subject"`       // 主题
	Content      string    `db:"content"`       // 内容
	Status       string    `db:"status"`        // 状态：pending, sent, failed
	ErrorMessage string    `db:"error_message"` // 错误信息
	CreatedAt    time.Time `db:"created_at"`
	UpdatedAt    time.Time `db:"updated_at"`
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	Id          int64     `db:"id"`
	Type        string    `db:"type"`        // 通知类型
	Scenario    string    `db:"scenario"`    // 场景
	Name        string    `db:"name"`        // 模板名称
	Description string    `db:"description"` // 模板描述
	Subject     string    `db:"subject"`     // 主题模板
	Content     string    `db:"content"`     // 内容模板
	CreatedAt   time.Time `db:"created_at"`
	UpdatedAt   time.Time `db:"updated_at"`
}

// NotificationHistory 通知历史
type NotificationHistory struct {
	Id             int64     `db:"id"`
	NotificationId int64     `db:"notification_id"` // 关联的通知ID
	Status         string    `db:"status"`          // 发送状态
	ErrorMessage   string    `db:"error_message"`   // 错误信息
	SentAt         time.Time `db:"sent_at"`         // 发送时间
	CreatedAt      time.Time `db:"created_at"`
}

// NotificationDao 通知数据访问对象
type NotificationDao struct {
	conn sqlx.SqlConn
}

// NewNotificationDao 创建通知DAO
func NewNotificationDao(conn sqlx.SqlConn) *NotificationDao {
	return &NotificationDao{
		conn: conn,
	}
}

// Insert 插入通知记录
func (d *NotificationDao) Insert(ctx context.Context, notification *Notification) (sql.Result, error) {
	query := `INSERT INTO notifications (type, scenario, recipient, subject, content, status, error_message, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`
	return d.conn.ExecCtx(ctx, query,
		notification.Type,
		notification.Scenario,
		notification.Recipient,
		notification.Subject,
		notification.Content,
		notification.Status,
		notification.ErrorMessage,
		notification.CreatedAt,
		notification.UpdatedAt,
	)
}

// Update 更新通知记录
func (d *NotificationDao) Update(ctx context.Context, notification *Notification) error {
	query := `UPDATE notifications SET status = ?, error_message = ?, updated_at = ? WHERE id = ?`
	_, err := d.conn.ExecCtx(ctx, query,
		notification.Status,
		notification.ErrorMessage,
		notification.UpdatedAt,
		notification.Id,
	)
	return err
}

// FindOne 根据ID查找通知
func (d *NotificationDao) FindOne(ctx context.Context, id int64) (*Notification, error) {
	query := `SELECT id, type, scenario, recipient, subject, content, status, error_message, created_at, updated_at 
			  FROM notifications WHERE id = ?`
	var notification Notification
	err := d.conn.QueryRowCtx(ctx, &notification, query, id)
	if err != nil {
		return nil, err
	}
	return &notification, nil
}

// TemplateDao 模板数据访问对象
type TemplateDao struct {
	conn sqlx.SqlConn
}

// NewTemplateDao 创建模板DAO
func NewTemplateDao(conn sqlx.SqlConn) *TemplateDao {
	return &TemplateDao{
		conn: conn,
	}
}

// Insert 插入模板
func (d *TemplateDao) Insert(ctx context.Context, template *NotificationTemplate) (sql.Result, error) {
	query := `INSERT INTO notification_templates (type, scenario, name, description, subject, content, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
	return d.conn.ExecCtx(ctx, query,
		template.Type,
		template.Scenario,
		template.Name,
		template.Description,
		template.Subject,
		template.Content,
		template.CreatedAt,
		template.UpdatedAt,
	)
}

// Update 更新模板
func (d *TemplateDao) Update(ctx context.Context, template *NotificationTemplate) error {
	query := `UPDATE notification_templates SET name = ?, description = ?, subject = ?, content = ?, updated_at = ? WHERE id = ?`
	_, err := d.conn.ExecCtx(ctx, query,
		template.Name,
		template.Description,
		template.Subject,
		template.Content,
		template.UpdatedAt,
		template.Id,
	)
	return err
}

// FindByTypeAndScenario 根据类型和场景查找模板
func (d *TemplateDao) FindByTypeAndScenario(ctx context.Context, notificationType, scenario string) (*NotificationTemplate, error) {
	query := `SELECT id, type, scenario, name, description, subject, content, created_at, updated_at 
			  FROM notification_templates WHERE type = ? AND scenario = ? LIMIT 1`
	var template NotificationTemplate
	err := d.conn.QueryRowCtx(ctx, &template, query, notificationType, scenario)
	if err != nil {
		return nil, err
	}
	return &template, nil
}

// FindOne 根据ID查找模板
func (d *TemplateDao) FindOne(ctx context.Context, id int64) (*NotificationTemplate, error) {
	query := `SELECT id, type, scenario, name, description, subject, content, created_at, updated_at 
			  FROM notification_templates WHERE id = ?`
	var template NotificationTemplate
	err := d.conn.QueryRowCtx(ctx, &template, query, id)
	if err != nil {
		return nil, err
	}
	return &template, nil
}



// Delete 删除模板
func (d *TemplateDao) Delete(ctx context.Context, id int64) error {
	query := `DELETE FROM notification_templates WHERE id = ?`
	_, err := d.conn.ExecCtx(ctx, query, id)
	return err
}

// HistoryDao 历史记录数据访问对象
type HistoryDao struct {
	conn sqlx.SqlConn
}

// NewHistoryDao 创建历史DAO
func NewHistoryDao(conn sqlx.SqlConn) *HistoryDao {
	return &HistoryDao{
		conn: conn,
	}
}

// Insert 插入历史记录
func (d *HistoryDao) Insert(ctx context.Context, history *NotificationHistory) (sql.Result, error) {
	query := `INSERT INTO notification_histories (notification_id, status, error_message, sent_at, created_at) 
			  VALUES (?, ?, ?, ?, ?)`
	return d.conn.ExecCtx(ctx, query,
		history.NotificationId,
		history.Status,
		history.ErrorMessage,
		history.SentAt,
		history.CreatedAt,
	)
}

// FindByConditions 根据条件查找历史记录
func (d *HistoryDao) FindByConditions(ctx context.Context, req interface{}) ([]*NotificationHistory, int64, error) {
	// 这里简化实现，实际项目中需要根据具体的查询条件构建SQL
	query := `SELECT h.id, h.notification_id, h.status, h.error_message, h.sent_at, h.created_at
			  FROM notification_histories h
			  ORDER BY h.created_at DESC
			  LIMIT 20`
	
	var histories []*NotificationHistory
	err := d.conn.QueryRowsCtx(ctx, &histories, query)
	if err != nil {
		return nil, 0, err
	}
	
	// 简化的总数查询
	countQuery := `SELECT COUNT(*) FROM notification_histories`
	var total int64
	err = d.conn.QueryRowCtx(ctx, &total, countQuery)
	if err != nil {
		return histories, 0, err
	}
	
	return histories, total, nil
}