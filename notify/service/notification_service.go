package service

import (
	"context"
	"fmt"
	"time"

	"hotel/common/bizerr"
	"hotel/notify/domain"
	"hotel/notify/mysql"
)

// NotificationService 通知服务
type NotificationService struct {
	notificationDao *mysql.NotificationDao
	templateDao     *mysql.TemplateDao
	historyDao      *mysql.HistoryDao
	emailService    EmailSender
	smsService      SMSSender
	pushService     PushSender
}

// EmailSender 邮件发送接口
type EmailSender interface {
	SendEmail(ctx context.Context, req *domain.EmailRequest) error
}

// SMSSender 短信发送接口
type SMSSender interface {
	SendSMS(ctx context.Context, req *domain.SMSRequest) error
}

// PushSender 推送发送接口
type PushSender interface {
	SendPush(ctx context.Context, req *domain.PushRequest) error
}

// NewNotificationService 创建通知服务实例
func NewNotificationService(
	notificationDao *mysql.NotificationDao,
	templateDao *mysql.TemplateDao,
	historyDao *mysql.HistoryDao,
	emailService EmailSender,
	smsService SMSSender,
	pushService PushSender,
) *NotificationService {
	return &NotificationService{
		notificationDao: notificationDao,
		templateDao:     templateDao,
		historyDao:      historyDao,
		emailService:    emailService,
		smsService:      smsService,
		pushService:     pushService,
	}
}

// SendNotification 发送通知
func (s *NotificationService) SendNotification(ctx context.Context, req *domain.SendNotificationRequest) (*domain.SendNotificationResponse, error) {
	// 参数验证
	if err := s.ValidateSendRequest(req); err != nil {
		return nil, err
	}

	// 获取通知模板
	template, err := s.templateDao.FindByTypeAndScenario(ctx, req.Type, req.Scenario)
	if err != nil {
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// 渲染模板内容
	content, err := s.renderTemplate(template, req.Variables)
	if err != nil {
		return nil, fmt.Errorf("failed to render template: %w", err)
	}

	// 创建通知记录
	notification := &mysql.Notification{
		Type:      req.Type,
		Scenario:  req.Scenario,
		Recipient: req.Recipient,
		Subject:   content.Subject,
		Content:   content.Body,
		Status:    domain.NotificationStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 保存通知记录
	result, err := s.notificationDao.Insert(ctx, notification)
	if err != nil {
		return nil, fmt.Errorf("failed to save notification: %w", err)
	}

	notificationID, _ := result.LastInsertId()
	notification.Id = notificationID

	// 根据类型发送通知
	var sendErr error
	switch req.Type {
	case domain.NotificationTypeEmail:
		sendErr = s.sendEmail(ctx, notification, req)
	case domain.NotificationTypeSMS:
		sendErr = s.sendSMS(ctx, notification, req)
	case domain.NotificationTypePush:
		sendErr = s.sendPush(ctx, notification, req)
	default:
		sendErr = fmt.Errorf("unsupported notification type: %s", req.Type)
	}

	// 更新发送状态
	status := domain.NotificationStatusSent
	if sendErr != nil {
		status = domain.NotificationStatusFailed
	}

	notification.Status = status
	notification.UpdatedAt = time.Now()
	if sendErr != nil {
		notification.ErrorMessage = sendErr.Error()
	}

	if err := s.notificationDao.Update(ctx, notification); err != nil {
		// 记录更新失败，但不影响主流程
		fmt.Printf("failed to update notification status: %v\n", err)
	}

	// 记录发送历史
	history := &mysql.NotificationHistory{
		NotificationId: notificationID,
		Status:         status,
		SentAt:         time.Now(),
		CreatedAt:      time.Now(),
	}
	if sendErr != nil {
		history.ErrorMessage = sendErr.Error()
	}

	if _, err := s.historyDao.Insert(ctx, history); err != nil {
		// 记录历史失败，但不影响主流程
		fmt.Printf("failed to save notification history: %v\n", err)
	}

	if sendErr != nil {
		return nil, fmt.Errorf("failed to send notification: %w", sendErr)
	}

	return &domain.SendNotificationResponse{
		NotificationID: notificationID,
		Status:         status,
	}, nil
}

// ValidateSendRequest 验证发送请求
func (s *NotificationService) ValidateSendRequest(req *domain.SendNotificationRequest) error {
	if req == nil {
		return bizerr.ParamErr.WithMessage("request cannot be nil")
	}
	if req.Type == "" {
		return bizerr.ParamErr.WithMessage("notification type is required")
	}
	if req.Scenario == "" {
		return bizerr.ParamErr.WithMessage("scenario is required")
	}
	if req.Recipient == "" {
		return bizerr.ParamErr.WithMessage("recipient is required")
	}
	return nil
}

// renderTemplate 渲染模板
func (s *NotificationService) renderTemplate(template *mysql.NotificationTemplate, variables map[string]interface{}) (*domain.RenderedContent, error) {
	// 简单的变量替换实现
	// 在实际项目中可以使用更复杂的模板引擎如 text/template
	subject := template.Subject
	body := template.Content

	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		valueStr := fmt.Sprintf("%v", value)
		subject = replaceAll(subject, placeholder, valueStr)
		body = replaceAll(body, placeholder, valueStr)
	}

	return &domain.RenderedContent{
		Subject: subject,
		Body:    body,
	}, nil
}

// replaceAll 简单的字符串替换
func replaceAll(text, old, new string) string {
	// 使用 strings.ReplaceAll 的简化实现
	result := text
	for {
		index := findIndex(result, old)
		if index == -1 {
			break
		}
		result = result[:index] + new + result[index+len(old):]
	}
	return result
}

// findIndex 查找子字符串位置
func findIndex(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// sendEmail 发送邮件
func (s *NotificationService) sendEmail(ctx context.Context, notification *mysql.Notification, req *domain.SendNotificationRequest) error {
	if s.emailService == nil {
		return fmt.Errorf("email service not configured")
	}

	emailReq := &domain.EmailRequest{
		To:      []string{req.Recipient},
		Subject: notification.Subject,
		Body:    notification.Content,
	}

	return s.emailService.SendEmail(ctx, emailReq)
}

// sendSMS 发送短信
func (s *NotificationService) sendSMS(ctx context.Context, notification *mysql.Notification, req *domain.SendNotificationRequest) error {
	if s.smsService == nil {
		return fmt.Errorf("SMS service not configured")
	}

	smsReq := &domain.SMSRequest{
		To:      req.Recipient,
		Content: notification.Content,
	}

	return s.smsService.SendSMS(ctx, smsReq)
}

// sendPush 发送推送
func (s *NotificationService) sendPush(ctx context.Context, notification *mysql.Notification, req *domain.SendNotificationRequest) error {
	if s.pushService == nil {
		return fmt.Errorf("push service not configured")
	}

	pushReq := &domain.PushRequest{
		To:      req.Recipient,
		Title:   notification.Subject,
		Content: notification.Content,
	}

	return s.pushService.SendPush(ctx, pushReq)
}

// GetNotificationHistory 获取通知历史
func (s *NotificationService) GetNotificationHistory(ctx context.Context, req *domain.GetHistoryRequest) (*domain.GetHistoryResponse, error) {
	if req == nil {
		return nil, bizerr.ParamErr.WithMessage("request cannot be nil")
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 查询通知历史
	histories, total, err := s.historyDao.FindByConditions(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get notification history: %w", err)
	}

	// 转换为domain类型
	historyItems := make([]*domain.NotificationHistoryItem, len(histories))
	for i, h := range histories {
		historyItems[i] = &domain.NotificationHistoryItem{
			NotificationID: h.NotificationId,
			Status:         h.Status,
			ErrorMessage:   h.ErrorMessage,
			SentAt:         h.SentAt,
			CreatedAt:      h.CreatedAt,
		}
	}

	return &domain.GetHistoryResponse{
		Histories: historyItems,
		Total:     total,
		Page:      req.Page,
		PageSize:  req.PageSize,
	}, nil
}