package service

import (
	"context"
	"fmt"
	"time"

	"hotel/common/bizerr"
	"hotel/notify/domain"
	"hotel/notify/mysql"
)

// TemplateService 模板服务
type TemplateService struct {
	templateDao *mysql.TemplateDao
}

// NewTemplateService 创建模板服务
func NewTemplateService(templateDao *mysql.TemplateDao) *TemplateService {
	return &TemplateService{
		templateDao: templateDao,
	}
}

// CreateTemplate 创建通知模板
func (s *TemplateService) CreateTemplate(ctx context.Context, req *domain.CreateTemplateRequest) (*domain.TemplateItem, error) {
	// 参数验证
	if err := s.validateCreateRequest(req); err != nil {
		return nil, err
	}

	// 检查是否已存在相同类型和场景的模板
	existingTemplate, err := s.templateDao.FindByTypeAndScenario(ctx, req.Type, req.Scenario)
	if err == nil && existingTemplate != nil {
		return nil, bizerr.DuplicateErr.WithMessage("template already exists for this type and scenario")
	}

	// 创建模板记录
	template := &mysql.NotificationTemplate{
		Type:        req.Type,
		Scenario:    req.Scenario,
		Name:        req.Name,
		Description: req.Description,
		Subject:     req.Subject,
		Content:     req.Content,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 保存到数据库
	result, err := s.templateDao.Insert(ctx, template)
	if err != nil {
		return nil, fmt.Errorf("failed to create template: %w", err)
	}

	templateID, _ := result.LastInsertId()
	template.Id = templateID

	return s.convertToTemplateItem(template), nil
}

// UpdateTemplate 更新通知模板
func (s *TemplateService) UpdateTemplate(ctx context.Context, req *domain.UpdateTemplateRequest) (*domain.TemplateItem, error) {
	// 参数验证
	if req.ID <= 0 {
		return nil, bizerr.ParamErr.WithMessage("template ID is required")
	}

	// 查找现有模板
	template, err := s.templateDao.FindOne(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	// 更新字段
	if req.Name != "" {
		template.Name = req.Name
	}
	if req.Description != "" {
		template.Description = req.Description
	}
	if req.Subject != "" {
		template.Subject = req.Subject
	}
	if req.Content != "" {
		template.Content = req.Content
	}
	template.UpdatedAt = time.Now()

	// 保存更新
	if err := s.templateDao.Update(ctx, template); err != nil {
		return nil, fmt.Errorf("failed to update template: %w", err)
	}

	return s.convertToTemplateItem(template), nil
}

// GetTemplate 获取模板详情
func (s *TemplateService) GetTemplate(ctx context.Context, id int64) (*domain.TemplateItem, error) {
	if id <= 0 {
		return nil, bizerr.ParamErr.WithMessage("template ID is required")
	}

	template, err := s.templateDao.FindOne(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("template not found: %w", err)
	}

	return s.convertToTemplateItem(template), nil
}

// DeleteTemplate 删除模板
func (s *TemplateService) DeleteTemplate(ctx context.Context, id int64) error {
	if id <= 0 {
		return bizerr.ParamErr.WithMessage("template ID is required")
	}

	// 检查模板是否存在
	_, err := s.templateDao.FindOne(ctx, id)
	if err != nil {
		return fmt.Errorf("template not found: %w", err)
	}

	// 删除模板
	if err := s.templateDao.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	return nil
}

// ListTemplates 获取模板列表
func (s *TemplateService) ListTemplates(ctx context.Context, req *domain.TemplateListRequest) (*domain.TemplateListResponse, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 这里简化实现，实际项目中需要根据条件查询
	// 由于当前的DAO没有实现分页查询，这里返回空列表
	// 在实际项目中需要扩展DAO方法

	return &domain.TemplateListResponse{
		Templates: []*domain.TemplateItem{},
		Total:     0,
		Page:      req.Page,
		PageSize:  req.PageSize,
	}, nil
}

// validateCreateRequest 验证创建请求
func (s *TemplateService) validateCreateRequest(req *domain.CreateTemplateRequest) error {
	if req == nil {
		return bizerr.ParamErr.WithMessage("request cannot be nil")
	}
	if req.Type == "" {
		return bizerr.ParamErr.WithMessage("template type is required")
	}
	if req.Scenario == "" {
		return bizerr.ParamErr.WithMessage("scenario is required")
	}
	if req.Name == "" {
		return bizerr.ParamErr.WithMessage("template name is required")
	}
	if req.Subject == "" && req.Type == domain.NotificationTypeEmail {
		return bizerr.ParamErr.WithMessage("subject is required for email templates")
	}
	if req.Content == "" {
		return bizerr.ParamErr.WithMessage("template content is required")
	}

	// 验证通知类型
	if !s.isValidNotificationType(req.Type) {
		return bizerr.ParamErr.WithMessage("invalid notification type")
	}

	return nil
}

// isValidNotificationType 验证通知类型
func (s *TemplateService) isValidNotificationType(notificationType string) bool {
	validTypes := []string{
		domain.NotificationTypeEmail,
		domain.NotificationTypeSMS,
		domain.NotificationTypePush,
	}

	for _, validType := range validTypes {
		if notificationType == validType {
			return true
		}
	}
	return false
}

// convertToTemplateItem 转换为模板项
func (s *TemplateService) convertToTemplateItem(template *mysql.NotificationTemplate) *domain.TemplateItem {
	return &domain.TemplateItem{
		ID:          template.Id,
		Type:        template.Type,
		Scenario:    template.Scenario,
		Name:        template.Name,
		Description: template.Description,
		Subject:     template.Subject,
		Content:     template.Content,
		CreatedAt:   template.CreatedAt,
		UpdatedAt:   template.UpdatedAt,
	}
}