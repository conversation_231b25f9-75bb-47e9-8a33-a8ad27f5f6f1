package api

import (
	"context"

	"hotel/common/bizerr"
	"hotel/notify/domain"
	"hotel/notify/service"
)

// NotificationAPI 通知API
type NotificationAPI struct {
	notificationService *service.NotificationService
	templateService     *service.TemplateService
}

// NewNotificationAPI 创建通知API
func NewNotificationAPI(notificationService *service.NotificationService, templateService *service.TemplateService) *NotificationAPI {
	return &NotificationAPI{
		notificationService: notificationService,
		templateService:     templateService,
	}
}

// SendNotification 发送通知
// @path: /notify/send
// @auth: required
// @tags: notification
// @desc: 发送多渠道通知
func (api *NotificationAPI) SendNotification(ctx context.Context, req *domain.SendNotificationRequest) (*domain.SendNotificationResponse, error) {
	// 参数验证
	if req == nil {
		return nil, bizerr.ParamErr.WithMessage("request cannot be nil")
	}

	// 调用服务层
	response, err := api.notificationService.SendNotification(ctx, req)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// GetNotificationHistory 获取通知历史
// @path: /notify/history
// @auth: required
// @tags: notification
// @desc: 获取通知发送历史记录
func (api *NotificationAPI) GetNotificationHistory(ctx context.Context, req *domain.NotificationHistoryRequest) (*domain.NotificationHistoryResponse, error) {
		if req == nil {
			return nil, bizerr.ParamErr.WithMessage("请求参数不能为空")
		}

		// 转换请求类型
		getHistoryReq := &domain.GetHistoryRequest{
			Type:      req.Type,
			Scenario:  req.Scenario,
			Recipient: req.Recipient,
			Status:    req.Status,
			Page:      req.Page,
			PageSize:  req.PageSize,
		}

		// 调用服务层获取通知历史
		response, err := api.notificationService.GetNotificationHistory(ctx, getHistoryReq)
		if err != nil {
			return nil, err
		}

		// 转换响应类型
		historyResponse := &domain.NotificationHistoryResponse{
			Histories: response.Histories,
			Total:     response.Total,
			Page:      response.Page,
			PageSize:  response.PageSize,
		}

		return historyResponse, nil
	}

// CreateTemplate 创建通知模板
// @path: /notify/template
// @auth: required
// @tags: template
// @desc: 创建通知模板
func (api *NotificationAPI) CreateTemplate(ctx context.Context, req *domain.CreateTemplateRequest) (*domain.TemplateItem, error) {
	// 参数验证
	if req == nil {
		return nil, bizerr.ParamErr.WithMessage("request cannot be nil")
	}

	// 调用服务层
	template, err := api.templateService.CreateTemplate(ctx, req)
	if err != nil {
		return nil, err
	}

	return template, nil
}

// UpdateTemplate 更新通知模板
// @path: /notify/template/{id}
// @auth: required
// @tags: template
// @desc: 更新通知模板
func (api *NotificationAPI) UpdateTemplate(ctx context.Context, req *domain.UpdateTemplateRequest) (*domain.TemplateItem, error) {
	// 参数验证
	if req == nil {
		return nil, bizerr.ParamErr.WithMessage("request cannot be nil")
	}

	// 调用服务层
	template, err := api.templateService.UpdateTemplate(ctx, req)
	if err != nil {
		return nil, err
	}

	return template, nil
}

// GetTemplate 获取模板详情
// @path: /notify/template/{id}
// @auth: required
// @tags: template
// @desc: 获取通知模板详情
func (api *NotificationAPI) GetTemplate(ctx context.Context, id int64) (*domain.TemplateItem, error) {
	// 参数验证
	if id <= 0 {
		return nil, bizerr.ParamErr.WithMessage("template ID is required")
	}

	// 调用服务层
	template, err := api.templateService.GetTemplate(ctx, id)
	if err != nil {
		return nil, err
	}

	return template, nil
}

// DeleteTemplate 删除模板
// @path: /notify/template/{id}
// @auth: required
// @tags: template
// @desc: 删除通知模板
func (api *NotificationAPI) DeleteTemplate(ctx context.Context, id int64) error {
	// 参数验证
	if id <= 0 {
		return bizerr.ParamErr.WithMessage("template ID is required")
	}

	// 调用服务层
	err := api.templateService.DeleteTemplate(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

// ListTemplates 获取模板列表
// @path: /notify/templates
// @auth: required
// @tags: template
// @desc: 获取通知模板列表
func (api *NotificationAPI) ListTemplates(ctx context.Context, req *domain.TemplateListRequest) (*domain.TemplateListResponse, error) {
	// 参数验证
	if req == nil {
		return nil, bizerr.ParamErr.WithMessage("request cannot be nil")
	}

	// 调用服务层
	response, err := api.templateService.ListTemplates(ctx, req)
	if err != nil {
		return nil, err
	}

	return response, nil
}