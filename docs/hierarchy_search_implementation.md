# 酒店地理层级搜索实现文档

## 概述

本文档描述了酒店地理层级搜索功能的实现，该功能解决了酒店地理数据不精确导致的搜索问题。

## 问题背景

### 原有问题
1. **单一关联关系**：酒店只通过 `city_region_id` 关联一个地理区域
2. **数据不精确**：一个酒店可能同时属于松江区和上海市，但只能关联其中一个
3. **供应商数据差异**：不同供应商的地理数据归属可能不同
4. **搜索遗漏**：搜索"上海"时，`city_region_id`为"松江区"的酒店无法被找到

### 解决方案
实现地理层级搜索，当搜索某个region时，自动包含其所有子region和父region的酒店。

## 实现方案

### 核心思路
- 利用现有的geography service中的层级关系数据
- 在搜索时自动扩展region ID列表
- 保持API接口不变，确保向后兼容

### 技术实现

#### 1. 新增地理层级扩展方法

在 `geography/service/init.go` 中新增：

```go
// GetExpandedRegionIds 获取扩展的region ID列表，包含原始region、所有祖先和所有后代
func (s *GeographyService) GetExpandedRegionIds(ctx context.Context, regionIds types.IDs) (types.IDs, error)
```

**功能说明**：
- 输入：原始region ID列表
- 输出：扩展后的region ID列表（包含原始、祖先、后代）
- 去重处理，避免重复的region ID

#### 2. 修改酒店搜索逻辑

在 `content/service/content.go` 中修改 `ListHotelByRegionIds` 方法：

```go
func (s *ContentService) ListHotelByRegionIds(ctx context.Context, req *protocol.ListHotelByRegionIdsReq) (*protocol.ListHotelByRegionIdsResp, error) {
    // 使用层级搜索扩展region ID列表
    expandedRegionIds, err := s.geoSrv.GetExpandedRegionIds(ctx, req.RegionIds)
    if err != nil {
        // 如果扩展失败，使用原始的region IDs
        expandedRegionIds = req.RegionIds
    }

    hs, pageResp, err := s.dao.ListPage(ctx, expandedRegionIds, req.PageReq, req.InternalSuppliers)
    // ...
}
```

**关键特性**：
- 自动扩展region ID列表
- 容错机制：扩展失败时fallback到原始逻辑
- 保持API接口不变

#### 3. 数据库查询优化

现有的 `HotelModel.ListPage` 方法已经支持多个region ID的IN查询：

```sql
SELECT * FROM hotel WHERE city_region_id IN (1000, 1001, 1002, 1003, ...)
```

无需修改数据库查询逻辑。

## 使用示例

### 搜索上海市酒店

**原始行为**：
```go
req := &protocol.ListHotelByRegionIdsReq{
    RegionIds: types.IDs{1000}, // 上海市
}
// 只能找到 city_region_id = 1000 的酒店
```

**新行为**：
```go
req := &protocol.ListHotelByRegionIdsReq{
    RegionIds: types.IDs{1000}, // 上海市
}
// 自动扩展为: [1000, 1001, 1002, 1003, ...] (上海市 + 所有区县)
// 能找到所有相关酒店
```

### 层级扩展示例

输入：`[1000]` (上海市)
扩展后：`[1000, 1001, 1002, 1003, 1004, ...]`
- 1000: 上海市 (原始)
- 1001: 松江区 (后代)
- 1002: 黄浦区 (后代)
- 1003: 徐汇区 (后代)
- ...

## 优势

### 1. 解决数据不精确问题
- 酒店A: `city_region_id = 松江区`
- 酒店B: `city_region_id = 上海市`
- 搜索"上海"时，两个酒店都能被找到

### 2. 兼容供应商数据差异
- 供应商1：酒店归属到具体区县
- 供应商2：酒店归属到城市级别
- 层级搜索统一处理这些差异

### 3. 提升搜索覆盖率
- 不会遗漏任何相关酒店
- 自动处理地理层级关系

### 4. 保持向后兼容
- API接口完全不变
- 现有代码无需修改
- 自动获得层级搜索能力

## 性能考虑

### 1. 缓存优化
- 地理层级关系相对稳定
- `GetExpandedRegionIds` 结果可以缓存
- 减少重复计算

### 2. 查询优化
- 使用IN查询匹配多个region ID
- 利用现有的 `city_region_id` 索引
- 查询性能基本不受影响

### 3. 容错机制
- 层级扩展失败时自动fallback
- 保证搜索功能的可用性
- 记录错误日志便于监控

## 测试

### 单元测试
- `TestListHotelByRegionIds_WithHierarchySearch`: 测试层级搜索逻辑
- 包含成功扩展、失败fallback、多region扩展等场景

### 集成测试
- `TestListHotelByRegionIds_Integration`: 端到端测试
- 验证完整的搜索流程

### 性能测试
- `TestHierarchySearchPerformance`: 性能基准测试
- 验证大量region ID的处理性能

## 部署说明

### 1. 无需数据库变更
- 不需要修改表结构
- 不需要数据迁移
- 利用现有的地理数据

### 2. 向后兼容
- 现有API调用无需修改
- 自动获得层级搜索能力
- 可以安全部署

### 3. 监控指标
- 层级扩展成功率
- 扩展后的region数量分布
- 搜索性能指标

## 未来优化

### 1. 智能扩展策略
- 根据region类型限制扩展深度
- 避免过度扩展影响性能

### 2. 结果排序优化
- 精确匹配优先
- 按地理距离排序

### 3. 多region关联表
- 长期方案：支持一个酒店关联多个region
- 从根本上解决数据质量问题

## 总结

本实现通过简单而有效的层级搜索增强，解决了酒店地理数据不精确的问题：

1. **立即生效**：无需数据库变更，部署后立即生效
2. **向后兼容**：现有代码无需修改
3. **性能友好**：利用现有索引，性能影响最小
4. **容错健壮**：失败时自动fallback，保证可用性

这为后续的数据质量改进和更复杂的地理搜索功能奠定了基础。
