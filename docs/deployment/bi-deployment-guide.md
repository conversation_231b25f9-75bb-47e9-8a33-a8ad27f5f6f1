# 订单BI分析系统部署指南

## 概述

订单BI分析系统已完成从搜索到订单再到分析的完整链路实现，支持实时数据收集、事件追踪和可视化分析。

## 系统架构

```
搜索服务 (search) → 订单服务 (trade) → BI分析服务 (bi) → 前端仪表板
     ↓                    ↓                   ↓
  事件追踪            订单创建追踪          实时数据收集
     ↓                    ↓                   ↓
           统一事件日志系统 (tracking_log)
```

## 部署前准备

### 1. 数据库准备

执行数据库迁移脚本：

```bash
mysql -u root -p < docs/sql/tracking_schema.sql
```

创建以下表：
- `tracking_log`: 事件追踪日志表
- `analytics_summary`: 分析汇总表
- `user_journey`: 用户行程表

### 2. 环境配置

确保以下配置项正确设置：

```yaml
# bi/config/config.yaml
DSN: "user:password@tcp(localhost:3306)/item?charset=utf8mb4&parseTime=True&loc=Local"
Redis:
  Host: "127.0.0.1:6379"
  Type: "node"
```

### 3. 依赖检查

运行就绪检查：

```bash
go run cmd/readiness-check/main.go
```

## 部署步骤

### 1. 后端部署

```bash
# 构建应用
make build

# 启动基础设施
make start-infra

# 运行应用
make run-dev
```

### 2. 前端部署

```bash
cd admin-fe
pnpm install
pnpm build
pnpm preview
```

### 3. 验证部署

访问以下端点验证功能：

1. **BI分析API**: `POST /api/bi/order/analytics`
2. **实时指标API**: `POST /api/bi/order/realtime` 
3. **前端仪表板**: `http://localhost:3008/bi/order-analytics`

## 核心功能

### 1. 搜索事件追踪

- 自动追踪所有酒店搜索请求
- 记录搜索关键词、结果数量、用户行为
- 支持按地区、时间范围分析搜索热度

### 2. 订单生命周期追踪

- 追踪从搜索到下单的完整用户行程
- 记录订单状态变更和关键时间点
- 支持转化率和流失率分析

### 3. 实时数据收集

- 每5分钟自动更新实时指标
- 支持今日订单数、收入、完成率等关键指标
- 提供实时数据API接口

### 4. 分析报表

- 支持多维度数据分析（时间、实体、状态等）
- 提供趋势图表、饼图、排行榜等可视化组件
- 支持Excel/CSV/PDF格式数据导出

## API文档

### 获取订单分析数据

```http
POST /api/bi/order/analytics
Content-Type: application/json

{
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-01-31T23:59:59Z",
  "granularity": "day",
  "entityId": 123
}
```

### 获取实时指标

```http
POST /api/bi/order/realtime
Content-Type: application/json

{
  "metrics": ["todayOrders", "todayRevenue", "activeBookings"]
}
```

### 导出数据

```http
POST /api/bi/export
Content-Type: application/json

{
  "exportType": "excel",
  "dataType": "analytics",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-01-31T23:59:59Z"
}
```

## 性能优化

### 1. 缓存策略

- 分析结果缓存15分钟
- 实时指标缓存5分钟
- 支持预热常用查询

### 2. 数据库优化

- 关键字段建立复合索引
- 使用汇总表优化大数据量查询
- 支持读写分离和分片

### 3. 内存优化

- 实现过期缓存自动清理
- 批量处理事件数据
- 异步执行追踪任务

## 监控和告警

### 1. 性能监控

- API响应时间监控
- 数据库查询性能监控
- 缓存命中率监控

### 2. 业务监控

- 实时订单数量监控
- 转化率异常告警
- 数据异常检测

### 3. 系统监控

- 内存使用率监控
- CPU负载监控
- 磁盘空间监控

## 故障排查

### 1. 常见问题

**数据库连接失败**
```bash
# 检查数据库连接
mysql -u root -p -h localhost

# 检查配置文件
cat bi/config/config.yaml
```

**缓存服务异常**
```bash
# 检查Redis连接
redis-cli ping

# 检查Redis配置
redis-cli config get '*'
```

**API响应慢**
```bash
# 检查数据库索引
SHOW INDEX FROM `order`;

# 分析慢查询
SHOW PROCESSLIST;
```

### 2. 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看事件追踪日志
SELECT * FROM tracking_log ORDER BY timestamp DESC LIMIT 100;

# 查看性能日志
SELECT COUNT(*) as queries, AVG(cost_time) as avg_time 
FROM hb_log 
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

## 扩展建议

### 1. 水平扩展

- 使用负载均衡器分发API请求
- 实现数据库读写分离
- 添加Redis集群支持

### 2. 功能扩展

- 增加机器学习预测模型
- 实现用户行为画像分析
- 添加A/B测试支持

### 3. 运维优化

- 实现自动化部署和回滚
- 添加蓝绿部署支持
- 集成CI/CD流水线

## 安全考虑

### 1. 数据安全

- 敏感数据加密存储
- API访问权限控制
- 数据导出审计日志

### 2. 网络安全

- HTTPS强制加密
- API限流和防护
- 跨域访问控制

## 维护清单

### 日常维护

- [ ] 检查实时服务运行状态
- [ ] 监控数据库性能指标
- [ ] 清理过期日志数据
- [ ] 验证备份恢复流程

### 周期维护

- [ ] 分析系统性能报告
- [ ] 更新安全补丁
- [ ] 优化数据库索引
- [ ] 检查磁盘空间使用

### 紧急维护

- [ ] 准备快速回滚方案
- [ ] 建立应急联系机制
- [ ] 制定数据恢复计划
- [ ] 准备备用环境