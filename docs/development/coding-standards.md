# 编码规范

本文档定义了 Hotel-BE 项目的编码规范和最佳实践，确保代码质量和团队协作效率。

## 📋 基本原则

### 1. 依赖注入原则
- **依赖项应在服务初始化时保证正确性**，避免在运行时进行nil检查
- 如果依赖项有问题，应该让服务无法启动，而不是在运行时返回降级数据
- 失败应该快速失败（fail-fast），不要掩盖错误

### 2. 测试代码管理
- **生产代码中不应该包含测试用的条件分支**
- 避免测试代码泄露到生产环境
- 使用真实的依赖项进行测试，只在必要时使用mock

### 3. 错误处理
- 使用 `hotel/common/log` 统一打日志，必须带上ctx
- 错误应该快速失败，不要掩盖真实错误
- 提供有意义的错误信息

## 🏗️ 项目结构规范

### 目录命名
- 使用小写字母和连字符（kebab-case）
- 模块目录使用有意义的名称
- 测试目录与源码目录保持一致

### 文件命名
- Go文件使用snake_case
- 测试文件以`_test.go`结尾
- 配置文件使用yaml格式

## 📝 代码规范

### 命名规范

#### 变量命名
```go
// ✅ 正确
var hotelId string
var userName string
var orderStatus int

// ❌ 错误
var HotelID string  // 禁用ID风格
var user_name string // 使用snake_case
```

#### 结构体字段
```go
// ✅ 正确
type Hotel struct {
    HotelId   string `json:"hotelId"`
    HotelName string `json:"hotelName"`
    CityId    string `json:"cityId"`
}

// ❌ 错误
type Hotel struct {
    HotelID   string `json:"hotelID"`  // 禁用ID风格
    hotelName string `json:"hotelName"` // 字段应该是大写
}
```

#### 函数命名
```go
// ✅ 正确
func GetHotelById(id string) (*Hotel, error)
func CreateOrder(req *OrderRequest) (*Order, error)
func UpdateUserProfile(user *User) error

// ❌ 错误
func getHotelById(id string) (*Hotel, error) // 导出函数应该大写
func create_order(req *OrderRequest) (*Order, error) // 使用snake_case
```

### 包命名规范

#### 测试包名
```go
// ✅ 正确 - 测试包名与被测包名一致
package mysql

func TestHotelModel_FindNearby(t *testing.T) {
    // 测试代码
}

// ❌ 错误
package mysql_test // 不要使用_test后缀
```

### 导入规范
```go
// ✅ 正确 - 按标准库、第三方库、内部库分组
import (
    "context"
    "fmt"
    "time"
    
    "github.com/gin-gonic/gin"
    "github.com/go-redis/redis/v8"
    
    "hotel/common/log"
    "hotel/content/domain"
)
```

## 🔧 开发实践

### 1. 依赖注入模式
```go
// ✅ 正确 - 在初始化时保证依赖项正确性
type TradeService struct {
    orderDao *OrderDao
    userDao  *UserDao
}

func NewTradeService(orderDao *OrderDao, userDao *UserDao) *TradeService {
    if orderDao == nil || userDao == nil {
        panic("dependencies cannot be nil")
    }
    return &TradeService{
        orderDao: orderDao,
        userDao:  userDao,
    }
}

// ❌ 错误 - 运行时检查依赖项
func (s *TradeService) ListOrders(ctx context.Context) ([]*Order, error) {
    if s.orderDao == nil {
        return []*Order{}, nil // 返回空数据掩盖错误
    }
    return s.orderDao.List(ctx)
}
```

### 2. 错误处理
```go
// ✅ 正确 - 快速失败
func (s *TradeService) GetOrder(ctx context.Context, orderId string) (*Order, error) {
    if orderId == "" {
        return nil, errors.New("orderId cannot be empty")
    }
    
    order, err := s.orderDao.Get(ctx, orderId)
    if err != nil {
        log.Error(ctx, "failed to get order", "orderId", orderId, "error", err)
        return nil, fmt.Errorf("failed to get order: %w", err)
    }
    
    return order, nil
}
```

### 3. 日志规范
```go
// ✅ 正确 - 使用common/log，带上ctx
import "hotel/common/log"

func (s *TradeService) CreateOrder(ctx context.Context, req *OrderRequest) (*Order, error) {
    log.Info(ctx, "creating order", "userId", req.UserId, "hotelId", req.HotelId)
    
    // 业务逻辑
    
    log.Info(ctx, "order created successfully", "orderId", order.Id)
    return order, nil
}
```

### 4. 测试规范
```go
// ✅ 正确 - 使用真实依赖项
func TestTradeService_CreateOrder(t *testing.T) {
    // 使用真实的数据库连接
    db := setupTestDB(t)
    orderDao := NewOrderDao(db)
    service := NewTradeService(orderDao, nil)
    
    req := &OrderRequest{
        UserId:  "user123",
        HotelId: "hotel456",
    }
    
    order, err := service.CreateOrder(context.Background(), req)
    assert.NoError(t, err)
    assert.NotNil(t, order)
}

// ✅ 正确 - 验证依赖项为nil时会panic
func TestTradeService_OrderDaoNil_ShouldPanic(t *testing.T) {
    defer func() {
        if r := recover(); r == nil {
            t.Error("expected panic when orderDao is nil")
        }
    }()
    
    NewTradeService(nil, nil)
}
```

## 🧪 测试规范

### 测试原则
1. **使用真实环境**: 优先使用真实的Redis、MySQL、配置等
2. **避免mock**: 只在测试纯业务逻辑时使用mock
3. **测试包名**: 与被测包名保持一致
4. **硬编码测试值**: 不要使用环境变量，直接硬编码测试值

### 测试结构
```go
func TestFunctionName_Scenario_ExpectedResult(t *testing.T) {
    // Arrange - 准备测试数据
    ctx := context.Background()
    req := &Request{...}
    
    // Act - 执行被测试的方法
    result, err := function(ctx, req)
    
    // Assert - 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.Equal(t, expected, result.Value)
}
```

### 集成测试
```go
func TestOrderFlow_EndToEnd(t *testing.T) {
    // 使用真实的数据库和Redis
    db := setupTestDB(t)
    redis := setupTestRedis(t)
    
    service := NewTradeService(NewOrderDao(db), NewUserDao(db))
    
    // 完整的业务流程测试
    order, err := service.CreateOrder(ctx, req)
    assert.NoError(t, err)
    
    // 验证订单状态
    updatedOrder, err := service.GetOrder(ctx, order.Id)
    assert.NoError(t, err)
    assert.Equal(t, OrderStatusConfirmed, updatedOrder.Status)
}
```

## 📊 代码质量

### 代码审查清单
- [ ] 遵循命名规范
- [ ] 没有测试代码泄露
- [ ] 依赖注入正确实现
- [ ] 错误处理完善
- [ ] 日志记录规范
- [ ] 测试覆盖充分
- [ ] 文档注释完整

### 静态分析工具
```bash
# 运行代码检查
golangci-lint run

# 运行测试
go test ./...

# 检查测试覆盖率
go test -cover ./...
```

## 🔄 版本控制

### 分支管理
- **主分支**: `master` - 生产环境代码
- **开发分支**: `develop` - 开发环境代码
- **功能分支**: `feature/功能名称` - 新功能开发
- **修复分支**: `fix/问题描述` - 问题修复

### 提交规范
```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

类型包括：
- `feat`: 新功能
- `fix`: 问题修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📚 参考资料

- [Go官方编码规范](https://golang.org/doc/effective_go.html)
- [Go项目结构最佳实践](https://github.com/golang-standards/project-layout)
- [Go测试最佳实践](https://github.com/golang/go/wiki/CodeReviewComments#tests)
- [Go错误处理最佳实践](https://blog.golang.org/error-handling-and-go)

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队 