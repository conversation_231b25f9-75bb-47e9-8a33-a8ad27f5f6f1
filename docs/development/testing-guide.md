# 测试指南

本文档介绍了 Hotel-BE 项目的测试策略、测试工具和最佳实践。

## 🧪 测试策略

### 测试金字塔

```
    ┌─────────────┐
    │   E2E Tests │  ← 少量，覆盖关键业务流程
    └─────────────┘
    ┌─────────────┐
    │Integration  │  ← 中等数量，覆盖服务间交互
    │   Tests     │
    └─────────────┘
    ┌─────────────┐
    │  Unit Tests │  ← 大量，覆盖业务逻辑
    └─────────────┘
```

### 测试原则

1. **使用真实环境**: 优先使用真实的Redis、MySQL、配置等
2. **避免mock**: 只在测试纯业务逻辑时使用mock
3. **测试包名**: 与被测包名保持一致
4. **硬编码测试值**: 不要使用环境变量，直接硬编码测试值

## 📋 测试类型

### 1. 单元测试 (Unit Tests)

#### 测试范围
- 业务逻辑函数
- 数据访问层方法
- 工具函数
- 配置验证

#### 测试示例
```go
package mysql

import (
    "context"
    "testing"
    "github.com/stretchr/testify/assert"
)

func TestHotelModel_FindNearby(t *testing.T) {
    // Arrange - 准备测试数据
    ctx := context.Background()
    lat, lng := 39.9042, 116.4074 // 北京坐标
    radius := 5000.0 // 5公里
    
    // Act - 执行被测试的方法
    hotels, err := hotelModel.FindNearby(ctx, lat, lng, radius)
    
    // Assert - 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, hotels)
    assert.Len(t, hotels, 20) // 期望返回20个酒店
    
    // 验证距离排序
    for i := 1; i < len(hotels); i++ {
        assert.LessOrEqual(t, hotels[i-1].Distance, hotels[i].Distance)
    }
}

func TestCleanString_SpecialCharacters(t *testing.T) {
    tests := []struct {
        input    string
        expected string
    }{
        {"Hotel\xA0Name", "Hotel Name"},
        {"Hotel\x92Name", "Hotel'Name"},
        {"Hotel\x93Name\x94", "Hotel\"Name\""},
        {"Hotel\x96Name\x97", "Hotel-Name—"},
    }
    
    for _, tt := range tests {
        result := CleanString(tt.input)
        assert.Equal(t, tt.expected, result)
    }
}
```

#### 测试命名规范
```go
// 格式：TestFunctionName_Scenario_ExpectedResult
func TestHotelModel_FindNearby_EmptyResult_WhenNoHotelsInRadius(t *testing.T) {
    // 测试代码
}

func TestTradeService_CreateOrder_Success_WhenValidRequest(t *testing.T) {
    // 测试代码
}

func TestUserService_Login_Failure_WhenInvalidCredentials(t *testing.T) {
    // 测试代码
}
```

### 2. 集成测试 (Integration Tests)

#### 测试范围
- 服务间交互
- 数据库操作
- 外部API调用
- 消息队列处理

#### 测试示例
```go
func TestOrderFlow_EndToEnd(t *testing.T) {
    // 使用真实的数据库和Redis
    db := setupTestDB(t)
    redis := setupTestRedis(t)
    
    // 创建服务实例
    userService := NewUserService(NewUserDao(db))
    tradeService := NewTradeService(NewOrderDao(db), userService)
    
    // 创建用户
    user, err := userService.CreateUser(ctx, &CreateUserRequest{
        Email:    "<EMAIL>",
        Password: "password123",
    })
    assert.NoError(t, err)
    
    // 创建订单
    order, err := tradeService.CreateOrder(ctx, &CreateOrderRequest{
        UserId:  user.Id,
        HotelId: "hotel123",
        CheckIn: "2025-08-01",
        CheckOut: "2025-08-03",
    })
    assert.NoError(t, err)
    
    // 验证订单状态
    assert.Equal(t, OrderStatusCreated, order.Status)
    
    // 查询订单
    retrievedOrder, err := tradeService.GetOrder(ctx, order.Id)
    assert.NoError(t, err)
    assert.Equal(t, order.Id, retrievedOrder.Id)
}
```

### 3. 端到端测试 (E2E Tests)

#### 测试范围
- 完整的业务流程
- API接口测试
- 前端交互测试
- 性能测试

#### 前端E2E测试示例
```typescript
// admin-fe/tests/e2e/invite_user.spec.ts
describe('User Invitation Flow', () => {
  it('should invite user successfully', () => {
    // 访问用户列表页面
    cy.visit('/business/user-list')
    
    // 点击邀请用户按钮
    cy.get('[data-testid="invite-user-btn"]').click()
    
    // 填写邀请信息
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="role-select"]').select('admin')
    cy.get('[data-testid="entity-input"]').type('entity123')
    
    // 提交邀请
    cy.get('[data-testid="submit-btn"]').click()
    
    // 验证成功提示
    cy.get('[data-testid="success-message"]').should('be.visible')
    cy.get('[data-testid="success-message"]').should('contain', '邀请发送成功')
  })
})
```

## 🛠️ 测试工具

### 1. Go测试工具

#### 测试框架
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./content/mysql

# 运行特定测试函数
go test -v ./content/mysql -run TestFindNearby

# 运行测试并显示覆盖率
go test -cover ./...

# 生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

#### 测试辅助工具
```go
// 测试数据库设置
func setupTestDB(t *testing.T) *sql.DB {
    db, err := sql.Open("mysql", "test_dsn")
    if err != nil {
        t.Fatal(err)
    }
    
    // 清理测试数据
    _, err = db.Exec("DELETE FROM hotels")
    if err != nil {
        t.Fatal(err)
    }
    
    return db
}

// 测试Redis设置
func setupTestRedis(t *testing.T) *redis.Client {
    client := redis.NewClient(&redis.Options{
        Addr: "localhost:6379",
        DB:   1, // 使用不同的数据库避免冲突
    })
    
    // 清理测试数据
    client.FlushDB(context.Background())
    
    return client
}
```

### 2. 前端测试工具

#### Cypress E2E测试
```bash
# 安装Cypress
npm install cypress --save-dev

# 运行E2E测试
npm run test:e2e

# 打开Cypress界面
npx cypress open
```

#### 单元测试
```bash
# 运行单元测试
npm run test:unit

# 运行测试覆盖率
npm run test:coverage
```

### 3. 性能测试工具

#### Apache Bench
```bash
# 基本压力测试
ab -n 1000 -c 10 http://localhost:8080/api/hotels/search

# 带参数的POST请求
ab -n 1000 -c 10 -p post_data.json -T application/json \
   http://localhost:8080/api/orders/create
```

#### wrk
```bash
# 高性能压力测试
wrk -t12 -c400 -d30s http://localhost:8080/api/hotels/search

# 带Lua脚本的测试
wrk -t12 -c400 -d30s -s test_script.lua http://localhost:8080/api/hotels/search
```

## 📊 测试覆盖率

### 覆盖率目标

| 模块 | 目标覆盖率 | 当前覆盖率 |
|------|------------|------------|
| 核心业务逻辑 | > 90% | 85% |
| 数据访问层 | > 80% | 75% |
| 工具函数 | > 95% | 90% |
| API接口 | > 70% | 65% |

### 覆盖率检查
```bash
# 生成覆盖率报告
go test -coverprofile=coverage.out ./...

# 查看覆盖率详情
go tool cover -func=coverage.out

# 生成HTML报告
go tool cover -html=coverage.out -o coverage.html
```

## 🔄 测试流程

### 1. 开发阶段测试

#### 本地开发测试
```bash
# 运行快速测试
go test -short ./...

# 运行特定模块测试
go test ./content/mysql

# 运行性能测试
go test -v ./content/mysql -run TestFindNearbyPerformance
```

#### 代码提交前测试
```bash
# 运行所有测试
make test

# 运行代码检查
make lint

# 运行安全扫描
make security-scan
```

### 2. CI/CD测试

#### GitHub Actions配置
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Set up Go
        uses: actions/setup-go@v2
        with:
          go-version: '1.24'
          
      - name: Run tests
        run: go test -v ./...
        
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

### 3. 生产环境测试

#### 健康检查
```bash
# 服务健康检查
curl -f http://localhost:8080/health

# 数据库连接检查
curl -f http://localhost:8080/health/db

# Redis连接检查
curl -f http://localhost:8080/health/redis
```

## 🐛 调试技巧

### 1. 测试调试

#### 使用调试器
```bash
# 使用delve调试测试
dlv test ./content/mysql -test.run TestFindNearby

# 设置断点
(dlv) break TestFindNearby
(dlv) continue
(dlv) next
```

#### 日志调试
```go
func TestHotelModel_FindNearby(t *testing.T) {
    // 启用详细日志
    log.SetLevel(log.DebugLevel)
    
    // 测试代码
    hotels, err := hotelModel.FindNearby(ctx, lat, lng, radius)
    
    // 输出调试信息
    t.Logf("Found %d hotels", len(hotels))
    for i, hotel := range hotels {
        t.Logf("Hotel %d: %s, distance: %.2f", i, hotel.Name, hotel.Distance)
    }
}
```

### 2. 性能调试

#### 性能分析
```bash
# CPU性能分析
go test -cpuprofile=cpu.prof -bench=.

# 内存性能分析
go test -memprofile=mem.prof -bench=.

# 分析性能数据
go tool pprof cpu.prof
go tool pprof mem.prof
```

## 📚 最佳实践

### 1. 测试数据管理

#### 使用测试夹具
```go
// testdata/hotels.json
{
  "hotels": [
    {
      "id": "hotel1",
      "name": "Test Hotel 1",
      "lat": 39.9042,
      "lng": 116.4074,
      "rating": 4.5
    }
  ]
}

// 加载测试数据
func loadTestData(t *testing.T) []*Hotel {
    data, err := os.ReadFile("testdata/hotels.json")
    if err != nil {
        t.Fatal(err)
    }
    
    var hotels []*Hotel
    err = json.Unmarshal(data, &hotels)
    if err != nil {
        t.Fatal(err)
    }
    
    return hotels
}
```

### 2. 测试隔离

#### 数据库隔离
```go
func TestWithDatabase(t *testing.T) {
    // 使用事务确保测试隔离
    tx, err := db.Begin()
    if err != nil {
        t.Fatal(err)
    }
    defer tx.Rollback()
    
    // 测试代码使用事务
    dao := NewHotelDao(tx)
    // ...
}
```

### 3. 并行测试

#### 并行执行测试
```go
func TestParallel(t *testing.T) {
    t.Parallel() // 启用并行测试
    
    // 测试代码
}
```

## 📈 持续改进

### 1. 测试指标监控

#### 关键指标
- **测试覆盖率**: 目标 > 80%
- **测试执行时间**: 目标 < 5分钟
- **测试稳定性**: 目标 > 95%
- **缺陷发现率**: 目标 > 90%

### 2. 测试优化

#### 优化策略
1. **减少测试时间**: 使用并行测试、测试数据缓存
2. **提高覆盖率**: 添加边界条件测试、错误场景测试
3. **提升稳定性**: 使用稳定的测试数据、隔离测试环境
4. **增强可维护性**: 重构测试代码、提取公共测试工具

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队 