# 部署指南

本文档详细介绍了 Hotel-BE 项目的部署流程、环境配置和运维管理。

## 🚀 部署概览

### 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │───▶│   API Gateway   │───▶│  Microservices  │
│   (Nginx/ALB)   │    │   (go-zero)     │    │   (Go Services) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   Redis Cache   │    │   MySQL DB      │
                    │   (Cluster)     │    │   (Master/Slave)│
                    └─────────────────┘    └─────────────────┘
```

### 环境管理

| 环境 | 用途 | 特点 | 部署方式 |
|------|------|------|----------|
| Development | 开发环境 | 本地开发，热更新 | Docker Compose |
| Testing | 测试环境 | 功能测试，集成测试 | Kubernetes |
| Staging | 预生产环境 | 性能测试，用户验收 | Kubernetes |
| Production | 生产环境 | 正式服务，高可用 | Kubernetes |

## 🐳 Docker 部署

### 1. 本地开发环境

#### Docker Compose 配置
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: hoteldev
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      
  api-gateway:
    build: .
    ports:
      - "8080:8080"
    environment:
      - ENV=development
    depends_on:
      - mysql
      - redis
    volumes:
      - .:/app
      - /app/tmp
      
  admin-fe:
    build: ./admin-fe
    ports:
      - "3000:3000"
    environment:
      - VITE_API_BASE_URL=http://localhost:8080
    volumes:
      - ./admin-fe:/app
      - /app/node_modules

volumes:
  mysql_data:
  redis_data:
```

#### 启动开发环境
```bash
# 启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f api-gateway
```

### 2. 生产环境部署

#### Dockerfile 优化
```dockerfile
# 多阶段构建
FROM golang:1.24-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

EXPOSE 8080
CMD ["./main"]
```

#### 生产环境 Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  api-gateway:
    image: hotel-be/api-gateway:latest
    ports:
      - "8080:8080"
    environment:
      - ENV=production
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        
  user-service:
    image: hotel-be/user-service:latest
    environment:
      - ENV=production
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
      
  search-service:
    image: hotel-be/search-service:latest
    environment:
      - ENV=production
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
      
  trade-service:
    image: hotel-be/trade-service:latest
    environment:
      - ENV=production
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
```

## ☸️ Kubernetes 部署

### 1. 命名空间配置

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: hotel-be
  labels:
    name: hotel-be
```

### 2. 配置管理

#### ConfigMap
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: hotel-be-config
  namespace: hotel-be
data:
  config.yaml: |
    MySQL:
      User: "user"
      Trade: "mysql://user:password@mysql:3306/trade"
      Content: "mysql://user:password@mysql:3306/content"
    Redis:
      host: "redis"
      port: 6379
      type: "node"
      pass: ""
      tls: false
```

#### Secret
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: hotel-be-secret
  namespace: hotel-be
type: Opaque
data:
  mysql-password: cGFzc3dvcmQxMjM=  # base64 encoded
  redis-password: cmVkaXNwYXNz
```

### 3. 数据库部署

#### MySQL StatefulSet
```yaml
# k8s/mysql.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql
  namespace: hotel-be
spec:
  serviceName: mysql
  replicas: 1
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: hotel-be-secret
              key: mysql-password
        ports:
        - containerPort: 3306
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: hotel-be
spec:
  selector:
    app: mysql
  ports:
  - port: 3306
    targetPort: 3306
```

#### Redis StatefulSet
```yaml
# k8s/redis.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
  namespace: hotel-be
spec:
  serviceName: redis
  replicas: 3
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7.0-alpine
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-data
          mountPath: /data
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 5Gi
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: hotel-be
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
```

### 4. 应用部署

#### API Gateway Deployment
```yaml
# k8s/api-gateway.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: hotel-be
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: hotel-be/api-gateway:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENV
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway
  namespace: hotel-be
spec:
  selector:
    app: api-gateway
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

#### Ingress 配置
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hotel-be-ingress
  namespace: hotel-be
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.hotel-be.com
    secretName: hotel-be-tls
  rules:
  - host: api.hotel-be.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway
            port:
              number: 80
```

### 5. 部署脚本

#### 部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

# 设置环境变量
ENVIRONMENT=${1:-staging}
NAMESPACE="hotel-be"

echo "Deploying to $ENVIRONMENT environment..."

# 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 应用配置
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# 部署数据库
kubectl apply -f k8s/mysql.yaml
kubectl apply -f k8s/redis.yaml

# 等待数据库就绪
echo "Waiting for database to be ready..."
kubectl wait --for=condition=ready pod -l app=mysql -n $NAMESPACE --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s

# 部署应用
kubectl apply -f k8s/api-gateway.yaml
kubectl apply -f k8s/user-service.yaml
kubectl apply -f k8s/search-service.yaml
kubectl apply -f k8s/trade-service.yaml

# 部署 Ingress
kubectl apply -f k8s/ingress.yaml

echo "Deployment completed successfully!"
```

## 🔧 环境配置

### 1. 开发环境配置

#### 本地配置文件
```yaml
# config/config.dev.yaml
MySQL:
  User: "root"
  Trade: "mysql://root:123456@localhost:3306/trade"
  Content: "mysql://root:123456@localhost:3306/content"
  User: "mysql://root:123456@localhost:3306/user"

Redis:
  host: "localhost"
  port: 6379
  type: "node"
  pass: ""
  tls: false

Log:
  level: "debug"
  format: "json"
  output: "stdout"

Server:
  port: 8080
  mode: "debug"
```

### 2. 生产环境配置

#### 生产环境配置
```yaml
# config/config.prod.yaml
MySQL:
  User: "hotel_user"
  Trade: "mysql://hotel_user:${MYSQL_PASSWORD}@mysql:3306/trade"
  Content: "mysql://hotel_user:${MYSQL_PASSWORD}@mysql:3306/content"
  User: "mysql://hotel_user:${MYSQL_PASSWORD}@mysql:3306/user"

Redis:
  host: "redis"
  port: 6379
  type: "cluster"
  pass: "${REDIS_PASSWORD}"
  tls: true

Log:
  level: "info"
  format: "json"
  output: "/var/log/app.log"

Server:
  port: 8080
  mode: "release"
```

## 📊 监控和日志

### 1. 健康检查

#### 健康检查端点
```go
// health.go
func (s *Server) healthHandler(w http.ResponseWriter, r *http.Request) {
    health := map[string]interface{}{
        "status": "healthy",
        "timestamp": time.Now().Unix(),
        "version": "1.0.0",
    }
    
    // 检查数据库连接
    if err := s.checkDB(); err != nil {
        health["status"] = "unhealthy"
        health["db_error"] = err.Error()
        w.WriteHeader(http.StatusServiceUnavailable)
    }
    
    // 检查Redis连接
    if err := s.checkRedis(); err != nil {
        health["status"] = "unhealthy"
        health["redis_error"] = err.Error()
        w.WriteHeader(http.StatusServiceUnavailable)
    }
    
    json.NewEncoder(w).Encode(health)
}
```

### 2. 日志配置

#### 结构化日志
```go
// log.go
import "hotel/common/log"

func initLogger() {
    log.Init(&log.Config{
        Level:  "info",
        Format: "json",
        Output: "stdout",
        Fields: log.Fields{
            "service": "hotel-be",
            "version": "1.0.0",
        },
    })
}
```

### 3. 监控指标

#### Prometheus 指标
```go
// metrics.go
import "github.com/prometheus/client_golang/prometheus"

var (
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    httpRequestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
        },
        []string{"method", "endpoint"},
    )
)

func init() {
    prometheus.MustRegister(httpRequestsTotal)
    prometheus.MustRegister(httpRequestDuration)
}
```

## 🔒 安全配置

### 1. TLS/SSL 配置

#### 证书管理
```bash
# 生成自签名证书（开发环境）
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 创建 Kubernetes Secret
kubectl create secret tls hotel-be-tls --cert=cert.pem --key=key.pem -n hotel-be
```

### 2. 网络安全

#### 网络策略
```yaml
# k8s/network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: hotel-be-network-policy
  namespace: hotel-be
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: hotel-be
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: hotel-be
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 6379
```

## 🔄 CI/CD 流程

### 1. GitHub Actions

#### 构建和部署流水线
```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Go
      uses: actions/setup-go@v2
      with:
        go-version: '1.24'
        
    - name: Build
      run: |
        docker build -t hotel-be/api-gateway:${{ github.sha }} .
        docker build -t hotel-be/user-service:${{ github.sha }} ./user
        docker build -t hotel-be/search-service:${{ github.sha }} ./search
        docker build -t hotel-be/trade-service:${{ github.sha }} ./trade
        
    - name: Push to registry
      run: |
        docker push hotel-be/api-gateway:${{ github.sha }}
        docker push hotel-be/user-service:${{ github.sha }}
        docker push hotel-be/search-service:${{ github.sha }}
        docker push hotel-be/trade-service:${{ github.sha }}
        
  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v2
    
    - name: Deploy to staging
      run: |
        kubectl set image deployment/api-gateway api-gateway=hotel-be/api-gateway:${{ github.sha }} -n hotel-be
        kubectl set image deployment/user-service user-service=hotel-be/user-service:${{ github.sha }} -n hotel-be
        kubectl set image deployment/search-service search-service=hotel-be/search-service:${{ github.sha }} -n hotel-be
        kubectl set image deployment/trade-service trade-service=hotel-be/trade-service:${{ github.sha }} -n hotel-be
```

### 2. 回滚策略

#### 自动回滚
```bash
#!/bin/bash
# rollback.sh

DEPLOYMENT=${1:-api-gateway}
NAMESPACE="hotel-be"

# 获取上一个版本
PREVIOUS_REVISION=$(kubectl rollout history deployment/$DEPLOYMENT -n $NAMESPACE | tail -2 | head -1 | awk '{print $1}')

echo "Rolling back $DEPLOYMENT to revision $PREVIOUS_REVISION..."

# 执行回滚
kubectl rollout undo deployment/$DEPLOYMENT -n $NAMESPACE --to-revision=$PREVIOUS_REVISION

# 等待回滚完成
kubectl rollout status deployment/$DEPLOYMENT -n $NAMESPACE

echo "Rollback completed successfully!"
```

## 📈 性能优化

### 1. 资源限制

#### 资源配额
```yaml
# k8s/resource-quota.yaml
apiVersion: v1
kind: ResourceQuota
metadata:
  name: hotel-be-quota
  namespace: hotel-be
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
```

### 2. 自动扩缩容

#### HPA 配置
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: hotel-be
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 🚨 故障排查

### 1. 常见问题

#### 服务无法启动
```bash
# 检查 Pod 状态
kubectl get pods -n hotel-be

# 查看 Pod 日志
kubectl logs -f deployment/api-gateway -n hotel-be

# 检查配置
kubectl describe configmap hotel-be-config -n hotel-be
```

#### 数据库连接失败
```bash
# 检查数据库服务
kubectl get svc mysql -n hotel-be

# 测试数据库连接
kubectl exec -it deployment/api-gateway -n hotel-be -- nc -zv mysql 3306

# 查看数据库日志
kubectl logs -f statefulset/mysql -n hotel-be
```

### 2. 性能问题排查

#### 资源使用情况
```bash
# 查看资源使用
kubectl top pods -n hotel-be

# 查看节点资源
kubectl top nodes

# 查看 Pod 详细信息
kubectl describe pod <pod-name> -n hotel-be
```

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队 