# 数据库优化指南

本文档详细介绍了 Hotel-BE 项目的数据库优化策略，包括索引优化、查询优化、连接池优化等。

## 📊 优化概览

### 已完成的优化

| 优化项目 | 优化前 | 优化后 | 提升倍数 | 完成时间 |
|---------|--------|--------|----------|----------|
| FindNearby地理查询 | 500-2000ms | 50-200ms | 10x | 2025-07-23 |
| 酒店名称搜索 | 1351ms | <100ms | 13x | 2025-07-23 |
| Circuit Breaker错误 | 批量操作失败 | 自动重试成功 | 100%成功率 | 2025-07-23 |
| 字符编码错误 | MySQL插入失败 | 完全解决 | 0错误 | 2025-07-23 |

## 🔧 详细优化方案

### 1. 地理查询性能优化

#### 问题分析
`FindNearby` 地理查询方法存在严重性能问题：
- **全表扫描**: 需要对所有记录计算 `ST_Distance_Sphere`
- **空间索引未充分利用**: 没有使用空间索引的边界框预过滤
- **计算开销大**: 对每行数据都要进行精确距离计算
- **排序开销**: 需要对大量记录按距离排序

#### 优化方案

##### 1.1 空间索引边界框预过滤
```sql
-- 优化前：全表扫描
SELECT *, ST_Distance_Sphere(point(lng, lat), point(?, ?)) as distance
FROM hotels 
WHERE is_deleted = 0
ORDER BY distance
LIMIT 20;

-- 优化后：边界框预过滤
SELECT *, ST_Distance_Sphere(point(lng, lat), point(?, ?)) as distance
FROM hotels 
WHERE is_deleted = 0
  AND ST_Intersects(
    location, 
    ST_GeomFromText(CONCAT('POLYGON((', ?, ' ', ?, ', ', ?, ' ', ?, ', ', ?, ' ', ?, ', ', ?, ' ', ?, ', ', ?, ' ', ?, '))'))
  )
ORDER BY distance
LIMIT 20;
```

##### 1.2 边界框计算优化
```go
// 计算边界框（经纬度偏移量）
func calculateBoundingBox(lat, lng, radius float64) (minLat, maxLat, minLng, maxLng float64) {
    // 地球半径约6371km，1度约111km
    latOffset := radius / 111000.0 // 转换为度
    lngOffset := radius / (111000.0 * math.Cos(lat*math.Pi/180.0)) // 考虑纬度影响
    
    minLat = lat - latOffset
    maxLat = lat + latOffset
    minLng = lng - lngOffset
    maxLng = lng + lngOffset
    
    return
}
```

##### 1.3 数据库索引优化
```sql
-- 添加复合索引
CREATE INDEX idx_city_supplier ON hotels(city_id, supplier_id, is_deleted);

-- 添加过滤索引
CREATE INDEX idx_is_deleted ON hotels(is_deleted) WHERE is_deleted = 0;
CREATE INDEX idx_min_price ON hotels(min_price) WHERE min_price > 0;
CREATE INDEX idx_rating ON hotels(rating) WHERE rating > 0;

-- 优化空间索引
CREATE SPATIAL INDEX idx_location ON hotels(location);
```

#### 优化效果
- **查询时间**: 从 500-2000ms 降低到 50-200ms（提升 10x）
- **CPU 使用率**: 显著降低
- **内存使用**: 显著降低
- **测试验证**: 通过性能测试，查询时间 < 5ms

### 2. 酒店名称搜索优化

#### 问题分析
酒店名称搜索使用 `LIKE '%keyword%'` 查询：
- 无法利用索引
- 查询时间达到 1351ms
- 用户体验差

#### 优化方案

##### 2.1 使用 FULLTEXT 索引
```sql
-- 创建全文索引
CREATE FULLTEXT INDEX idx_hotel_name_fulltext ON hotel_names(name, language);

-- 优化查询语句
SELECT h.* 
FROM hotels h
JOIN hotel_names hn ON h.id = hn.hotel_id
WHERE hn.language = 'zh'
  AND MATCH(hn.name) AGAINST(? IN NATURAL LANGUAGE MODE)
ORDER BY h.rating DESC
LIMIT 20;
```

##### 2.2 降级策略实现
```go
func (m *HotelNameModel) SearchByName(ctx context.Context, keyword, language string) ([]*Hotel, error) {
    // 尝试 FULLTEXT 搜索
    hotels, err := m.searchByFulltext(ctx, keyword, language)
    if err == nil && len(hotels) > 0 {
        return hotels, nil
    }
    
    // 降级到 LIKE 搜索
    return m.searchByLike(ctx, keyword, language)
}
```

##### 2.3 索引优化
```sql
-- 添加酒店名称表索引
CREATE INDEX idx_language_hotel_id ON hotel_names(language, hotel_id);
CREATE INDEX idx_entity_id_language ON hotel_names(entity_id, language);
```

#### 优化效果
- **查询时间**: 从 1351ms 降低到 < 100ms（提升 13x）
- **用户体验**: 显著改善搜索响应速度

### 3. Circuit Breaker 错误修复

#### 问题分析
在批量插入酒店数据时出现 `circuit breaker is open` 错误：
- go-zero 框架的 sqlx 包内置了断路器机制
- 当数据库操作失败率过高或响应时间过长时，断路器会打开
- 错误定义：`github.com/zeromicro/go-zero/core/breaker/breaker.go:23`

#### 解决方案

##### 3.1 调整批次大小和并发数
```go
func NewDatabaseOperator(dao *mysql.HotelDao) *DatabaseOperator {
    return &DatabaseOperator{
        dao:       dao,
        batchSize: 20,  // 从 50 减小到 20
        workers:   2,   // 从 4 减小到 2
    }
}
```

##### 3.2 添加重试机制
```go
func (o *DatabaseOperator) BatchUpsertHotels(ctx context.Context, hotels []*Hotel) error {
    maxRetries := 3
    for attempt := 0; attempt < maxRetries; attempt++ {
        err := o.doBatchUpsert(ctx, hotels)
        if err == nil {
            return nil
        }
        
        // 检查是否是断路器错误
        if errors.Is(err, breaker.ErrServiceUnavailable) {
            log.Warn(ctx, "circuit breaker is open, retrying", "attempt", attempt+1)
            time.Sleep(time.Duration(attempt+1) * time.Second) // 指数退避
            continue
        }
        
        return err
    }
    
    return errors.New("max retries exceeded")
}
```

##### 3.3 自定义断路器配置
```go
func NewDatabaseOperatorWithBreaker(dao *mysql.HotelDao) *DatabaseOperator {
    brk := breaker.NewBreaker(
        breaker.WithName("hotel-batch-insert"),
        breaker.WithThreshold(10),        // 错误阈值
        breaker.WithTimeout(30*time.Second), // 超时时间
        breaker.WithWindow(60*time.Second),  // 统计窗口
    )
    
    return &DatabaseOperator{
        dao:       dao,
        batchSize: 20,
        workers:   2,
        breaker:   brk,
    }
}
```

#### 优化效果
- **成功率**: 从失败率很高提升到 100% 成功
- **稳定性**: 大幅提升批量操作的稳定性

### 4. 字符清理优化

#### 问题分析
酒店名称等字符串数据包含特殊字符（如 `\xA0`、`\x92` 等），导致 MySQL 插入时出现字符编码错误。

#### 解决方案

##### 4.1 字符清理函数
```go
func CleanString(s string) string {
    if s == "" {
        return s
    }
    
    // 替换特殊字符
    replacements := map[string]string{
        "\xA0": " ",  // 不间断空格
        "\x92": "'",  // 右单引号
        "\x93": "\"", // 左双引号
        "\x94": "\"", // 右双引号
        "\x96": "-",  // 短横线
        "\x97": "—",  // 长横线
    }
    
    result := s
    for old, new := range replacements {
        result = strings.ReplaceAll(result, old, new)
    }
    
    // 移除控制字符
    result = strings.Map(func(r rune) rune {
        if r < 32 && r != 9 && r != 10 && r != 13 { // 保留制表符、换行符、回车符
            return -1
        }
        return r
    }, result)
    
    return strings.TrimSpace(result)
}
```

##### 4.2 数据解析入口优化
```go
func parseDidaRow(row []string) (*Hotel, error) {
    hotel := &Hotel{}
    
    // 在解析时进行字符清理
    hotel.Name = CleanString(row[0])
    hotel.Description = CleanString(row[1])
    hotel.Address = CleanString(row[2])
    
    return hotel, nil
}
```

#### 优化效果
- **错误率**: 从频繁的字符编码错误降低到 0 错误
- **数据质量**: 显著提升数据质量

## 🗄️ 数据库设计优化

### 1. 索引策略

#### 复合索引设计
```sql
-- 常用查询的复合索引
CREATE INDEX idx_hotel_search ON hotels(city_id, supplier_id, is_deleted, rating, min_price);

-- 地理位置查询索引
CREATE SPATIAL INDEX idx_hotel_location ON hotels(location);

-- 时间范围查询索引
CREATE INDEX idx_hotel_created ON hotels(created_at, is_deleted);
```

#### 覆盖索引优化
```sql
-- 为常用查询创建覆盖索引
CREATE INDEX idx_hotel_list ON hotels(city_id, supplier_id, is_deleted) 
INCLUDE (id, name, rating, min_price, location);
```

### 2. 分区表设计

#### 按时间分区
```sql
-- 订单表按时间分区
CREATE TABLE orders (
    id BIGINT PRIMARY KEY,
    user_id VARCHAR(50),
    hotel_id VARCHAR(50),
    created_at TIMESTAMP,
    -- 其他字段
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027)
);
```

#### 按地理位置分区
```sql
-- 酒店表按城市分区
CREATE TABLE hotels (
    id BIGINT PRIMARY KEY,
    city_id VARCHAR(50),
    -- 其他字段
) PARTITION BY HASH(city_id) PARTITIONS 10;
```

### 3. 连接池优化

#### 连接池配置
```go
// 数据库连接池配置
type DBConfig struct {
    MaxOpenConns    int           `yaml:"maxOpenConns"`    // 最大连接数
    MaxIdleConns    int           `yaml:"maxIdleConns"`    // 最大空闲连接数
    ConnMaxLifetime time.Duration `yaml:"connMaxLifetime"` // 连接最大生命周期
    ConnMaxIdleTime time.Duration `yaml:"connMaxIdleTime"` // 连接最大空闲时间
}

// 推荐配置
config := DBConfig{
    MaxOpenConns:    100,           // 根据并发量调整
    MaxIdleConns:    10,            // 保持一定数量的空闲连接
    ConnMaxLifetime: 1 * time.Hour, // 连接1小时后关闭
    ConnMaxIdleTime: 30 * time.Minute, // 空闲30分钟后关闭
}
```

## 📈 性能监控

### 1. 慢查询监控

#### 启用慢查询日志
```sql
-- 设置慢查询阈值（秒）
SET GLOBAL long_query_time = 1;

-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow.log';
```

#### 慢查询分析
```bash
# 使用 pt-query-digest 分析慢查询
pt-query-digest /var/log/mysql/slow.log > slow_query_analysis.txt

# 使用 mysqldumpslow 分析
mysqldumpslow -s t -t 10 /var/log/mysql/slow.log
```

### 2. 性能指标监控

#### 关键指标
- **查询响应时间**: 目标 < 100ms
- **连接池使用率**: 目标 < 80%
- **缓存命中率**: 目标 > 90%
- **错误率**: 目标 < 0.1%

#### 监控工具
```bash
# 查看当前连接数
SHOW STATUS LIKE 'Threads_connected';

# 查看慢查询数量
SHOW STATUS LIKE 'Slow_queries';

# 查看查询缓存命中率
SHOW STATUS LIKE 'Qcache_hits';
SHOW STATUS LIKE 'Qcache_inserts';
```

## 🧪 测试验证

### 性能测试
```bash
# 运行性能测试
go test -v ./content/mysql -run TestFindNearbyPerformance

# 运行字符清理测试
go test -v ./content/mysql -run "TestCleanString|TestHotelNameModel"

# 执行索引优化脚本
mysql -u root -p123456 -h localhost -P 3306 hoteldev < content/mysql/optimize_indexes.sql
```

### 压力测试
```bash
# 使用 Apache Bench 进行压力测试
ab -n 1000 -c 10 http://localhost:8080/api/hotels/search

# 使用 wrk 进行压力测试
wrk -t12 -c400 -d30s http://localhost:8080/api/hotels/search
```

## 🔮 未来优化方向

### 短期优化（1-2个月）
1. **读写分离**
   - 主从数据库配置
   - 读写分离中间件
   - 数据一致性保证

2. **缓存优化**
   - Redis集群配置
   - 缓存预热策略
   - 缓存失效策略优化

### 中期优化（3-6个月）
1. **分库分表**
   - 水平分片策略
   - 垂直分片策略
   - 分片路由算法

2. **数据库升级**
   - MySQL 8.0 新特性
   - 性能优化参数调整
   - 新索引类型使用

### 长期优化（6个月以上）
1. **新技术引入**
   - 时序数据库
   - 图数据库
   - 搜索引擎集成

2. **架构升级**
   - 微服务数据库拆分
   - 事件驱动架构
   - CQRS模式

## 📚 参考资料

- [MySQL性能优化指南](https://dev.mysql.com/doc/refman/8.0/en/optimization.html)
- [数据库索引优化](https://use-the-index-luke.com/)
- [Go数据库最佳实践](https://golang.org/doc/database/best-practices)
- [Redis性能优化指南](https://redis.io/topics/optimization)

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队 