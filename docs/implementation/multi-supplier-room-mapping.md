# Multi-Supplier Room Mapping Integration - Configuration Updates

## Supplier Rule Enhancements

The supplier rule configuration needs to be updated to support multiple suppliers for increased availability. Here are the recommended changes:

### 1. Update Available Suppliers List

In `search/service/init.go`, the `availableSuppliers` list has been expanded to include:

```go
availableSuppliers: []supplierDomain.Supplier{
    supplierDomain.Supplier_Dida,
    supplierDomain.Supplier_Ctrip,
    supplierDomain.Supplier_DerbySoft,
    supplierDomain.Supplier_HotelBeds,
    // supplierDomain.Supplier_Giata,   // Uncomment when GIATA booking is ready
},
```

### 2. Seller Entity Configuration

Update seller entity rules to allow multiple suppliers per seller:

```json
{
  "rule_type": "seller_in_rule",
  "entity_id": "seller_001",
  "suppliers": [
    {
      "supplier": "dida",
      "priority": 1,
      "conditions": {
        "regions": ["US", "EU"],
        "hotel_categories": ["luxury", "business"]
      }
    },
    {
      "supplier": "ctrip",
      "priority": 2,
      "conditions": {
        "regions": ["CN", "APAC"],
        "hotel_categories": ["economy", "mid-scale"]
      }
    },
    {
      "supplier": "derbysoft",
      "priority": 3,
      "conditions": {
        "fallback": true
      }
    }
  ]
}
```

### 3. Enhanced Room Mapping Rules

```json
{
  "rule_type": "room_mapping_rule",
  "strategy": "multi_provider",
  "providers": [
    {
      "name": "olivier",
      "priority": 1,
      "confidence_threshold": 0.7
    },
    {
      "name": "giata",
      "priority": 2,
      "confidence_threshold": 0.8,
      "enhancement_only": true
    }
  ],
  "merging_strategy": {
    "type": "keep_all_with_mapping",
    "deduplication_threshold": 0.9
  }
}
```

### 4. Fallback Configuration

```json
{
  "fallback_rules": {
    "supplier_failure": {
      "retry_count": 2,
      "fallback_suppliers": ["derbysoft", "hotelbeds"],
      "timeout_ms": 5000
    },
    "mapping_failure": {
      "action": "continue_without_mapping",
      "log_level": "warn"
    }
  }
}
```

## Implementation Notes

1. **Gradual Rollout**: Enable suppliers incrementally to monitor performance impact
2. **A/B Testing**: Use feature flags to test room mapping effectiveness
3. **Monitoring**: Track mapping success rates and supplier response times
4. **Caching**: Implement intelligent caching for room mapping results

## Testing Strategy

1. **Unit Tests**: Test individual mapping providers
2. **Integration Tests**: Test multi-supplier flows end-to-end
3. **Load Tests**: Verify performance with multiple suppliers
4. **Mapping Quality Tests**: Validate room mapping accuracy

## Deployment Checklist

- [ ] Update supplier credentials in configuration
- [ ] Enable new suppliers in staging environment
- [ ] Run regression tests for existing functionality
- [ ] Monitor mapping quality metrics
- [ ] Gradual production rollout with feature flags