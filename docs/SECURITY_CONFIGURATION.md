# 安全配置指南

本文档说明如何正确配置项目中的敏感信息，移除硬编码的账号密码和API密钥。

## 🚨 发现的安全问题

### 1. 硬编码的数据库密码

**问题位置：**
- `api/config/config.yaml`
- `content/config/config.yaml`
- `geography/config/config.yaml`
- `user/config/config.yaml`
- `trade/config/config.yaml`
- `search/config/config.yaml`
- `bi/config/config.yaml`

**风险：** 数据库密码直接暴露在配置文件中

### 2. 硬编码的JWT密钥

**问题位置：**
- `api/config/config.yaml` - JWT secret
- `api/config/config.dev.yaml` - JWT secret
- `api/config/config.uat.yaml` - JWT secret

**风险：** JWT密钥泄露可能导致身份验证绕过

### 3. 硬编码的API密钥

**问题位置：**
- `geography/config/config.yaml` - Google Maps API Key
- `build/api/upload.go` - 七牛云 AccessKey 和 SecretKey

**风险：** 第三方API密钥泄露可能导致服务滥用和费用损失

### 4. 硬编码的超级管理员账号

**问题位置：**
- `api/service/auth.go` - 超级管理员逻辑
- `user/domain/predefined.go` - 预定义用户账号密码

**风险：** 管理员账号密码泄露可能导致系统完全被控制

### 5. 测试代码中的硬编码账号

**问题位置：**
- `admin-fe/tests/e2e/fixtures/testData.ts`
- `admin-fe/tests/e2e/test_cache_mechanism.js`
- `admin-fe/tests/e2e/test_starling_i18n.js`
- `admin-fe/cypress/e2e/login.cy.ts`

**状态：** ✅ 已修复 - 已改为从环境变量读取

## 🔧 解决方案

### 1. 环境变量配置

#### 创建环境变量文件

```bash
# 复制示例文件
cp .env.example .env

# 编辑环境变量文件
vim .env
```

#### 必需的环境变量

```bash
# 数据库配置
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_secure_mysql_password
MYSQL_DATABASE=your_database_name

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_ISSUER=<EMAIL>
JWT_AUDIENCE=hotel-api

# 超级管理员配置
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=your_super_admin_password

# Google Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# 七牛云配置
QINIU_ACCESS_KEY=your_qiniu_access_key
QINIU_SECRET_KEY=your_qiniu_secret_key
QINIU_BUCKET=your_qiniu_bucket
QINIU_DOMAIN=your_qiniu_domain
```

### 2. 配置文件模板化

#### 使用配置模板

```bash
# 复制配置模板
cp api/config/config.template.yaml api/config/config.yaml

# 确保环境变量已设置
source .env

# 启动服务（服务会自动读取环境变量）
go run api/main.go
```

### 3. 代码修改建议

#### 3.1 修改配置加载逻辑

在 Go 代码中使用 `os.Getenv()` 读取环境变量：

```go
package config

import (
    "os"
    "fmt"
)

type DatabaseConfig struct {
    Host     string
    Port     string
    User     string
    Password string
    Database string
}

func LoadDatabaseConfig() *DatabaseConfig {
    return &DatabaseConfig{
        Host:     getEnvOrDefault("MYSQL_HOST", "localhost"),
        Port:     getEnvOrDefault("MYSQL_PORT", "3306"),
        User:     getEnvOrDefault("MYSQL_USER", "root"),
        Password: os.Getenv("MYSQL_PASSWORD"), // 必需，无默认值
        Database: getEnvOrDefault("MYSQL_DATABASE", "hoteldev"),
    }
}

func getEnvOrDefault(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}
```

#### 3.2 修改超级管理员逻辑

```go
// 在 auth.go 中
func (s *AuthService) getSuperAdminCredentials() (email, password string) {
    email = os.Getenv("SUPER_ADMIN_EMAIL")
    password = os.Getenv("SUPER_ADMIN_PASSWORD")
    
    if email == "" || password == "" {
        logx.Error("Super admin credentials not configured")
        return "", ""
    }
    
    return email, password
}
```

### 4. 部署环境配置

#### 4.1 开发环境

```bash
# 设置开发环境变量
export MYSQL_PASSWORD="dev_password"
export JWT_SECRET="dev-jwt-secret-at-least-32-characters"
export SUPER_ADMIN_EMAIL="<EMAIL>"
export SUPER_ADMIN_PASSWORD="dev_admin_password"
```

#### 4.2 生产环境

```bash
# 使用强密码和复杂密钥
export MYSQL_PASSWORD="$(openssl rand -base64 32)"
export JWT_SECRET="$(openssl rand -base64 48)"
export SUPER_ADMIN_PASSWORD="$(openssl rand -base64 24)"
```

#### 4.3 Docker 部署

```yaml
# docker-compose.yml
version: '3.8'
services:
  hotel-api:
    image: hotel-api:latest
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - SUPER_ADMIN_EMAIL=${SUPER_ADMIN_EMAIL}
      - SUPER_ADMIN_PASSWORD=${SUPER_ADMIN_PASSWORD}
    env_file:
      - .env
```

#### 4.4 Kubernetes 部署

```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: hotel-api-secrets
type: Opaque
data:
  mysql-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-secret>
  super-admin-password: <base64-encoded-password>

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hotel-api
spec:
  template:
    spec:
      containers:
      - name: hotel-api
        image: hotel-api:latest
        env:
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: hotel-api-secrets
              key: mysql-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: hotel-api-secrets
              key: jwt-secret
```

## 🔒 安全最佳实践

### 1. 密码策略

- **数据库密码：** 至少16位，包含大小写字母、数字和特殊字符
- **JWT密钥：** 至少32位随机字符串
- **管理员密码：** 至少12位复杂密码

### 2. 密钥轮换

- **定期轮换：** 每3-6个月轮换一次密码和密钥
- **泄露响应：** 发现泄露时立即轮换所有相关密钥
- **版本管理：** 保留旧密钥一段时间以支持平滑过渡

### 3. 访问控制

- **最小权限：** 每个服务只获得必需的权限
- **网络隔离：** 数据库和Redis不应直接暴露到公网
- **审计日志：** 记录所有敏感操作的日志

### 4. 监控和告警

- **异常登录：** 监控超级管理员账号的异常登录
- **API调用：** 监控第三方API的异常调用
- **配置变更：** 监控配置文件的变更

## 📋 检查清单

### 开发阶段

- [ ] 所有配置文件已模板化
- [ ] 环境变量已正确设置
- [ ] 硬编码密码已移除
- [ ] 测试用例使用环境变量
- [ ] .gitignore 已更新

### 部署阶段

- [ ] 生产环境使用强密码
- [ ] 密钥已安全存储
- [ ] 网络访问已限制
- [ ] 监控已配置
- [ ] 备份策略已制定

### 运维阶段

- [ ] 定期密钥轮换
- [ ] 安全审计
- [ ] 漏洞扫描
- [ ] 应急响应计划

## 🆘 应急响应

### 密钥泄露处理

1. **立即轮换** 所有相关密钥
2. **检查日志** 查找异常访问
3. **通知团队** 告知安全事件
4. **更新文档** 记录事件和处理过程

### 联系方式

- **安全团队：** <EMAIL>
- **运维团队：** <EMAIL>
- **紧急联系：** +86-xxx-xxxx-xxxx

## 前端安全配置

### 测试账号配置

前端项目中的测试账号已从硬编码改为环境变量配置：

```bash
# 前端环境变量配置 (admin-fe/.env.local)
VITE_TEST_SUPER_EMAIL=<EMAIL>
VITE_TEST_SUPER_PASSWORD=your-super-password
VITE_TEST_ADMIN_EMAIL=<EMAIL>
VITE_TEST_ADMIN_PASSWORD=admin-password
VITE_TEST_USER_EMAIL=<EMAIL>
VITE_TEST_USER_PASSWORD=user-password
```

### Cypress 测试配置

```bash
# Cypress 环境变量配置 (admin-fe/cypress.env.json)
{
  "TEST_ADMIN_EMAIL": "<EMAIL>",
  "TEST_ADMIN_PASSWORD": "test-password",
  "TEST_API_BASE_URL": "http://localhost:8888"
}
```

### 前端安全文件

以下文件已添加到 `.gitignore`：
- `admin-fe/.env.local`
- `admin-fe/.env.*.local`
- `admin-fe/cypress.env.json`
- `admin-fe/config.local.*`
- `admin-fe/*.key`, `admin-fe/*.pem`

## 相关文档

- [前端安全配置指南](../admin-fe/docs/SECURITY_CONFIGURATION.md)
- [部署指南](../build/README.md)
- [开发环境设置](../build/dev/README.md)
- [数据库迁移](../build/ddl/README.md)

---

**注意：** 本文档包含敏感信息配置指南，请确保只有授权人员可以访问。