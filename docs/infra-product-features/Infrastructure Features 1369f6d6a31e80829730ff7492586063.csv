﻿Feature Name,Module,Feature Description,Priority,MVP,MVP Implementation,Phase 2,Phase 3 / Unplanned,BE Estimated PD (boosting&optimal),Difficulty level,Cloud cost,Improvement Space
Usage Limits,Business,"This function allows the business to define rate limits for their overall business (all customers) and for each customer (api key) 

Queries per second/minute ",P1,Yes,Needs to be further defined,,,1,Easy,Cheap,Medium but little value
BI & Monitoring Tools,BI,"To start with some basic dashboards for booking trends, commercial performance. To extend API monitoring and performance of suppliers and customers performance",P0,Yes,Few critical dashboards will be defined,To extend API monitoring and performance of suppliers and customers performance,,5,Super Hard,Super costly,Large and impactful
"AWS Portal Access (Shared vs Dedicated
  clusters)",Business,"To figure out the right architecture deployment for our customers, and to ensure the price point is line with industry benchmarks, while ",P0,Yes,Critical P0s will be defined together with RD,,,,Unknown,,
Hotel & room image link masking,"Business, CMS",,P0,Yes,,,,1,Easy,Medium,Little and no value
GDPR Compliance,Tech,,,No,,,,2,Medium,Cheap,Nice to hive
Mailing /Notification System,"Business, Platform","Multiple emails will be sent by the platform to  customer and suppliers (booking notifications); to customer (system reminders), to customer & supplier (low-credit) etc ",P0,Yes,Critical P0s will be defined together with RD,,,1,Easy,Medium,Nice to hive
Templates for various emails,"Business, Platform",We need to have some standard templates that the business can customize (similar to visa template of BD),P0,Yes,Critical P0s will be defined together with RD,,,1,Easy,Cheap,Medium but little value
Push notifications,Business,"The business would get push notifications of orders, cancellations, failed bookings, timeout bookings etc (similar to emails)",P0,Yes,Critical P0s will be defined together with RD,,,2,Easy,Cheap,Medium and medium
Logs viewer,Business,"The business needs a way to view the searct to book logs of their bookings (confirmed, cancelled, failed, etc) … The log types are supplier + internal (markups, rules etc), external (customer) api out ",P0,Yes,,,,3,Medium,Super costly,Medium and medium
"Automated processes for
  cleaning up old session data",Platform,To clean up session / search data every 10 days,P0,Yes,,,,1,Easy,None,Nice to hive
Rebook (Post-try),Business,To allow business to rebook existing refundable bookings at a cheaper price,P1,??,,Yes,,3,Medium,Cheap,Large and impactful
SmartBook (Retry),Business,"1- To allow business to rebook a better price at the booking confirmation time from other sources

2- To allow business to rebook a higher price if the original booking fails with the supplier ",P0,??,,,,2,Medium,Cheap,Large and impactful
API OUT Business,Platform,"To have a standardized platform API out, that the business can use for their front-end (This is direct to business API) which will include more information such as supplier name, cost price, markup etc",P0,Yes,,,,3,Medium,Cheap,Medium and medium
API OUT Customer,Platform,"To have a standardized platform API out, that the business can use for their API Customers (This is business to business API) which will include less information such as supplier name, cost price, markup etc",,Yes,,,,2,Medium,Cheap,Medium and medium
API OUT WRAPPER,Platform,To wrap our universal API out (Customer) in our competitor format so business customers can easily integrate in,P1,No,,,,3,Hard,Cheap,Medium and medium
Customer Management Portal,Business,"Gives the business a way to manage their customers ",P0,Yes,,,,2,Medium,Cheap,Large and impactful
Customer Credit Limit,Business,The customer credit limit will be used to validate if the customer has sufficient funds to book or not (it is a like fake wallet),,,,,,1,Easy,None,Medium and medium
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,
,,,,,,,,,,,