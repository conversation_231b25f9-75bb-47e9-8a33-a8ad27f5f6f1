# Customer Credit Limit

BE Estimated PD (boosting&optimal): 1
Cloud cost: None
Difficulty level: Easy
Feature Description: The customer credit limit will be used to validate if the customer has sufficient funds to book or not (it is a like fake wallet)
Improvement Space: Medium and medium
Module: Business

Credit limit >>

The business needs to define the allowed credit limit per customer group

The customer cannot book if they have consumed their credit limit

- How to calculate credit limit?
    - Based on booking creation
    - Based on cancellation policy = non-refundable
- For example, customer A has 100,000 limit
    - based on booking creation, everytime user makes a booking , the booking amount will be deducted from the credit limit
    - based on cxl policy, the credit limit will only be deducted if the booking has reached non-refundable status