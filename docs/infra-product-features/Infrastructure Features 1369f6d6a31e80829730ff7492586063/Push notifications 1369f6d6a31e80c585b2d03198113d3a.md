# Push notifications

Priority: P0
BE Estimated PD (boosting&optimal): 2
Cloud cost: Cheap
Difficulty level: Easy
Feature Description: The business would get push notifications of orders, cancellations, failed bookings, timeout bookings etc (similar to emails)
Improvement Space: Medium and medium
MVP: Yes
MVP Implementation: Critical P0s will be defined together with RD
Module: Business

### 2. Notifications for agents / managers

**Agent Confirmation**

**Proforma Invoice**

### 3. Reports

**Orders near cancellation policy**

This push will send the agents & the client a daily report for all segments that match the above conditions:

- Their status is "OK".
- Approaching cancellation policy start date (7 days of delta).
- NOT paid.
- Check-In date is not passed.

**New and cancelled reservations**

This push will send the agents & the client a daily report for all orders that are:

- Were created today (GMT+4 time).
- Were cancelled today (GMT +4time).

**Failed reservation attempts (orders error report)**

This PUSH will send the client a daily report for reservation attempts that were not completed successfully due to an error.

In addition, various error codes have different meanings - therefore, we will create an error library with explanation on almost each error code.

**Insufficient credit notification (out of credit)**

This PUSH will notify the client when the supplier couldn't confirm a booking due to credit limit reached.

It is very useful and helpful the business to re-fill your credit with the supplier and be able to confirm bookings again.