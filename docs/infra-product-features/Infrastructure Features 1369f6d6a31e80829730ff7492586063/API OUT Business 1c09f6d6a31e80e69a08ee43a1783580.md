# API OUT Business

Priority: P0
BE Estimated PD (boosting&optimal): 3
Cloud cost: Cheap
Difficulty level: Medium
Feature Description: To have a standardized platform API out, that the business can use for their front-end (This is direct to business API) which will include more information such as supplier name, cost price, markup etc
Improvement Space: Medium and medium
MVP: Yes
Module: Platform

# Booking Flow

Needs to be further improved by analyzing key supplier APIs and create our own universal API out req/res

| **Request** | **Explanation** |
| --- | --- |
| Search Hotels | Hotel Search request allows you to search for Hotels, either by Geolocations or by HotelIDs. |
| Get Packages | Retrieves the Rooms details and information of a specific Hotel. |
| Get Cancellation Policy | This function sends a request for the cancellation policy and should be used before sending a booking request. |
| Get Payment Preferences | Payment Preferences is used to retrieve the booking payment possibilities for a selected package. |
| Book | Submits a new booking request for the selected hotel and returns the booking reference and booking status. |