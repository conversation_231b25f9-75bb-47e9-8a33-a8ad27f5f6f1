# Hotel & room image link masking

Priority: P0
BE Estimated PD (boosting&optimal): 1
Cloud cost: Medium
Difficulty level: Easy
Improvement Space: Little and no value
MVP: Yes
Module: Business, CMS

> What is CDN ?
> 
> 
> A Content Delivery Network (CDN) - is a network of servers spread across different locations to enhance web content delivery, including web pages, images, videos, and digital assets. CDNs aim to boost content performance and availability while relieving strain on a single web server.
> 

HSP provides more than just static and dynamic content delivery, it also offers the option to **mask original supplier image URLs** by displaying them through your own domain.

In both, each image, whether it's for a hotel or a room, is returned with a concealed URL from HSP side. This adds an additional layer of masking, concealing both the supplier's name and the original link.

### Example

Image URL - as returned from Supplier :

> https://www.tboholidays.com//imageresource.aspx?img=FbrGPTrju5e5v0qrAGTD8pPBsj8/wYA59yZzciIrL6JD9kapBHAHEQ+koW52BsGJsqZ61qIO/OGEFQ3mDV4W8Xc8q4lTqGLkmDtjYxBfgHvSlXLRr8fx9w==
> 

Image URL - as returned from TRAVEL LOOP, with url masking :

[https://dimg04.loop.com/images/0226u12000aoavp1p3907_R_600_400_Q90_Mht_1_PSMALLBEAUTY.jp](https://dimg04.loop.com/images/0226u12000aoavp1p3907_R_600_400_Q90_Mht_1_PSMALLBEAUTY.jp)