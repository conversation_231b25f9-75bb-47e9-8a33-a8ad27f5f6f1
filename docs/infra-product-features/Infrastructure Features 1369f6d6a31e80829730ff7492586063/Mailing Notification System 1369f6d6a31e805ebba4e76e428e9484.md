# Mailing /Notification System

Priority: P0
BE Estimated PD (boosting&optimal): 1
Cloud cost: Medium
Difficulty level: Easy
Feature Description: Multiple emails will be sent by the platform to  customer and suppliers (booking notifications); to customer (system reminders), to customer & supplier (low-credit) etc 
Improvement Space: Nice to hive
MVP: Yes
MVP Implementation: Critical P0s will be defined together with RD
Module: Business, Platform

An automated SMTP service sends out emails containing vouchers and invoices to relevant parties such as:
Business, Customer, Supplier, End user email. Each email has a different template, and the Business can define this in their admin portal. 

Every booking will automatically trigger emails for these different types.

When: Booking Confirmation & Cancellation, Errors, Payment Failures, HotelConfirmation Numbers, Timeouts

| **Property** | **Business Confirmation** | **Customer Confirmation** | **Invoice** | **Voucher** |
| --- | --- | --- | --- | --- |
| To Who? | Internal user/email of the business | Customer email  | Customer Email | Customer Email |
| **Includes Price?** | Yes | Yes | Yes | No |
| Which Price? | Net and Sell price, supplier and customer | Sell Price | Selling Price | No |
| **Currency** | Supplier Currency + Customer Currency | Customer currency | Customer currency | - |