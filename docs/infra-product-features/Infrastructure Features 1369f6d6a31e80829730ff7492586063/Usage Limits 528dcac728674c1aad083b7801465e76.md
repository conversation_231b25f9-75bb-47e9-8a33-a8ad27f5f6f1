# Usage Limits

Priority: P1
BE Estimated PD (boosting&optimal): 1
Cloud cost: Cheap
Difficulty level: Easy
Feature Description: This function allows the business to define rate limits for their overall business (all customers) and for each customer (api key) 

Queries per second/minute 
Improvement Space: Medium but little value
MVP: Yes
MVP Implementation: Needs to be further defined
Module: Business

Throttling Settings in Travel Loop are designed to protect our system against misuse and to regulate the frequency of service requests, particularly those from API clients with improper implementation.

**Default System Configuration**

A Super Admin Business User sets default throttling limits across the system, which will serve as a baseline for all Customers.

**Customer-Specific Adjustments**

System admins can set limits on a user basis *typically applicable for API users. This flexibility allows tailored control when specific users require adjustments due to detected misuse or implementation issues.