# SmartBook (Retry)

Priority: P0
BE Estimated PD (boosting&optimal): 2
Cloud cost: Cheap
Difficulty level: Medium
Feature Description: 1- To allow business to rebook a better price at the booking confirmation time from other sources

2- To allow business to rebook a higher price if the original booking fails with the supplier 
Improvement Space: Large and impactful
MVP: ??
Module: Business

# Overview

SmartBook is crafted to **enhance the success rate** and **profitability of hotel bookings**.

Bookings might not go through due to issues like the supplier declaring a "sold out", "price change" or other errors at the time of booking. SmartBook empowers the system to exhaust all options to ensure a booking is successful at the chosen hotel.

The main aim of SmartBook is to streamline the process of booking the desired room or rooms at the specified price, all in a single transaction.

Once the booking request is initiated, no additional steps are needed.

This feature is built with an advanced business logic layer, **designed to maximize profit and boost the likelihood of a successful booking**, even in scenarios like room unavailability or fluctuating prices.

When activated, SmartBook will search for an alternative package than what was initially requested by the user. However, this alternative **will always be of equal or superior quality in terms of room specifications, board options, cancellation policy, refundability, and will fall within an acceptable price range deviation.**

# SmartBook modes

1. **ProfitGen -**ProfitGen operates by searching for more affordable rates while keeping the room type, basis, cancellation policy, and other factors unchanged. It then secures the more economical rate, aiming to enhance and maximize profits for each booking inquiry.
2. **ErrorRecovery -**ErrorRecovery triggers when a booking fails due to sold-out rates, unavailability, or price changes. It then locates a comparable rate for the unsuccessful booking and rescues the booking attempt.

## 🚧

# SmartBook Development requirements

# Configuration

Once the SmartBook is enabled, you can navigate to the "SmartBook Settings" by following the steps :

## Configuration Explained

### 2. Cancellation Policy Settings

![](https://files.readme.io/23e80ab-image.png)

| **Field** | **Description** |
| --- | --- |
| Allow fallback to NFR CXL Policy | When checked- the system will create and assign a NFR CXL for packages which the user called to get CXL for , yet an error occured. |

### Book user's selection first if ...

![](https://files.readme.io/c3f84f8-image.png)

| **Field** | **Description** |
| --- | --- |
| Candidates profit increase is less then | If the profit increase is less then fixed amount/percentage from the selected package - book the selected package. |
| Its contract is in | If the contract is from predefined contract group - book the selected package. |
| Its property ID is in | if the HotelID is from predefined hotels - book the selected package. |
| Its property country is in | if the Hotels country is from predefined country list - book the selected package. |
| Its property chain is in | if the Hotels chain is from predefined chain list - book the selected package. |

### 4. If the users selection failed

![](https://files.readme.io/f1524c3-image.png)

| **Field** | **Description** |
| --- | --- |
| Profit forfeit % is no more than | In Error Recovery mode, this parameter specifies the percentage of Net Profit or Markup that the partner is willing to give-up to salvage the booking.**Example:**Net Price: $100Markup: $50Total Price: $150Profit Forfeit Percentage: 50% .In this scenario, if the original booking fails, Smartbook will attempt to book an alternative room to salvage the booking. It will use the Net Price ($100) plus up to 50% of the Markup ($25), **allowing a maximum price of $125 from another supplier to save the booking.** |