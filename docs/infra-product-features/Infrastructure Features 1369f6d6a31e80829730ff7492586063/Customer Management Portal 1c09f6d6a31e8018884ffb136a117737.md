# Customer Management Portal

Priority: P0
BE Estimated PD (boosting&optimal): 2
Cloud cost: Cheap
Difficulty level: Medium
Feature Description: Gives the business a way to manage their customers 
Improvement Space: Large and impactful
MVP: Yes
Module: Business

This feature enables the creation of a **parent-child customer structure**, allowing for centralized management while supporting multiple operational units. Users (both API and human) are managed at the **account level**, with configurable access and linkage logic.

### **Core Structure**

- **Parent Customer**: Represents the top-level organization (e.g., *Emirates*).
- **Child Customers / Accounts**: Sub-units or departments under the parent (e.g., *Dnata*, *Emirates Airlines*, *Arabian Adventures*, *Call-Center*).

### **User Types**

1. **API Users**
    - Each **child account** can have up to **3 API keys**.
    - **One API key** is strictly associated with **one child account** only.
    - Designed for system-to-system integration (e.g., automated booking systems, back-office operations).
2. **Person Users**
    - A **person user** (e.g., an employee or agent) can be linked to **multiple child accounts**.
    - Enables flexible access for users working across different sub-units under the same parent customer.

### **Example: Emirates Group**

- **Parent Customer**: Emirates
- **Child Accounts**:
    - Account 1: Dnata
    - Account 2: Emirates Airlines
    - Account 3: Arabian Adventures
    - Account 4: Call-Center
- **Users**:
    - **API Users**:
        - API User 1 → linked to Account 1 (Dnata)
        - API User 2 → linked to Account 2 (Emirates Airlines)
        - API User role: will define it the key can access “content”, “Search”, “booking” etc apis
    - **Person Users**:
        - User A → linked to Accounts 1, 2, 3, and 4 (multi-account access)
        - Person User role: will define the role on the front-end
            - Super admin: can create more users, edit, delete etc
            - Super Agent: All of below, but not super admin
            - Agent: can search ./ can book/ can canel
            - View only: can search only
            - Finance: Can view report/download bookings