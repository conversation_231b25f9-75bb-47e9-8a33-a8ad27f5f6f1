# Rebook (Post-try)

Priority: P1
BE Estimated PD (boosting&optimal): 3
Cloud cost: Cheap
Difficulty level: Medium
Feature Description: To allow business to rebook existing refundable bookings at a cheaper price
Improvement Space: Large and impactful
MVP: ??
Module: Business
Phase 2: Yes

# Rebooker

# Overview

Rebooker is a post-booking process that maximizes profit for existing bookings.

Rebooker enables the offline rebooking of existing refundable bookings:

- After the booking was made, The Rebooker will track changes of the available Net rates for the hotel room booked by the consumer.
- Based on predefined logic, like a price drop of more than X%, the Rebooker will book the same accommodation from the same or alternative provider of the inventory and cancel the original reservation automatically.

> 👍NOTE :
> 
> 
> It’s important to note that this feature is done only for cancellable bookings, so no risks are taken.
> 

**The Rebooker process goes as follows :**

1. Rebooker will scan the existing, active and refundable segments periodically for cheaper prices.
2. Once a cheaper price is found - Rebooker will attempt to book it.
3. If the booking was confirmed by the supplier, <PERSON>booker will cancel the previous active booking automatically.
4. Rebooker will update the segment with the new room pricing and information.
5. The room rate which was booked originally and which was canceled by <PERSON>book<PERSON> will be archived.
6. A notification will be sent to the Client

# Features

- **Active Booking Rule -** Rebooker ensures that at least one active and confirmed booking exists; even if something went wrong during the Rebooker booking process or during the Rebooker cancellation process. This prevents a scenario where the end-customer will remain without any booking.
- **Configurable -** Rebooker activity can be configured according to the company’s rules in order to meet business needs.
- **Automated -** Rebooker is fully automated with no manual intervention needed. Rebooker automatically cancels the original booking, as long as the full reservation criteria are met: same or better cancellation policy, room board, room category, and bedding.
- **Push Notification -** Once a new Rebooking is made, a push notification will be sent to the specified endpoint URL.
- **Rebooking History -** The full history of the rebooked segment is visible, either through the Orders section in the Back-Office or through the API request ‘GetSegmentFinReport’.This allows admins/customer support/operations team users to see the original booking that was booked and what Rebooker actually booked and when.
- **Zero Implementation Efforts -** Rebooker is designed to be compatible with the existing implementation of  API and the Client's current work-flows.

---

# Configuration

The configuration menu can be accessed by heading to the Back-Office , and then entering the ‘Admin’ panel using an admin user, clicking on the root node / user, and then selecting ‘Rebooker Settings’.

### Allowed Contracts for Booking

Choose from the following options:

**All Contracts:** Rebooker will search for profit-increasing rates from all available contracts under the affiliate.

**Specific Contract Group:** Define a specific contract group for Rebooker to exclusively search within.

**User's Active Contracts:** Rebooker will search for profit-increasing rates only among contracts activated for the specific user.

## 2. Rebooker Candidates

![](https://files.readme.io/5c3c936-image.png)

### Its total amount is over

The rebooker candidates will be packages only if the profit increase is over the defined amount .

### Its days prior to check-in date is

Rebooker candidates will be packaged only if their check-in date is one day prior to the rebooking date.

### Its days prior to free cxl date is

Rebooker candidates will be packaged only if their cxl-policy free days are at least X days from the current date.

## 3. Exclude From Rebooker

![](https://files.readme.io/6a20524-image.png)

### Its ID is in

SegmentIDs which will be excluded from rebooker .

### Its Property ID is in

HotelID which will be excluded from rebooker .

### Its property country is in

Countries which will be excluded from rebooker .

### Its property chain is in

Hotel Chain which will be excluded from rebooker.

### Was originally booked from

Suppliers which will be excluded from rebooker .

## 4. Rebook Only If

![](https://files.readme.io/d290343-image.png)

### Profit increase is more than

Rebooker will only rebook packages with profit increase more than amount / percentage .

---