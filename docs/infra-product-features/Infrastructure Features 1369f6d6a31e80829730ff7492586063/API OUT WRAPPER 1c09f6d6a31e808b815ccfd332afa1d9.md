# API OUT WRAPPER

Priority: P1
BE Estimated PD (boosting&optimal): 3
Cloud cost: Cheap
Difficulty level: Hard
Feature Description: To wrap our universal API out (Customer) in our competitor format so business customers can easily integrate in
Improvement Space: Medium and medium
MVP: No
Module: Platform

To create a wrapper of our API response in the response structure of a competitor.

API request & response needs to be wrapped so customers can easily connect with us seamlessly