# AWS Portal Access (Shared vs Dedicated
  clusters)

Priority: P0
Difficulty level: Unknown
Feature Description: To figure out the right architecture deployment for our customers, and to ensure the price point is line with industry benchmarks, while 
MVP: Yes
MVP Implementation: Critical P0s will be defined together with RD
Module: Business

# Overview

**Cloud Infrastructure**

Our cloud infrastructure should be designed to offer scalable, efficient, and reliable computing resources. Leveraging the power of cloud computing, we ensure that our services are always available, responsive, and capable of handling evolving business needs.

**Choice of AWS Cloud**

AWS Cloud has been our choice for cloud services owing to its robust scalability, exceptional reliability, comprehensive security measures, and diverse range of services. AWS global presence and continuous innovation align perfectly with our goals for digital transformation.

**Key AWS Services and Features**

Our architecture incorporates several aws services:

- aws Virtual Machines for flexible, scalable computing power.
- aws Storage for high-performance, secure data storage.
- aws SQL Database for reliable, managed database services.
- aws Kubernetes Service (AKS) for efficient container orchestration.

**Security and Compliance**

Security is paramount. We implement rigorous network security protocols, data encryption standards, and identity and access management policies. Compliance with industry standards is strictly adhered to, ensuring data protection and privacy.

**Scalability and Performance Optimization**

Our aws infrastructure is built for scalability, enabling us to efficiently manage workload fluctuations. Performance optimization is a continuous process, ensuring high availability and minimal latency in our services.

---

# Shared & Dedicated Cluster

travelLoop provides two distinct options for our API and portal clients, catering to different business needs and scales:

### Shared Cluster:

**What It Is:**

The Shared Cluster is a communal environment used by our clients for various API activities such as searches, bookings, and other related functions, as well as for Back-Office operations.

**Who It's For:**

Ideal for small to medium-sized businesses.

**How It Works:**

In this shared setting, clients operate alongside others. However, it's important to note that excessive use, spike or stress testing by any client in the Shared Cluster could lead to slower response times for everyone using that cluster.

> 👍NOTE:
> 
> 
> Clients with high activity within the "Shared Cluster" **will be subject to throttling measures** to ensure system performance is maintained and to prevent any adverse impact on other clients
> 

### Private/Dedicated Cluster:

**What It Is:**

The Private/Dedicated Cluster offers an exclusive environment tailored for each client.

**Who It's For:**

Suited for larger businesses seeking control and independence from the shared environment.

**How It Works:**

Operations in this cluster are isolated, ensuring your activities are not impacted by other clients. This exclusive 'space' guarantees that your processes run smoothly and efficiently.

> 👍NOTE:
> 
> 
> If you **expect to have a high number of searches** and would like to have your own dedicated cluster, it's doable, take into consideration it comes with costs .
> 

# Estimating Your Azure Costs

The cost analysis for your Dedicated Cluster on aws is influenced by a variety of factors including client trends and the resources you use. Key components contributing to the cost include Virtual Machines (VMs), Storage solutions, Bandwidth usage, Virtual Networking, Load Balancers, among others.

> 👍NOTE:
> 
> 
> **For an estimation of your aws costs, please use** - [a](https://azure.microsoft.com/en-us/pricing/calculator/)ws pricing calculator (Azure example: [https://azure.microsoft.com/en-us/pricing/calculator/](https://azure.microsoft.com/en-us/pricing/calculator/))
> 

---

### Case study: Cost Estimation 1

**Client Type:**

- B2C Client.
- Running multiple campaigns.
- Client that using API users.
- **Clients average Searches per day - 5,773**
- Monthly average - $1,542.

| **Service** | **Resource Group** | **Tier** | **Meter** | **Resources** | **Cost (USD)** |
| --- | --- | --- | --- | --- | --- |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dv3 Series Windows | D4 v3/D4s v3 Spot | 1 | $161 |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dsv5 Series Windows | D4s v5 | 1 | $612 |
| Bandwidth | Virtual machine scale set | Rtn Preference: MGN | Standard Data Processed - Ingress | 5 | $39 |
| Bandwidth | Virtual machine scale set | Rtn Preference: MGN | Inter Continent Data Transfer Out - NAM or EU West | 4 | $102.2 |
| Storage | Disk | Standard HDD Managed Disks | S10 LRS Disk | 265 | $121 |
| Storage | Storage account |  | LRS Write Operations | 2 | $73 |
| Storage | Disk | Standard HDD Managed Disks | S4 LRS Disk Operations | 356 | $111 |
| Load Balancer | Load balancer | Load Balancer | Standard Data Processed | 2 | $215 |
| Event Hubs | Event Hubs namespace | Event Hubs | Standard Ingress Events | 1 | $108 |
|  |  |  |  | **Monthly Cost** | **$1,542** |

---

### Case study: Cost Estimation 2

**Client Type:**

- Client that using multiple Agents + API users.
- Client is using cache.
- **Clients average Searches per day - 562,896**
- Monthly average - $3,506.

| **Service** | **Resource Group** | **Tier** | **Meter** | **Resources** | **Cost (USD)** |
| --- | --- | --- | --- | --- | --- |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dv3 Series Windows | D4 v3/D4s v3 Spot | 1 | $1,373 |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dsv5 Series Windows | D4s v5 | 1 | $1,111 |
| Bandwidth | Virtual machine scale set | Rtn Preference: MGN | Standard Data Processed - Ingress | 7 | $154.74 |
| Load Balancer | Load balancer | Load Balancer | Standard Data Processed | 2 | $315 |
| Bandwidth | Virtual machine scale set | Rtn Preference: MGN | Inter Continent Data Transfer Out - NAM or EU West | 6 | $23.06 |
| Storage | Disk | Standard HDD Managed Disks | S10 LRS Disk | 485 | $165 |
| Storage | Storage account |  | LRS Write Operations | 2 | $168 |
| Storage | Disk | Standard HDD Managed Disks | S4 LRS Disk Operations | 511 | $179 |
|  |  |  |  | **Monthly Cost** | **$3,506.81** |

---

### Case study: Cost Estimation 3

**Client type:**

- Clients using multiple distribution channels.
- Clients using multiple Agents + API users.
- High count of searches per day.
- **Clients average Searches per day - 2,004,752**
- Monthly average - $5,435.

| **Service** | **Resource Group** | **Tier** | **Meter** | **Resources** | **Cost (USD)** |
| --- | --- | --- | --- | --- | --- |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dv3 Series Windows | D4 v3/D4s v3 Spot | 1 | $1762 |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dsv5 Series Windows | D4s v5 | 1 | $1284 |
| Bandwidth | Virtual machine scale set | Rtn Preference: MGN | Standard Data Transfer Out | 4 | $536 |
| Virtual Network | Virtual machine scale set | Virtual Network Private Link | Standard Data Processed - Ingress | 2 | $457 |
| Load Balancer | Load balancer | Load Balancer | Standard Data Processed | 2 | $412 |
| Virtual Machines | Virtual machine scale set | Virtual Machines Dsv5 Series Windows | D16s v5 Spot | 1 | $245 |
| Storage | Disk | Standard HDD Managed Disks | S10 LRS Disk | 514 | $222 |
| Storage | Storage account | General Block Blob | LRS Write Operations | 2 | $185 |
| Storage | Disk | Standard HDD Managed Disks | S4 LRS Disk Operations | 511 | $184 |
| Storage | Storage account | General Block Blob | LRS Data Stored | 2 | $148 |
|  |  |  |  | **Monthly Cost** | **$5,435** |

---

# Optimization

As previously mentioned, the costs associated with your Dedicated Cluster on aws are affected by several factors.

This section aims to provide guidance on optimizing these costs and your aws usage through various methods. These include managing session and data storage, optimizing search processes, limitations, scaling, and more.

## Stateless Search

Each interaction with our API, including actions like Search, GetPackages, cxl-policy, book, and others, along with transactions and bookings, are tracked and logged in our Glogs - a dedicated session viewer tool.

This data, stored on client storage, contributes to aws costs.

For users engaged in frequent searches or those who require caching or meta-searches, we've designed the 'Stateless' Search method.

This request enables you to execute searches without saving session data and associated dumps.

By doing so, it offers a way to minimize the storage requirements for your data.

## Storage

We track and log every interaction with our API, including searches, get packages, CXL policies, bookings, and more.

However, storing all this information can grow expensive. To keep costs down and optimize your aws usage, we offer "Reduce Dumping".

What does "Reduce Dumping" do? It shrinks the size of your session logs, minimizing data storage requirements and lowering your aws costs. In simple terms, you get the same great insights with less data and expense.

In order to enable the "Reduce Dumping" feature - customer can contact us/ managed by setting.

## Throttling

Managing multiple distribution channels, including different agents and API users, can be difficult, particularly when tracking their usage.

To address this challenge, Throttling Settings have been implemented. These settings are crucial for preventing system abuse and controlling the usage of specific services and request types. This is especially important in scenarios where API clients might improperly implement these services.

more information about Throttling:

Throttling Settings are designed to prevent system abuse and limit the usage of certain services and request types, particularly in cases of improper implementation, often by API clients.

These settings are initially established at the system-wide level by a Super Admin User, serving as the default configuration for all users of the system.

However, these default settings can be subsequently modified for individual system users as needed.

Beyond these system-wide settings, Throttling Settings can also be tailored for particular system users. This is typically done when a system administrator detects misuse or incorrect implementation by specific users.

## Configuration

To configure throttling for a specific user :

1. in the Back-office, navigate to "Manage company" .
2. Click on the desired user to configure "Throttling" to .
3. Click on the "Throttling Settings" and then "Add".
4. Configure the throttling setting.

![](https://files.readme.io/aea8ab9-image.png)

| **Field** | **Description** |
| --- | --- |
| Service Type | Choose the service to apply throttling to, such as Hotels, Cars, Payment, Orders, etc. |
| Service Request Type | Select the specific method within the chosen service. |
| Seconds | Specify the time frame in which the user is allowed to send transactions. |
| Transactions | Define the number of requests a user can send within the set time frame in seconds. |
| Deployment | Determine the environment or cluster where the call limits/throttling will be applied. |

Example :If set to 10 seconds and 10 transactions, a user can make up to 10 calls within 10 seconds for the chosen method. This can be distributed as one request per second or 10 calls in one second, followed by a 9-second pause. This setting is specific to the selected method only.

## Scale settings

To optimize costs efficiency and functionality in your aws cluster, several key strategies can be addressed:

**Auto-Scaling:** Implement auto-scaling within the aws cluster to dynamically adjust resource allocation in response to demand, thereby lowering costs during periods of reduced usage.

**Resource Optimization:** Conduct regular assessments and adjustments of the cluster's resource sizes, like VMs, ensuring they match actual usage and preventing excess expenditure on resources that are not fully utilized.

**Performance and Cost Monitoring:** Employ aws Cost Management tools for ongoing monitoring of both cluster performance and expenditure. This facilitates timely and informed adjustments to scale settings.

Additionally, there are various other measures that can be taken.

Our team is committed to expertly managing and optimizing your aws cluster, providing dedicated and professional service.

## VMs (Spot & Public)

Spot and Public instances in cloud computing have distinct differences:

### Pricing

- **Spot Instances:** The pricing for Spot instances is significantly lower, often up to 90% cheaper than Public instances. This is because they utilize the cloud provider's excess capacity. However, the prices are variable and based on real-time supply and demand, which means they can fluctuate frequently.
- **Public Instances (On-Demand):** These instances have fixed, predictable pricing. They are more expensive than Spot instances, but you pay a premium for the reliability and the assurance that the instance won't be terminated unexpectedly due to demand fluctuations.

### Performance

- **Spot instances:** are generally equivalent to Public instances, as they use the same underlying hardware. The key difference lies in their availability and reliability. Spot instances can be terminated by the provider with very little notice if there's an increase in demand for the capacity, which makes them less reliable for critical, uninterrupted workloads.
- **Public Instances (On-Demand):** These offer consistent performance and availability. They are not subject to termination due to demand fluctuations, making them suitable for workloads that require stability and continuous operation.To optimize the use of these instances, it's important to assess your workload requirements. Use Spot instances for non-essential, flexible, or batch processing tasks where interruption is acceptable, and reserve Public instances for critical, performance-sensitive workloads that require uninterrupted operation. This approach can significantly reduce costs while maintaining the necessary performance and reliability. clients who opt for a dedicated cluster will grant access to "AWS Cost Analysis - View".

This tool not only allows for monitoring AWS costs but also provides the ability to:

- Gain valuable insights.
- Make informed business decisions.
- Implement effective optimization strategies.

# Cost Analysis

## Cost Analysis - Monthly view

Upon opening "Cost Analysis" section, the main screen displays the current month's information, providing insights into:

- **Services:** This includes items like Virtual Machines (VMs), Storage solutions, Bandwidth, Load Balancers, and more.
- **Location:** Shows the geographical placement of your machines and resources (e.g., EU West).
- **Resources:** Details the specific machines and resources you have.

Additionally, it offers financial tracking features such as:

- **Actual Cost:** This shows the costs incurred so far for the month, reflecting the prices of services you've actually used.
- **Forecast:** An estimated projection of your expenses by month-end.
- **Budget:** This is currently not active, but it allows clients to set a budget or receive alerts for their costs.

![](https://files.readme.io/ce1cf83-image.png)

*Monthly cost Analysis*

## Cost Analysis - over Periods

AWS  provides detailed cost analysis over various periods, like quarterly, semi-annually, and annually, enabling you to examine your spending patterns across different resources and timeframes.

This feature allows you to track business trends, growth and resource utilization, such as increases in storage, VM usage, and overall traffic, through comprehensive monthly / daily breakdowns.

![](https://files.readme.io/9d9209d-image.png)

##