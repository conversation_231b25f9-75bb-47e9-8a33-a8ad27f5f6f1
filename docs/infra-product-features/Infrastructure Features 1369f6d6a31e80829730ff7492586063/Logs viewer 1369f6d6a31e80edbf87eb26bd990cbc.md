# Logs viewer

Priority: P0
BE Estimated PD (boosting&optimal): 3
Cloud cost: Super costly
Difficulty level: Medium
Feature Description: The business needs a way to view the searct to book logs of their bookings (confirmed, cancelled, failed, etc) … The log types are supplier + internal (markups, rules etc), external (customer) api out 
Improvement Space: Medium and medium
MVP: Yes
Module: Business

![image.png](Logs%20viewer%201369f6d6a31e80edbf87eb26bd990cbc/image.png)

### Goals

- **Business Goals**
    - Provide a transparent logging system that aids in debugging, transaction verification, and failure analysis.
    - Reduce customer service load by enabling users to self-diagnose booking issues.
- **User Goals**
    - Enable real-time session access and analysis for admins and users based on their permissions.
    - Provide a fail-safe system for viewing transaction steps and identifying root causes for booking failures.
- **Non-Goals**
    - This tool is not intended to manage or alter booking data directly.

---

### Overview of Key Concepts and Roles

### 1. **Core Entities**

- **Session**: A complete series of API interactions between the system and a user or supplier, capturing every request/response exchange.
- **Transaction**: A specific API interaction within a session (e.g., search, book, cancel), each tracked with unique identifiers and time-stamped.

### 1. **Session Viewer Access & Permissions**

- **Access Points**:
    - **Direct URL**: Users can access the Session Viewer via a designated URL
    - **User Role-based Access**
    - 
    
    ### 2. **Session Viewing & Filtering**
    
    - **Session Access Options**:
        - **By Session ID**: Locate a specific session by its unique identifier.
        - **By Segment ID**: Track activities associated with a specific booking segment across multiple transactions.
        - **By User ID**: View all sessions associated with a specific user within a defined timeframe.
        - **By Date and Environment**: Filter sessions by date and specify if the data pertains to a production or test environment.
    - **Session Data Fields**:
        - **Timestamp**: Records the exact GMT time for each request/response.
        - **Transaction Duration**: Time elapsed for each transaction within a session (e.g., search, book).
        - **Size**: The data size for each request/response, displayed in bytes or KB.
        - **Supplier Information**: Shortened name of the supplier (if relevant).
        - **Dump File Name**: Unique file name identifier for each log dump.
    - **User Flow**:
        1. User logs into the Session Viewer tool.
        2. Selects viewing options (Session ID, Segment ID, or User).
        3. Filtered logs are displayed, showing full interaction sequences and time-stamped activities.
    
    ### 3. **Failed Booking Transactions Inspection**
    
    - **Purpose**: Offers a dedicated view to diagnose failed bookings, allowing users to view errors and debug issues effectively.
    - **Features**:
        - **Failed Booking Table**:
            - Displays each failed transaction in a searchable table.
            - Allows filtering by **Error Code**, **Affiliate ID**, **Date**, and **Session ID**.
            - Provides a preview of booking attempt errors and transaction steps.
        - **Session Logs**:
            - **Partial Session View**: Shows activities only from the booking request onward, focused specifically on booking-related interactions.
            - **Actions**: Users can **Mark**, **Save**, or **View** each log for troubleshooting.
        - **Error Codes and Types**:
            - **Supplier Errors**: Errors originating from suppliers (e.g., timeouts, unavailable inventory).
            - **Service Errors**: Platform-side errors, which can include validation issues or internal logic conflicts (e.g., pricing mismatch).
    - **User Flow**:
        1. User navigates to "Auditing → Transactions" and selects a specific failed transaction.
        2. Table displays the failed booking details.
        3. User inspects individual logs or saves them for further analysis.
    
    ### 4. **Detailed Session Logs**
    
    - **Purpose**: Capture and view every API call within a session, including both requests and responses, to aid in full transaction tracking and debugging.
    - **Core Fields in Session Log**:
        - **Date-Time Stamp (GMT)**: Indicates when the request or response was sent.
        - **Total Time Passed**: Time elapsed from the first logged action.
        - **Sequence Number**: A unique identifier assigned to each API call.
        - **Data Size**: Shows the size of the request or response in bytes or kilobytes.
        - **Actions for Logs**:
            - **Mark**: Flags the log entry for quick identification.
            - **Save**: Allows users to download the entire XML/JSON dump of the session.
            - **View**: Opens the log for in-depth review within the tool interface.
    - **Session Dump Contents**:
        - Each session dump contains all interaction data, categorized by transaction type (e.g., Search, Book).
        - Includes detailed error information, if applicable, to assist in troubleshooting.
    - **User Flow**:
        1. User accesses the Session Log Viewer.
        2. Filters logs by specific session or transaction.
        3. Reviews sequence and data to troubleshoot specific issues or errors.
    
    ---
    
    ### Technical Requirements and Considerations
    
    - **Data Storage and Format**:
        - Logs will be stored in **XML/JSON** format, and files can be downloaded by users as needed.
        - All session data must comply with data privacy regulations, retaining only essential data for analysis.
    - **Search and Filter Performance**:
        - Implement indexed databases to ensure fast query performance, especially when filtering by Session ID, Segment ID, or other attributes.
        - Use a caching layer to improve response times for high-frequency queries.
    - **Security**:
        - Role-Based Access Control (RBAC) to manage viewing privileges based on user roles.
        - Session data should be accessible only via secure, authenticated connections, using TLS for data protection.
    - **Error Logging and Translation**:
        - Map all error codes (supplier and service errors) to standardized platform error messages to improve user understanding and actionability.
        - Ensure error codes are updated in real time to prevent confusion with supplier updates.