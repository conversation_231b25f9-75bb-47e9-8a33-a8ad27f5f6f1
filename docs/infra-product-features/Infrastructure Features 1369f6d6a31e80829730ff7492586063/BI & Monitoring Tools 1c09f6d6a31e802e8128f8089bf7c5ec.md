# BI & Monitoring Tools

Priority: P0
BE Estimated PD (boosting&optimal): 5
Cloud cost: Super costly
Difficulty level: Super Hard
Feature Description: To start with some basic dashboards for booking trends, commercial performance. To extend API monitoring and performance of suppliers and customers performance
Improvement Space: Large and impactful
MVP: Yes
MVP Implementation: Few critical dashboards will be defined
Module: BI
Phase 2: To extend API monitoring and performance of suppliers and customers performance

# Overview

TL - Insights module will provide a monitoring, tracking and data visualization tool with dashboards, reports and real time data to track and monitor many aspects of the systems like performance and clients usage and business insights, many of the dashboards and reports are available to all clients.

Below are some examples. 
Most technical dashboards can be made on grafana or kibana .. but we need a couple of custom made dashboards that are visible inside the portal such as below example (which are more transaction/booking related data of customers/suppliers):

![image.png](BI%20&%20Monitoring%20Tools%201c09f6d6a31e802e8128f8089bf7c5ec/image.png)

This includes live information that will empower you to make informed decisions, monitor Searches, GetPackages, and track bookings per supplier/user, among much more information!

Other examples:

## (1) Main Dashboard - Today 📊

A **central monitoring hub showing daily real-time data**, including the number of bookings, cancellations, status codes (ER/ERC), failed bookings, SmartBook and Rebooker stats (if applicable), among other metrics.

![](https://files.readme.io/1ca6abd811a922df700f0481ac3eb2ff3fa5ead1bf989657818d2b6322283cd2-image.png)

## (2) Hotel Sales Platform - by Booking Date 📈

**Filtering Options :** Dates, Nodes and Users.

**Displays all performance stats in one place !**

- **Booking Stats:** Displays bookings and cancellations over time, including L2B and other detailed breakdowns.
    
    ![](https://files.readme.io/dc8429e-image.png)
    
- **Cancellation Stats:** Displays cancellations information and other detailed breakdowns.
    
    ![](https://files.readme.io/5a7a3ef-image.png)
    
- **Bookings & Cancellation -** Month over Month Comparison: Last 6 month Booking count and Volumes.
    
    ![](https://files.readme.io/fbc1c4b-image.png)
    
- **Supplier Stats:** Your connected supplier, property mapping and Recommended suppliers breakdown.
    
    ![](https://files.readme.io/b64bce9-image.png)
    
- **SmartBook Stats:** Shows how SmartBook operates, including the volume of bookings it generates and the amount saved. This helps to understand its efficiency and impact.
    
    ![](https://files.readme.io/990dbf0-image.png)
    
- **Rebooker Stats:** Shows how Rebooker operates, including the volume of bookings it generates. This helps to understand its efficiency and impact.
    
    ![](https://files.readme.io/2f033a4-image.png)
    
- **Requires Attention:** ER/ERC and unsaved bookings requiring attention.
    
    ![](https://files.readme.io/1f4aa29-image.png)
    

## (3) SmartBook - Overview 🧠

Overview of how Smartbook operates, including the volume of bookings it generated. The analysis includes month-over-month comparisons, presenting a table of bookings with both the amount of bookings and their increases, covering both ProfitGen and ErrorRevovery Modes. This data helps in understanding the efficiency and impact of Smartbook.

![](https://files.readme.io/bd2cb486135a1e0bb122ac2197a4319b59f46682cef4b0ebc13a106a8e476222-image.png)

## (4) Rebooker - Overview 🔄

Overview of how Rebooker operates, including the volume of bookings it generated. The analysis includes month-over-month comparisons, presenting a table of bookings with both the amount of bookings and their increases, covering both Live and Evaluation Modes. This data helps in understanding the efficiency and impact of Rebooker.

![](https://files.readme.io/6ebd57180bdc89616f06d1dffd29e92c5abce746daa46adcf77b1520bf78a74f-image.png)

### Requires Attention Section

Provides a valuable section for segments with failed cancellations from the previous supplier, offering an easy way to view and manually manage ER and ERC.

![](https://files.readme.io/f7105ab20734c2a7d448ef7d05d241229dabbb3b6ac5d63db09cb2bc31307dcc-image.png)

## (5) Clients Journey & API Overview ⚙️

A detailed analysis of your users and their settings, a breakdown of bookings by supplier, and a full API flow assessment—including success ratios, common errors, and average response times across your API funnels.

![](https://files.readme.io/4403e79215f67a68bdec64b48277fae87e30181b977abb996a7404046368c51a-image.png)

![](https://files.readme.io/9d1c3c5fb69dba6a5ebc1b27ab73870fb30180ac718381c17e2939afb4564efe-image.png)

## (6) Bookings by Supplier

Displays bookings categorized by suppliers for today, alongside comparisons to the same day 1, 2, or 3 weeks prior. It offers an understanding of your booking distribution across suppliers and identifies trends and ratios for different periods.

![](https://files.readme.io/1ce00e0-image.png)

## (7) Bookings by Users

Shows bookings categorized by users and API users for today, with comparisons to the same day 1, 2, or 3 weeks ago. It helps understand the distribution and trends of bookings among different users.

![](https://files.readme.io/a9a0a75-image.png)

## (9) Bookings Trends by Supplier

Your booking trends over time, broken down by daily patterns and supplier distribution, providing full visibility into bookings by supplier over time.

![](https://files.readme.io/ae3fff0426a9884783487d6b16dad54c1861a98d1da319fb46529dc136170ca0-image.png)

## 10 - Booking Errors ⚠️

A powerful dashboard that tracks booking success ratios over time, broken down by supplier failure rates and more. It provides insights into common booking errors and trends, allowing you to analyze different suppliers, trace session links, and dive deeper into the reasons behind booking failures—equipping you with concrete tools for better issue resolution.

![](https://files.readme.io/03a2599db152c03b680dfe217dd83382af431c033168d3f774d94f68039bd239-image.png)

![](https://files.readme.io/3673568253569f5e86e5716f59ea5c3d3a1c5d4c976986e6101cd23925716099-image.png)

![](https://files.readme.io/7e0a6fca0564190f5b4e1b3f0523d43a4f542716e3ce03ea15a1d6020622f6a5-image.png)

## 11 - Client 360 - BreakDown

A shared data dashboard enables clients to contribute their data via ETL, allowing step-by-step comparisons across all API flows and detailed dropdown statistics.

> 📘Note -
> 
> 
> If you choose to share your data, this dashboard will display step-by-step comparisons between your data and ours for easy analysis - Please reach out to your CSM.
> 

# 12 - Dyn.Markups per Client

A comprehensive analysis of your booking distribution across Static and Dynamic markups, highlighting potential profits and a detailed breakdown by suppliers—particularly relevant for clients utilizing the Dynamic Markup feature.

![](https://files.readme.io/3ef92d070be970033057166a5c3222ef84ecf08e86e5f0c04f5c11165d5c0e62-image.png)

## 13 - Transactions - Per User Per Client

Displays transactions categorized by users over time. It provides insights into the distribution of searches, GetPackages, Cxl-Policy, and bookings across users, helping to identify trends over different periods.

![](https://files.readme.io/dc6cbb8-image.png)