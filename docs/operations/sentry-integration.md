# Sentry 集成文档

本文档描述了如何在酒店 API 项目中集成和使用 Sentry 错误监控。

## 功能概述

Sentry 集成已添加到项目中，用于自动捕获和报告以下类型的错误：

1. **系统级 Panic** - 捕获运行时 panic 并发送到 Sentry
2. **500 内部服务器错误** - 捕获非业务错误并发送到 Sentry
3. **手动错误报告** - 支持手动发送错误、消息到 Sentry

## 环境变量配置

在启动应用之前，需要设置以下环境变量：

```bash
# Sentry DSN（必需）
export SENTRY_DSN="https://<EMAIL>/project-id"

# 应用版本（可选）
export APP_VERSION="v1.0.0"

# 环境标识（自动从 ENV 环境变量获取）
export ENV="PRD"  # 或 DEV、UAT、PRE
```

## 自动错误捕获

### Panic 捕获

当应用发生 panic 时，Sentry 会自动捕获以下信息：

- **堆栈跟踪** - 完整的 panic 堆栈信息
- **请求信息** - HTTP 方法、URL、用户代理、远程地址
- **用户信息** - 用户 ID、用户名（如果已登录）
- **租户信息** - 租户组、客户信息
- **服务信息** - 服务名称、方法名称、请求路径

### 500 错误捕获

当 API 返回 500 内部服务器错误时，Sentry 会自动捕获：

- **错误详情** - 错误消息和类型
- **请求上下文** - 请求输入、用户信息、租户信息
- **服务信息** - 调用的服务和方法

## 手动错误报告

### 捕获异常

```go
import sentryHelper "hotel/common/sentry"

// 基本异常捕获
err := someOperation()
if err != nil {
    sentryHelper.CaptureException(ctx, err, nil, nil)
}

// 带标签和额外信息的异常捕获
tags := map[string]string{
    "component": "payment",
    "action":    "process",
}

extra := map[string]interface{}{
    "payment_id": "12345",
    "amount":     100.50,
}

sentryHelper.CaptureException(ctx, err, tags, extra)
```

### 捕获消息

```go
import (
    sentryHelper "hotel/common/sentry"
    "github.com/getsentry/sentry-go"
)

// 发送信息级别消息
sentryHelper.CaptureMessage(ctx, "User performed important action", sentry.LevelInfo, nil, nil)

// 发送警告级别消息
tags := map[string]string{"module": "booking"}
extra := map[string]interface{}{"booking_id": "67890"}

sentryHelper.CaptureMessage(ctx, "Booking limit reached", sentry.LevelWarning, tags, extra)
```

## 配置选项

在 `api/api.go` 的 `initInfra()` 函数中可以调整 Sentry 配置：

```go
sentryConfig := sentryHelper.Config{
    DSN:         os.Getenv("SENTRY_DSN"),
    Environment: envhelper.GetENV(),
    Release:     os.Getenv("APP_VERSION"),
    SampleRate:  1.0, // 生产环境建议设置为 0.1 (10%) 或更低
    Timeout:     2 * time.Second,
}
```

### 采样率建议

- **开发环境**: `1.0` (100%) - 捕获所有错误
- **测试环境**: `1.0` (100%) - 捕获所有错误
- **预发布环境**: `0.5` (50%) - 捕获一半错误
- **生产环境**: `0.1` (10%) - 捕获 10% 错误以控制成本

## 安全注意事项

Sentry 集成已实施以下安全措施：

1. **敏感信息过滤** - 自动移除请求头中的敏感信息：
   - Authorization
   - Cookie
   - X-API-Key

2. **数据脱敏** - 在发送到 Sentry 之前，确保敏感的用户数据被过滤

## 监控和告警

### 在 Sentry 控制台中设置告警

1. 登录 Sentry 控制台
2. 导航到项目设置 > Alerts
3. 创建新的告警规则：
   - **错误数量告警** - 当错误数量超过阈值时触发
   - **新错误告警** - 当出现新类型错误时触发
   - **性能告警** - 当错误率超过阈值时触发

### 推荐的告警配置

- **高频错误**: 5分钟内超过 10 个相同错误
- **新错误**: 出现新的未见过的错误类型
- **错误率**: 1小时内错误率超过 5%

## 故障排除

### Sentry 未接收到错误

1. 检查 `SENTRY_DSN` 环境变量是否正确设置
2. 检查网络连接是否正常
3. 查看应用日志中的 Sentry 初始化信息
4. 确认 Sentry 项目配置正确

### 错误信息不完整

1. 确认应用具有足够的权限访问堆栈信息
2. 检查 Sentry 项目的数据保留设置
3. 验证采样率配置是否合适

## 最佳实践

1. **合理设置采样率** - 生产环境使用较低的采样率以控制成本
2. **添加有意义的标签** - 帮助在 Sentry 中过滤和分组错误
3. **定期检查 Sentry 报告** - 主动监控和修复常见错误
4. **设置合适的告警** - 确保能及时响应关键错误
5. **保护敏感信息** - 避免在错误报告中包含敏感数据

## 相关文件

- `common/sentry/sentry.go` - Sentry 集成核心代码
- `common/httpdispatcher/service_dispatcher.go` - Panic 捕获中间件
- `common/httpdispatcher/service_dispatcher_response.go` - 500 错误捕获
- `api/api.go` - Sentry 初始化配置