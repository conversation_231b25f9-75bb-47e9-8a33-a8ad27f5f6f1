# 监控告警指南

本文档介绍了 Hotel-BE 项目的监控体系、告警策略和运维管理。

## 📊 监控体系

### 监控架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Prometheus    │───▶│    Grafana      │
│   (Metrics)     │    │   (Collector)   │    │   (Dashboard)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   AlertManager  │    │   Log Aggregator│
                    │   (Alerts)      │    │   (ELK Stack)   │
                    └─────────────────┘    └─────────────────┘
```

### 监控层次

| 层次 | 监控内容 | 工具 | 频率 |
|------|----------|------|------|
| **基础设施** | CPU、内存、磁盘、网络 | Node Exporter | 15s |
| **应用服务** | 响应时间、错误率、吞吐量 | Prometheus | 15s |
| **业务指标** | 订单量、搜索量、用户活跃度 | Custom Metrics | 1m |
| **日志分析** | 错误日志、访问日志、性能日志 | ELK Stack | 实时 |

## 🔧 监控配置

### 1. Prometheus 配置

#### prometheus.yml
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'hotel-be-api'
    static_configs:
      - targets: ['api-gateway:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'hotel-be-services'
    static_configs:
      - targets: 
        - 'user-service:8080'
        - 'search-service:8080'
        - 'trade-service:8080'
    metrics_path: '/metrics'

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
```

### 2. 告警规则

#### alert_rules.yml
```yaml
groups:
  - name: hotel-be-alerts
    rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "Service has been down for more than 1 minute"

      # 响应时间告警
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.instance }}"
          description: "95th percentile response time is above 1 second"

      # 错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is above 5%"

      # 数据库连接告警
      - alert: DatabaseConnectionFailed
        expr: mysql_up == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Database connection failed"
          description: "Cannot connect to MySQL database"

      # 内存使用告警
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 90%"

      # 磁盘空间告警
      - alert: DiskSpaceFull
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space is running low on {{ $labels.instance }}"
          description: "Disk usage is above 85%"
```

## 📈 业务监控

### 1. 自定义指标

#### 订单相关指标
```go
// metrics/order.go
var (
    orderCreatedTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hotel_order_created_total",
            Help: "Total number of orders created",
        },
        []string{"status", "supplier"},
    )
    
    orderAmountTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hotel_order_amount_total",
            Help: "Total amount of orders",
        },
        []string{"currency"},
    )
    
    orderProcessingDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "hotel_order_processing_duration_seconds",
            Help: "Order processing duration in seconds",
        },
        []string{"status"},
    )
)

func init() {
    prometheus.MustRegister(orderCreatedTotal)
    prometheus.MustRegister(orderAmountTotal)
    prometheus.MustRegister(orderProcessingDuration)
}
```

#### 搜索相关指标
```go
// metrics/search.go
var (
    searchRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "hotel_search_requests_total",
            Help: "Total number of search requests",
        },
        []string{"city", "supplier"},
    )
    
    searchResultsCount = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "hotel_search_results_count",
            Help: "Number of search results",
        },
        []string{"city"},
    )
    
    searchDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "hotel_search_duration_seconds",
            Help: "Search duration in seconds",
        },
        []string{"supplier"},
    )
)
```

### 2. 业务告警

#### 业务告警规则
```yaml
# business_alerts.yml
groups:
  - name: business-alerts
    rules:
      # 订单量异常告警
      - alert: LowOrderVolume
        expr: rate(hotel_order_created_total[1h]) < 10
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Low order volume"
          description: "Order creation rate is below 10 per hour"

      # 搜索失败率告警
      - alert: HighSearchFailureRate
        expr: rate(hotel_search_requests_total{status="error"}[5m]) / rate(hotel_search_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High search failure rate"
          description: "Search failure rate is above 10%"

      # 供应商响应时间告警
      - alert: SupplierSlowResponse
        expr: hotel_search_duration_seconds{quantile="0.95"} > 5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Slow supplier response"
          description: "Supplier response time is above 5 seconds"
```

## 📊 Grafana 仪表板

### 1. 系统概览仪表板

#### 系统指标面板
```json
{
  "dashboard": {
    "title": "Hotel-BE System Overview",
    "panels": [
      {
        "title": "CPU Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)"
          }
        ]
      },
      {
        "title": "Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100"
          }
        ]
      },
      {
        "title": "Disk Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100"
          }
        ]
      }
    ]
  }
}
```

### 2. 应用性能仪表板

#### API性能面板
```json
{
  "dashboard": {
    "title": "API Performance",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100"
          }
        ]
      }
    ]
  }
}
```

### 3. 业务指标仪表板

#### 业务指标面板
```json
{
  "dashboard": {
    "title": "Business Metrics",
    "panels": [
      {
        "title": "Orders Created",
        "type": "stat",
        "targets": [
          {
            "expr": "increase(hotel_order_created_total[24h])"
          }
        ]
      },
      {
        "title": "Order Amount",
        "type": "stat",
        "targets": [
          {
            "expr": "increase(hotel_order_amount_total[24h])"
          }
        ]
      },
      {
        "title": "Search Requests",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(hotel_search_requests_total[5m])"
          }
        ]
      }
    ]
  }
}
```

## 🚨 告警管理

### 1. AlertManager 配置

#### alertmanager.yml
```yaml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      severity: critical
    receiver: 'pager-duty'
    continue: true
  - match:
      severity: warning
    receiver: 'slack'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://127.0.0.1:5001/'

- name: 'slack'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/xxx'
    channel: '#alerts'
    title: '{{ template "slack.hotel-be.title" . }}'
    text: '{{ template "slack.hotel-be.text" . }}'

- name: 'pager-duty'
  pagerduty_configs:
  - service_key: 'xxx'
    description: '{{ template "pagerduty.hotel-be.description" . }}'
```

### 2. 告警通知模板

#### Slack 通知模板
```yaml
templates:
- '/etc/alertmanager/template/*.tmpl'

# slack.hotel-be.title
{{ define "slack.hotel-be.title" }}
[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}] {{ .CommonLabels.alertname }}
{{ end }}

# slack.hotel-be.text
{{ define "slack.hotel-be.text" }}
{{ range .Alerts }}
*Alert:* {{ .Annotations.summary }}
*Description:* {{ .Annotations.description }}
*Severity:* {{ .Labels.severity }}
*Instance:* {{ .Labels.instance }}
*Started:* {{ .StartsAt | since }}
{{ end }}
{{ end }}
```

## 📝 日志监控

### 1. ELK Stack 配置

#### Logstash 配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "hotel-be" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    if [level] == "ERROR" {
      mutate {
        add_tag => [ "error" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "hotel-be-%{+YYYY.MM.dd}"
  }
}
```

### 2. 日志告警

#### 错误日志告警
```yaml
# log_alerts.yml
groups:
  - name: log-alerts
    rules:
      - alert: HighErrorLogRate
        expr: rate(log_entries_total{level="error"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error log rate"
          description: "Error log rate is above 10 per minute"

      - alert: CriticalError
        expr: count_over_time(log_entries_total{level="error", message=~".*panic.*"}[5m]) > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical error detected"
          description: "Panic error detected in logs"
```

## 🔍 故障排查

### 1. 监控指标排查

#### 性能问题排查
```bash
# 查看响应时间分布
curl -X GET "http://prometheus:9090/api/v1/query" \
  --data-urlencode 'query=histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))'

# 查看错误率
curl -X GET "http://prometheus:9090/api/v1/query" \
  --data-urlencode 'query=rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])'

# 查看资源使用情况
curl -X GET "http://prometheus:9090/api/v1/query" \
  --data-urlencode 'query=(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes'
```

### 2. 日志分析

#### 错误日志分析
```bash
# 查看最近的错误日志
curl -X GET "http://elasticsearch:9200/hotel-be-*/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "bool": {
        "must": [
          {"match": {"level": "error"}},
          {"range": {"@timestamp": {"gte": "now-1h"}}}
        ]
      }
    },
    "sort": [{"@timestamp": {"order": "desc"}}],
    "size": 100
  }'
```

## 📊 监控最佳实践

### 1. 指标设计原则

#### 黄金信号
- **延迟 (Latency)**: 请求响应时间
- **流量 (Traffic)**: 请求速率
- **错误 (Errors)**: 错误率
- **饱和度 (Saturation)**: 资源使用率

#### 指标命名规范
```go
// 使用有意义的指标名称
http_requests_total          // 总请求数
http_request_duration_seconds // 请求持续时间
hotel_order_created_total    // 订单创建总数
hotel_search_results_count   // 搜索结果数量
```

### 2. 告警设计原则

#### 告警规则
- **避免告警风暴**: 合理设置告警间隔和分组
- **分级告警**: 根据严重程度设置不同级别
- **可操作告警**: 告警信息要包含处理建议
- **避免误报**: 设置合适的阈值和持续时间

#### 告警阈值设置
```yaml
# 渐进式告警阈值
- alert: WarningLevel
  expr: metric > 80
  for: 5m
  labels:
    severity: warning

- alert: CriticalLevel
  expr: metric > 95
  for: 2m
  labels:
    severity: critical
```

## 🔄 监控维护

### 1. 定期检查

#### 监控健康检查
```bash
# 检查 Prometheus 状态
curl -X GET "http://prometheus:9090/api/v1/query" \
  --data-urlencode 'query=up'

# 检查告警规则
curl -X GET "http://prometheus:9090/api/v1/rules"

# 检查目标状态
curl -X GET "http://prometheus:9090/api/v1/targets"
```

### 2. 性能优化

#### 监控系统优化
- **数据保留策略**: 合理设置数据保留时间
- **查询优化**: 使用高效的 PromQL 查询
- **存储优化**: 使用 SSD 存储监控数据
- **网络优化**: 优化监控网络带宽使用

---

**最后更新**: 2025-07-23  
**维护者**: Hotel-BE 开发团队 