#!/bin/bash

# 订单取消功能部署脚本
# 使用方法: ./deploy-cancel-feature.sh [environment]
# 环境: dev, staging, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-dev}
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|production)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be one of: dev, staging, production"
    exit 1
fi

log_info "Starting deployment for environment: $ENVIRONMENT"

# 配置文件路径
CONFIG_DIR="./config"
CONFIG_FILE="$CONFIG_DIR/$ENVIRONMENT.yaml"

# 检查配置文件
if [[ ! -f "$CONFIG_FILE" ]]; then
    log_error "Configuration file not found: $CONFIG_FILE"
    exit 1
fi

log_success "Configuration file found: $CONFIG_FILE"

# 1. 预检查
log_info "Running pre-deployment checks..."

# 检查 Go 版本
if ! command -v go &> /dev/null; then
    log_error "Go is not installed"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
log_info "Go version: $GO_VERSION"

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed"
    exit 1
fi

NODE_VERSION=$(node --version)
log_info "Node.js version: $NODE_VERSION"

# 检查 Redis 连接
log_info "Checking Redis connection..."
REDIS_HOST=$(grep -A 5 "redis:" "$CONFIG_FILE" | grep "host:" | awk '{print $2}' | tr -d '"')
REDIS_PORT=$(grep -A 5 "redis:" "$CONFIG_FILE" | grep "port:" | awk '{print $2}')

if ! redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1; then
    log_error "Cannot connect to Redis at $REDIS_HOST:$REDIS_PORT"
    exit 1
fi

log_success "Redis connection successful"

# 检查 MySQL 连接
log_info "Checking MySQL connection..."
MYSQL_HOST=$(grep -A 10 "mysql:" "$CONFIG_FILE" | grep "host:" | awk '{print $2}' | tr -d '"')
MYSQL_PORT=$(grep -A 10 "mysql:" "$CONFIG_FILE" | grep "port:" | awk '{print $2}')
MYSQL_USER=$(grep -A 10 "mysql:" "$CONFIG_FILE" | grep "user:" | awk '{print $2}' | tr -d '"')
MYSQL_PASSWORD=$(grep -A 10 "mysql:" "$CONFIG_FILE" | grep "password:" | awk '{print $2}' | tr -d '"')
MYSQL_DATABASE=$(grep -A 10 "mysql:" "$CONFIG_FILE" | grep "database:" | awk '{print $2}' | tr -d '"')

if ! mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $MYSQL_DATABASE;" > /dev/null 2>&1; then
    log_error "Cannot connect to MySQL at $MYSQL_HOST:$MYSQL_PORT"
    exit 1
fi

log_success "MySQL connection successful"

# 2. 运行测试
log_info "Running tests..."

# 后端测试
log_info "Running backend tests..."
cd trade/service
if ! go test -v ./... -timeout 30s; then
    log_error "Backend tests failed"
    exit 1
fi
cd ../..

log_success "Backend tests passed"

# 前端测试
log_info "Running frontend tests..."
cd admin-fe
if ! npm test; then
    log_error "Frontend tests failed"
    exit 1
fi
cd ..

log_success "Frontend tests passed"

# 3. 数据库迁移
log_info "Running database migrations..."

# 创建审计日志表
mysql -h "$MYSQL_HOST" -P "$MYSQL_PORT" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" << 'EOF'
CREATE TABLE IF NOT EXISTS order_cancel_audit (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL,
    action VARCHAR(50) NOT NULL,
    user_id BIGINT,
    original_status INT NOT NULL,
    final_status INT NOT NULL,
    supplier_cancel_success BOOLEAN DEFAULT FALSE,
    supplier_errors JSON,
    reason TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS cancel_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    total_requests BIGINT DEFAULT 0,
    successful_cancellations BIGINT DEFAULT 0,
    failed_cancellations BIGINT DEFAULT 0,
    supplier_failures BIGINT DEFAULT 0,
    database_failures BIGINT DEFAULT 0,
    message_queue_failures BIGINT DEFAULT 0,
    avg_processing_time_ms BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status);
CREATE INDEX IF NOT EXISTS idx_supplier_orders_order_id ON supplier_orders (order_id);
CREATE INDEX IF NOT EXISTS idx_orders_update_time ON orders (update_time);
EOF

log_success "Database migrations completed"

# 4. 设置 Redis Stream
log_info "Setting up Redis Streams..."

redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" << 'EOF'
XGROUP CREATE order_cancel cancel_consumer $ MKSTREAM
XGROUP CREATE order_cancel_dead_letter dead_letter_consumer $ MKSTREAM
EOF

log_success "Redis Streams configured"

# 5. 构建应用
log_info "Building applications..."

# 构建后端
log_info "Building backend..."
if ! go build -o bin/hotel-be ./cmd/main.go; then
    log_error "Backend build failed"
    exit 1
fi

log_success "Backend build completed"

# 构建前端
log_info "Building frontend..."
cd admin-fe
if ! npm run build; then
    log_error "Frontend build failed"
    exit 1
fi
cd ..

log_success "Frontend build completed"

# 6. 部署应用
log_info "Deploying applications..."

# 停止旧版本
if pgrep -f "hotel-be" > /dev/null; then
    log_info "Stopping old backend instance..."
    pkill -f "hotel-be"
    sleep 5
fi

# 启动新版本
log_info "Starting new backend instance..."
nohup ./bin/hotel-be --config="$CONFIG_FILE" > logs/hotel-be.log 2>&1 &

# 等待应用启动
sleep 10

# 检查应用状态
if ! pgrep -f "hotel-be" > /dev/null; then
    log_error "Backend failed to start"
    exit 1
fi

log_success "Backend started successfully"

# 部署前端（假设使用 nginx）
if [[ "$ENVIRONMENT" == "production" ]]; then
    log_info "Deploying frontend to production..."
    sudo cp -r admin-fe/dist/* /var/www/html/
    sudo systemctl reload nginx
    log_success "Frontend deployed to production"
fi

# 7. 健康检查
log_info "Running health checks..."

# 检查后端健康状态
BACKEND_PORT=$(grep -A 5 "server:" "$CONFIG_FILE" | grep "port:" | awk '{print $2}')
if ! curl -f "http://localhost:$BACKEND_PORT/health" > /dev/null 2>&1; then
    log_error "Backend health check failed"
    exit 1
fi

log_success "Backend health check passed"

# 8. 功能验证
log_info "Running functional tests..."

# 运行端到端测试
cd admin-fe
if ! npm run test:e2e; then
    log_warning "E2E tests failed, but deployment continues"
else
    log_success "E2E tests passed"
fi
cd ..

# 9. 监控设置验证
log_info "Verifying monitoring setup..."

# 检查 Prometheus 指标端点
METRICS_PORT=$(grep -A 5 "metrics:" "$CONFIG_FILE" | grep "port:" | awk '{print $2}')
if curl -f "http://localhost:$METRICS_PORT/metrics" > /dev/null 2>&1; then
    log_success "Metrics endpoint is accessible"
else
    log_warning "Metrics endpoint is not accessible"
fi

# 10. 部署完成
log_success "Deployment completed successfully!"

# 输出部署信息
echo ""
echo "=== Deployment Summary ==="
echo "Environment: $ENVIRONMENT"
echo "Backend PID: $(pgrep -f 'hotel-be')"
echo "Backend Port: $BACKEND_PORT"
echo "Metrics Port: $METRICS_PORT"
echo "Config File: $CONFIG_FILE"
echo "Log File: logs/hotel-be.log"
echo ""

# 输出后续步骤
echo "=== Next Steps ==="
echo "1. Monitor application logs: tail -f logs/hotel-be.log"
echo "2. Check metrics: curl http://localhost:$METRICS_PORT/metrics"
echo "3. Verify cancel functionality in admin panel"
echo "4. Monitor alerts and dashboards"
echo ""

log_info "Deployment script completed"
