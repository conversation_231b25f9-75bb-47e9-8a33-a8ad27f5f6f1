#!/bin/bash

# 订单取消功能验证脚本
# 用于验证部署后的功能是否正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
BACKEND_URL="http://localhost:8080"
ADMIN_URL="http://localhost:3000"
METRICS_URL="http://localhost:9090"

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check() {
    local description="$1"
    local command="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    log_info "Checking: $description"
    
    if eval "$command" > /dev/null 2>&1; then
        log_success "✓ $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "✗ $description"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 开始验证
log_info "Starting cancel feature verification..."
echo ""

# 1. 基础服务检查
log_info "=== Basic Service Checks ==="

check "Backend service is running" "curl -f $BACKEND_URL/health"
check "Admin frontend is accessible" "curl -f $ADMIN_URL"
check "Metrics endpoint is accessible" "curl -f $METRICS_URL/metrics"

echo ""

# 2. 数据库检查
log_info "=== Database Checks ==="

check "Order cancel audit table exists" "mysql -e 'DESCRIBE order_cancel_audit' hotel_db"
check "Cancel metrics table exists" "mysql -e 'DESCRIBE cancel_metrics' hotel_db"
check "Orders table has status index" "mysql -e 'SHOW INDEX FROM orders WHERE Key_name = \"idx_orders_status\"' hotel_db"

echo ""

# 3. Redis Stream 检查
log_info "=== Redis Stream Checks ==="

check "Order cancel stream exists" "redis-cli XINFO STREAM order_cancel"
check "Cancel consumer group exists" "redis-cli XINFO GROUPS order_cancel | grep cancel_consumer"
check "Dead letter stream exists" "redis-cli XINFO STREAM order_cancel_dead_letter"

echo ""

# 4. API 端点检查
log_info "=== API Endpoint Checks ==="

# 创建测试订单
TEST_ORDER_ID="TEST_$(date +%s)"
create_test_order() {
    curl -s -X POST "$BACKEND_URL/api/orders" \
        -H "Content-Type: application/json" \
        -d "{
            \"orderId\": \"$TEST_ORDER_ID\",
            \"hotelName\": \"Test Hotel\",
            \"checkIn\": \"2025-02-01\",
            \"checkOut\": \"2025-02-03\",
            \"amount\": 50000,
            \"status\": 4
        }" > /dev/null
}

check "Create test order" "create_test_order"

# 测试取消 API
test_cancel_api() {
    curl -s -X POST "$BACKEND_URL/api/trade/cancel" \
        -H "Content-Type: application/json" \
        -d "{\"orderId\": \"$TEST_ORDER_ID\"}" \
        | grep -q "success"
}

check "Cancel order API works" "test_cancel_api"

echo ""

# 5. 消息队列检查
log_info "=== Message Queue Checks ==="

# 检查消息是否被处理
sleep 5  # 等待消息处理

check_message_processed() {
    # 检查是否有消息被消费
    local pending=$(redis-cli XPENDING order_cancel cancel_consumer | head -1 | awk '{print $1}')
    [[ "$pending" == "0" ]]
}

check "Cancel message was processed" "check_message_processed"

echo ""

# 6. 监控指标检查
log_info "=== Monitoring Metrics Checks ==="

check_metrics() {
    curl -s "$METRICS_URL/metrics" | grep -q "cancel_requests_total"
}

check "Cancel metrics are exposed" "check_metrics"

check_audit_log() {
    mysql -e "SELECT COUNT(*) FROM order_cancel_audit WHERE order_id = '$TEST_ORDER_ID'" hotel_db | tail -1 | grep -q -v "0"
}

check "Audit log was created" "check_audit_log"

echo ""

# 7. 前端功能检查
log_info "=== Frontend Functionality Checks ==="

check_cancel_dialog() {
    curl -s "$ADMIN_URL" | grep -q "CancelOrderDialog"
}

check "Cancel dialog component is loaded" "check_cancel_dialog"

echo ""

# 8. 供应商接口检查
log_info "=== Supplier Interface Checks ==="

# 检查供应商配置
check_supplier_config() {
    curl -s "$BACKEND_URL/api/suppliers/config" | grep -q "cancel_endpoint"
}

check "Supplier cancel endpoints are configured" "check_supplier_config"

echo ""

# 9. 错误处理检查
log_info "=== Error Handling Checks ==="

# 测试无效订单ID
test_invalid_order() {
    local response=$(curl -s -X POST "$BACKEND_URL/api/trade/cancel" \
        -H "Content-Type: application/json" \
        -d '{"orderId": "INVALID_ORDER"}')
    echo "$response" | grep -q "error\|not found"
}

check "Invalid order ID is handled properly" "test_invalid_order"

# 测试重复取消
test_duplicate_cancel() {
    local response=$(curl -s -X POST "$BACKEND_URL/api/trade/cancel" \
        -H "Content-Type: application/json" \
        -d "{\"orderId\": \"$TEST_ORDER_ID\"}")
    echo "$response" | grep -q "already cancelled\|invalid state"
}

check "Duplicate cancel is handled properly" "test_duplicate_cancel"

echo ""

# 10. 性能检查
log_info "=== Performance Checks ==="

check_response_time() {
    local start_time=$(date +%s%N)
    curl -s "$BACKEND_URL/health" > /dev/null
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 ))  # Convert to milliseconds
    [[ $duration -lt 1000 ]]  # Should be less than 1 second
}

check "API response time is acceptable" "check_response_time"

echo ""

# 清理测试数据
log_info "=== Cleanup ==="

cleanup_test_data() {
    mysql -e "DELETE FROM orders WHERE order_id = '$TEST_ORDER_ID'" hotel_db 2>/dev/null || true
    mysql -e "DELETE FROM order_cancel_audit WHERE order_id = '$TEST_ORDER_ID'" hotel_db 2>/dev/null || true
}

check "Test data cleanup" "cleanup_test_data"

echo ""

# 生成验证报告
log_info "=== Verification Report ==="
echo ""
echo "Total Checks: $TOTAL_CHECKS"
echo "Passed: $PASSED_CHECKS"
echo "Failed: $FAILED_CHECKS"
echo ""

if [[ $FAILED_CHECKS -eq 0 ]]; then
    log_success "🎉 All checks passed! Cancel feature is ready for production."
    echo ""
    echo "=== Recommended Next Steps ==="
    echo "1. Monitor the application for the first few hours"
    echo "2. Check Grafana dashboards for metrics"
    echo "3. Verify alerts are working properly"
    echo "4. Conduct user acceptance testing"
    echo "5. Update documentation and runbooks"
    exit 0
else
    log_error "❌ $FAILED_CHECKS checks failed. Please review and fix issues before going to production."
    echo ""
    echo "=== Troubleshooting Tips ==="
    echo "1. Check application logs: tail -f logs/hotel-be.log"
    echo "2. Verify configuration files"
    echo "3. Check database connectivity"
    echo "4. Verify Redis connectivity"
    echo "5. Review supplier API configurations"
    exit 1
fi
