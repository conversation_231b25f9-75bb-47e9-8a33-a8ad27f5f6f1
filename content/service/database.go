package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"hotel/common/errgroup"
	"hotel/common/log"
	"hotel/common/types"
	"hotel/content/domain"
	"hotel/content/mysql"
	supplierDomain "hotel/supplier/domain"
)

// DatabaseOperator 高性能数据库操作器
type DatabaseOperator struct {
	dao       *mysql.HotelDao
	batchSize int
	workers   int
}

// NewDatabaseOperator 创建新的数据库操作器
func NewDatabaseOperator(dao *mysql.HotelDao) *DatabaseOperator {
	return &DatabaseOperator{
		dao:       dao,
		batchSize: 20, // 从 50 减小到 20，避免断路器触发
		workers:   2,  // 从 4 减小到 2，减少并发压力
	}
}

// BatchUpsertHotels 批量更新或插入酒店数据
func (d *DatabaseOperator) BatchUpsertHotels(ctx context.Context, hotels []*domain.Hotel) error {
	if len(hotels) == 0 {
		return nil
	}

	st := time.Now()
	defer func() {
		log.Infoc(ctx, "BatchUpsertHotels completed, processed: %d, cost: %s", len(hotels), time.Since(st))
	}()

	// 分类处理：插入和更新
	insertHotels, updateHotels, err := d.classifyHotels(ctx, hotels)
	if err != nil {
		return fmt.Errorf("classify hotels failed: %w", err)
	}

	// 并发执行插入和更新操作
	var wg = errgroup.WithContext(ctx)
	// 批量插入
	if len(insertHotels) > 0 {
		wg.Go(func(ctx context.Context) error {
			if err := d.batchInsert(ctx, insertHotels); err != nil {
				return fmt.Errorf("batch insert failed: %w", err)
			}
			return nil
		})
	}

	// 批量更新 携程作为主数据，暂时只管 insert，不做 update
	updateHotels = updateHotels.Filter(func(hotel *domain.Hotel) bool {
		return len(hotel.HotelSupplierRef) == 1 && hotel.HotelSupplierRef[0].Supplier != supplierDomain.Supplier_Trip
	})
	if len(updateHotels) > 0 {
		wg.Go(func(ctx context.Context) error {
			if err := d.batchUpdate(ctx, updateHotels); err != nil {
				return fmt.Errorf("batch update failed: %w", err)
			}
			return nil
		})
	}

	return wg.Wait()

}

// classifyHotels 分类酒店数据
func (d *DatabaseOperator) classifyHotels(ctx context.Context, hotels domain.HotelList) (insertHotels, updateHotels domain.HotelList, err error) {
	// 构建查询条件
	ctripHotels := hotels.Filter(func(hotel *domain.Hotel) bool {
		return hotel.HotelSupplierRef[0].Supplier == supplierDomain.Supplier_Trip
	})

	var hotelIDs types.IDs
	for _, hotel := range ctripHotels {
		hotelIDs = append(hotelIDs, hotel.ID)
	}

	// 批量查询已存在的酒店
	existingHotels, err := d.dao.FindByIDs(ctx, hotelIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("find existing hotels failed: %w", err)
	}

	// 对于非携程酒店，用匹配的携程酒店的名称和地址来更新
	nonCtripHotels := hotels.Filter(func(hotel *domain.Hotel) bool {
		return hotel.HotelSupplierRef[0].Supplier != supplierDomain.Supplier_Trip
	})

	wg := errgroup.WithContext(ctx)
	wg.GOMAXPROCS(d.workers)
	var mu sync.Mutex

	for _, hotel := range nonCtripHotels {
		wg.Go(func(ctx context.Context) error {
			existed, err := d.dao.Find(ctx, hotel)
			if err != nil {
				return fmt.Errorf("find existing hotels failed: %w", err)
			}
			if len(existed) > 0 {
				log.Infoc(ctx, "%d find existing hotels: %v", hotel.ID, existed.IDs())
				mu.Lock()
				existingHotels = append(existingHotels, existed[0]) // todo: 先取第一个
				mu.Unlock()
			}
			return nil
		})
	}

	// 构建已存在酒店的映射
	existingMap := make(map[types.ID]*domain.Hotel)
	for _, hotel := range existingHotels {
		existingMap[hotel.ID] = hotel
	}

	// 分类
	for _, hotel := range hotels {
		if existingHotel, exists := existingMap[hotel.ID]; exists {
			// 更新已存在的酒店
			hotel = existingHotel.MergeUpdate(hotel)
			updateHotels = append(updateHotels, hotel)
		} else {
			// 插入新酒店
			insertHotels = append(insertHotels, hotel)
		}
	}

	return insertHotels, updateHotels, nil
}

// batchInsert 批量插入酒店
func (d *DatabaseOperator) batchInsert(ctx context.Context, hotels domain.HotelList) error {
	if len(hotels) == 0 {
		return nil
	}

	// 分批处理
	batches := hotels.Split(d.batchSize)

	g := errgroup.WithContext(ctx)
	g.GOMAXPROCS(d.workers)

	for i, batch := range batches {
		batchIndex := i
		batchHotels := batch

		g.Go(func(ctx context.Context) error {
			return d.insertBatch(ctx, batchHotels, batchIndex)
		})
	}

	return g.Wait()
}

// batchUpdate 批量更新酒店
func (d *DatabaseOperator) batchUpdate(ctx context.Context, hotels domain.HotelList) error {
	if len(hotels) == 0 {
		return nil
	}

	// 分批处理
	batches := hotels.Split(d.batchSize)

	g := errgroup.WithContext(ctx)
	g.GOMAXPROCS(d.workers)

	for i, batch := range batches {
		batchIndex := i
		batchHotels := batch

		g.Go(func(ctx context.Context) error {
			return d.updateBatch(ctx, batchHotels, batchIndex)
		})
	}

	return g.Wait()
}

// insertBatch 插入单个批次
func (d *DatabaseOperator) insertBatch(ctx context.Context, hotels []*domain.Hotel, batchIndex int) error {
	return d.insertBatchWithRetry(ctx, hotels, batchIndex)
}

// insertBatchWithRetry 带重试机制的批次插入
func (d *DatabaseOperator) insertBatchWithRetry(ctx context.Context, hotels []*domain.Hotel, batchIndex int) error {
	maxRetries := 3
	backoff := time.Second

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := d.dao.BatchInsert(ctx, hotels)
		if err == nil {
			return nil
		}

		// 检查是否是断路器错误
		if strings.Contains(err.Error(), "circuit breaker is open") {
			log.Warnc(ctx, "Circuit breaker triggered for batch %d, attempt %d/%d", batchIndex, attempt+1, maxRetries)

			if attempt < maxRetries-1 {
				time.Sleep(backoff)
				backoff *= 2 // 指数退避
				continue
			}
		}

		return fmt.Errorf("batch insert failed after %d attempts: %w", maxRetries, err)
	}

	return nil
}

// updateBatch 更新单个批次
func (d *DatabaseOperator) updateBatch(ctx context.Context, hotels domain.HotelList, batchIndex int) error {
	st := time.Now()
	defer func() {
		log.Infoc(ctx, "updateBatch %d completed, count: %d, cost: %s", batchIndex, len(hotels), time.Since(st))
	}()

	for _, hotel := range hotels {
		if err := d.dao.Update(ctx, hotel); err != nil {
			return fmt.Errorf("update hotel %d failed: %w", hotel.ID, err)
		}
	}

	return nil
}
