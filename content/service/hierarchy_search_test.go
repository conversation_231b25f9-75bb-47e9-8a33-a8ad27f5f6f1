package service

import (
	"context"
	"testing"

	"hotel/common/types"
	"hotel/geography/domain"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockGeographyService 用于测试的mock服务
type MockGeographyService struct {
	mock.Mock
}

func (m *MockGeographyService) GetExpandedRegionIds(ctx context.Context, regionIds types.IDs) (types.IDs, error) {
	args := m.Called(ctx, regionIds)
	return args.Get(0).(types.IDs), args.Error(1)
}

func TestListHotelByRegionIds_WithHierarchySearch(t *testing.T) {
	// 模拟场景：搜索上海市（region ID: 1000），应该扩展包含松江区（region ID: 1001）等子区域
	tests := []struct {
		name              string
		inputRegionIds    types.IDs
		expandedRegionIds types.IDs
		expandError       error
		expectedRegionIds types.IDs
	}{
		{
			name:              "成功扩展region层级",
			inputRegionIds:    types.IDs{1000},                   // 上海市
			expandedRegionIds: types.IDs{1000, 1001, 1002, 1003}, // 上海市 + 松江区 + 黄浦区 + 徐汇区
			expandError:       nil,
			expectedRegionIds: types.IDs{1000, 1001, 1002, 1003},
		},
		{
			name:              "扩展失败时fallback到原始region",
			inputRegionIds:    types.IDs{1000},
			expandedRegionIds: nil,
			expandError:       assert.AnError,
			expectedRegionIds: types.IDs{1000}, // fallback到原始region
		},
		{
			name:              "多个region的扩展",
			inputRegionIds:    types.IDs{1000, 2000},                         // 上海市 + 北京市
			expandedRegionIds: types.IDs{1000, 1001, 1002, 2000, 2001, 2002}, // 包含各自的子区域
			expandError:       nil,
			expectedRegionIds: types.IDs{1000, 1001, 1002, 2000, 2001, 2002},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建mock geography service
			mockGeoSrv := &MockGeographyService{}
			mockGeoSrv.On("GetExpandedRegionIds", mock.Anything, tt.inputRegionIds).
				Return(tt.expandedRegionIds, tt.expandError)

			// 注意：这里只是测试逻辑，实际测试需要完整的依赖注入
			// 验证扩展逻辑
			ctx := context.Background()
			expandedIds, err := mockGeoSrv.GetExpandedRegionIds(ctx, tt.inputRegionIds)

			if tt.expandError != nil {
				assert.Error(t, err)
				// 模拟fallback逻辑
				expandedIds = tt.inputRegionIds
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expectedRegionIds, expandedIds)
			mockGeoSrv.AssertExpectations(t)
		})
	}
}

func TestGeographyService_GetExpandedRegionIds(t *testing.T) {
	// 这个测试需要真实的geography service和数据
	// 由于需要完整的数据库和索引，这里只提供测试框架
	t.Skip("需要完整的geography service和region数据")

	// 示例测试逻辑：
	// geoService := geography.NewGeographyService()
	// ctx := context.Background()
	//
	// // 假设region ID 1000 是上海市，有子区域 1001(松江区), 1002(黄浦区)
	// regionIds := types.IDs{1000}
	// expandedIds, err := geoService.GetExpandedRegionIds(ctx, regionIds)
	//
	// assert.NoError(t, err)
	// assert.Contains(t, expandedIds, types.ID(1000)) // 包含原始region
	// assert.Greater(t, len(expandedIds), 1) // 应该包含更多region
}

// 辅助函数：创建测试用的region数据
func createTestRegions() []*domain.Region {
	return []*domain.Region{
		{
			ID:   1000,
			Type: domain.RegionType_MultiCityVicinity,
			Name: "Shanghai",
			Descendants: []*domain.Region{
				{ID: 1001, Type: domain.RegionType_City, Name: "Songjiang District"},
				{ID: 1002, Type: domain.RegionType_City, Name: "Huangpu District"},
				{ID: 1003, Type: domain.RegionType_City, Name: "Xuhui District"},
			},
		},
		{
			ID:   1001,
			Type: domain.RegionType_City,
			Name: "Songjiang District",
			Ancestors: []*domain.Region{
				{ID: 1000, Type: domain.RegionType_MultiCityVicinity, Name: "Shanghai"},
			},
		},
		{
			ID:   1002,
			Type: domain.RegionType_City,
			Name: "Huangpu District",
			Ancestors: []*domain.Region{
				{ID: 1000, Type: domain.RegionType_MultiCityVicinity, Name: "Shanghai"},
			},
		},
	}
}
