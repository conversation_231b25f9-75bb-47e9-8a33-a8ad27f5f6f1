package mysql

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/i18n"
)

func initHotelNameModelForSmartTest() *HotelNameModel {
	conn := sqlx.NewSqlConn("mysql", "root:123456@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=UTC")
	return NewHotelNameModel(conn)
}

func TestShouldUseFulltext(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Short word (less than 4 chars)",
			input:    "Hil",
			expected: false,
		},
		{
			name:     "Common word - hotel",
			input:    "hotel",
			expected: false,
		},
		{
			name:     "Common word - inn",
			input:    "inn",
			expected: false,
		},
		{
			name:     "Word with special characters",
			input:    "Hotel-123",
			expected: false,
		},
		{
			name:     "Word with numbers",
			input:    "Hotel123",
			expected: false,
		},
		{
			name:     "Specific hotel name - should use LIKE (based on performance)",
			input:    "Shanghai",
			expected: false,
		},
		{
			name:     "Good candidate for FULLTEXT - brand name",
			input:    "Hilton",
			expected: true,
		},
		{
			name:     "Location - should use LIKE (based on performance)",
			input:    "Beijing",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := shouldUseFulltext(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestSmartSearchPerformance(t *testing.T) {
	model := initHotelNameModelForSmartTest()
	ctx := context.Background()

	testCases := []struct {
		name        string
		search      i18n.I18N
		description string
	}{
		{
			name: "Specific hotel name - should use FULLTEXT",
			search: i18n.I18N{
				En: "Shanghai",
			},
			description: "Specific location name, should use FULLTEXT",
		},
		{
			name: "Common word - should use LIKE",
			search: i18n.I18N{
				En: "Hotel",
			},
			description: "Common word, should use LIKE",
		},
		{
			name: "Short word - should use LIKE",
			search: i18n.I18N{
				En: "Hil",
			},
			description: "Short word, should use LIKE",
		},
		{
			name: "Word with special chars - should use LIKE",
			search: i18n.I18N{
				En: "Hotel-123",
			},
			description: "Word with special characters, should use LIKE",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			start := time.Now()
			results, err := model.SearchName(ctx, tc.search, 10)
			duration := time.Since(start)

			assert.NoError(t, err)
			t.Logf("%s: Search completed in %v, found %d results", tc.description, duration, len(results))

			// 性能断言：搜索应该在合理时间内完成
			if duration > 2*time.Second {
				t.Logf("Warning: Search took %v, which is slower than expected", duration)
			}

			// 验证结果
			if len(results) > 0 {
				for _, result := range results {
					assert.NotEmpty(t, result.Name)
					assert.NotZero(t, result.HotelId)
					assert.NotEmpty(t, result.Language)
				}
			}
		})
	}
}

func TestSearchWithLike(t *testing.T) {
	model := initHotelNameModelForSmartTest()
	ctx := context.Background()

	testCases := []struct {
		name     string
		language string
		search   string
	}{
		{
			name:     "Search for Hotel",
			language: "en",
			search:   "Hotel",
		},
		{
			name:     "Search for Shanghai",
			language: "en",
			search:   "Shanghai",
		},
		{
			name:     "Search for Four Seasons",
			language: "en",
			search:   "Four Seasons",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			start := time.Now()
			results, err := model.searchWithLike(ctx, tc.language, tc.search, 10)
			duration := time.Since(start)

			assert.NoError(t, err)
			t.Logf("LIKE search for '%s' completed in %v, found %d results", tc.search, duration, len(results))

			// 性能断言：LIKE 搜索应该在合理时间内完成
			if duration > 1*time.Second {
				t.Logf("Warning: LIKE search took %v, which is slower than expected", duration)
			}

			// 验证结果
			if len(results) > 0 {
				for _, result := range results {
					assert.NotEmpty(t, result.Name)
					assert.NotZero(t, result.HotelId)
					assert.Equal(t, tc.language, result.Language)
				}
			}
		})
	}
}

func TestContainsSpecialChars(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Only letters and spaces",
			input:    "Hotel Name",
			expected: false,
		},
		{
			name:     "Contains numbers",
			input:    "Hotel123",
			expected: true,
		},
		{
			name:     "Contains special characters",
			input:    "Hotel-Name",
			expected: true,
		},
		{
			name:     "Contains punctuation",
			input:    "Hotel.Name",
			expected: true,
		},
		{
			name:     "Only letters",
			input:    "HotelName",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := containsSpecialChars(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestIsCommonWord(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Common word - hotel",
			input:    "hotel",
			expected: true,
		},
		{
			name:     "Common word - Hotel (case insensitive)",
			input:    "Hotel",
			expected: true,
		},
		{
			name:     "Common word - inn",
			input:    "inn",
			expected: true,
		},
		{
			name:     "Not a common word",
			input:    "Shanghai",
			expected: false,
		},
		{
			name:     "Not a common word - Hilton",
			input:    "Hilton",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isCommonWord(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// BenchmarkSmartSearch 智能搜索性能基准测试
func BenchmarkSmartSearch(b *testing.B) {
	model := initHotelNameModelForSmartTest()
	ctx := context.Background()

	testCases := []struct {
		name   string
		search i18n.I18N
	}{
		{
			name:   "Specific location",
			search: i18n.I18N{En: "Shanghai"},
		},
		{
			name:   "Common word",
			search: i18n.I18N{En: "Hotel"},
		},
		{
			name:   "Short word",
			search: i18n.I18N{En: "Hil"},
		},
	}

	for _, tc := range testCases {
		b.Run(tc.name, func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, err := model.SearchName(ctx, tc.search, 10)
				if err != nil {
					b.Logf("Search failed: %v", err)
				}
			}
		})
	}
}
