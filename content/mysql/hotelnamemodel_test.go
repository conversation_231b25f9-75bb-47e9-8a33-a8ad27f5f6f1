package mysql

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/i18n"
)

func initHotelNameModel() *HotelNameModel {
	conn := sqlx.NewSqlConn("mysql", "root:123456@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=UTC")
	return NewHotelNameModel(conn)
}

func TestCleanString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "normal string",
			input:    "Hampton by Hilton",
			expected: "Hampton by Hilton",
		},
		{
			name:     "string with special characters",
			input:    "Hampton by Hilton\u00a0Lu\u0092an Shanghe Street",
			expected: "Hampton by Hilton Lu'an Shanghe Street",
		},
		{
			name:     "string with control characters",
			input:    "Hotel\x00Name\x01With\x02Control\x03Chars",
			expected: "Hotel Name With Control Chars",
		},
		{
			name:     "string with multiple spaces",
			input:    "Hotel   Name    With    Multiple    Spaces",
			expected: "Hotel Name With Multiple Spaces",
		},
		{
			name:     "empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "string with unicode characters",
			input:    "酒店名称\u00a0with\u0092special chars",
			expected: "酒店名称 with'special chars",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CleanString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHotelNameModel_UpsertName(t *testing.T) {
	model := initHotelNameModel()

	// 测试数据包含特殊字符
	testName := i18n.I18N{
		En: "Hampton by Hilton\xa0Lu\x92an Shanghe Street",
		Zh: "汉普顿希尔顿\xa0路\x92安上海街",
		Ar: "هانتون باي هيلتون\xa0لو\x92ان شنغهاي ستريت",
	}

	t.Run("UpsertName_WithSpecialCharacters", func(t *testing.T) {
		hotelID := time.Now().UnixNano()

		// 测试插入包含特殊字符的酒店名称
		err := model.UpsertName(context.Background(), hotelID, testName)
		assert.NoError(t, err)

		// 验证数据是否正确插入
		// 这里可以添加查询验证逻辑
		t.Logf("Successfully inserted hotel name with special characters for hotel ID: %d", hotelID)
	})
}

func TestHotelNameModel_SearchName(t *testing.T) {
	model := initHotelNameModel()

	t.Run("SearchName_WithCleanedString", func(t *testing.T) {
		// 测试搜索包含特殊字符的酒店名称
		searchName := i18n.I18N{
			En: "Hampton\xa0by\x92Hilton",
		}

		results, err := model.SearchName(context.Background(), searchName, 10)
		assert.NoError(t, err)

		// 验证搜索结果
		t.Logf("Search results count: %d", len(results))
	})
}
