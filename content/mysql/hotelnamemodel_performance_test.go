package mysql

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/i18n"
)

func initHotelNameModelForTest() *HotelNameModel {
	conn := sqlx.NewSqlConn("mysql", "root:123456@tcp(localhost:3306)/hoteldev?charset=utf8mb4&parseTime=True&loc=UTC")
	return NewHotelNameModel(conn)
}

func TestSearchNamePerformance(t *testing.T) {
	model := initHotelNameModelForTest()
	ctx := context.Background()

	// 测试数据
	testCases := []struct {
		name     string
		search   i18n.I18N
		language string
	}{
		{
			name: "English hotel name search",
			search: i18n.I18N{
				En: "Penthouse Level 8 Nilie Hospitality MGMT",
			},
			language: "en-US",
		},
		{
			name: "Chinese hotel name search",
			search: i18n.I18N{
				Zh: "酒店名称",
			},
			language: "zh-CN",
		},
		{
			name: "Short keyword search",
			search: i18n.I18N{
				En: "Hilton",
			},
			language: "en-US",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试 FULLTEXT 搜索性能
			start := time.Now()
			results, err := model.SearchName(ctx, tc.search, 10)
			ftDuration := time.Since(start)

			if err != nil {
				t.Logf("FULLTEXT search failed: %v", err)
				// 如果 FULLTEXT 搜索失败，测试降级策略
				start = time.Now()
				results, err = model.SearchNameWithFallback(ctx, tc.search, 10)
				fallbackDuration := time.Since(start)

				assert.NoError(t, err)
				t.Logf("Fallback search completed in %v, found %d results", fallbackDuration, len(results))
			} else {
				t.Logf("FULLTEXT search completed in %v, found %d results", ftDuration, len(results))
			}

			// 性能断言：FULLTEXT 搜索应该在 100ms 内完成
			if ftDuration > 100*time.Millisecond {
				t.Logf("Warning: FULLTEXT search took %v, which is slower than expected", ftDuration)
			}

			// 验证结果
			if len(results) > 0 {
				for _, result := range results {
					assert.NotEmpty(t, result.Name)
					assert.NotZero(t, result.HotelId)
					assert.NotEmpty(t, result.Language)
				}
			}
		})
	}
}

func TestSearchNameWithIdsPerformance(t *testing.T) {
	model := initHotelNameModelForTest()
	ctx := context.Background()

	// 模拟酒店ID列表
	hotelIds := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}

	testCases := []struct {
		name   string
		search i18n.I18N
	}{
		{
			name: "Search within specific hotel IDs",
			search: i18n.I18N{
				En: "Hilton",
			},
		},
		{
			name: "Search with Chinese text",
			search: i18n.I18N{
				Zh: "酒店",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			start := time.Now()
			results, err := model.SearchNameWithIds(ctx, tc.search, hotelIds)
			duration := time.Since(start)

			assert.NoError(t, err)
			t.Logf("SearchNameWithIds completed in %v, found %d results", duration, len(results))

			// 性能断言：应该在 50ms 内完成
			if duration > 50*time.Millisecond {
				t.Logf("Warning: SearchNameWithIds took %v, which is slower than expected", duration)
			}

			// 验证结果
			if len(results) > 0 {
				for _, result := range results {
					assert.NotEmpty(t, result.Name)
					assert.NotZero(t, result.HotelId)
					assert.Contains(t, hotelIds, int64(result.HotelId))
				}
			}
		})
	}
}

func TestCleanStringPerformance(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Normal string",
			input:    "Hilton Hotel",
			expected: "Hilton Hotel",
		},
		{
			name:     "String with special characters",
			input:    "Hilton\u00a0Hotel\u0092Name",
			expected: "Hilton Hotel'Name",
		},
		{
			name:     "String with control characters",
			input:    "Hotel\x00Name\x01With\x02Control",
			expected: "Hotel Name With Control",
		},
		{
			name:     "String with multiple spaces",
			input:    "Hotel   Name    With    Spaces",
			expected: "Hotel Name With Spaces",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			start := time.Now()
			result := CleanString(tc.input)
			duration := time.Since(start)

			assert.Equal(t, tc.expected, result)

			// 性能断言：字符串清理应该在 1ms 内完成
			if duration > time.Millisecond {
				t.Logf("Warning: CleanString took %v for input length %d", duration, len(tc.input))
			}
		})
	}
}

// BenchmarkSearchName 性能基准测试
func BenchmarkSearchName(b *testing.B) {
	model := initHotelNameModelForTest()
	ctx := context.Background()

	search := i18n.I18N{
		En: "Hilton",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := model.SearchName(ctx, search, 10)
		if err != nil {
			b.Logf("Search failed: %v", err)
		}
	}
}

// BenchmarkCleanString 字符串清理性能基准测试
func BenchmarkCleanString(b *testing.B) {
	testString := "Hilton\u00a0Hotel\u0092Name\x00With\x01Control\x02Chars"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CleanString(testString)
	}
}
