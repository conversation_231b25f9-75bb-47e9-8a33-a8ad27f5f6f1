package model

import "encoding/xml"

// GiataRoomTypesResponse represents GIATA room types response
type GiataRoomTypesResponse struct {
	XMLName   xml.Name        `xml:"response"`
	RoomTypes []GiataRoomType `xml:"roomtype"`
}

type GiataRoomType struct {
	ID          string                   `xml:"id,attr"`
	Code        string                   `xml:"code,attr"`
	Name        GiataMultilingualContent `xml:"name"`
	Description GiataMultilingualContent `xml:"description"`
	Category    string                   `xml:"category,attr"`
}

type GiataMultilingualContent struct {
	XMLName xml.Name `xml:"name"`
	En      string   `xml:"en,attr"`
	De      string   `xml:"de,attr"`
	Fr      string   `xml:"fr,attr"`
	Es      string   `xml:"es,attr"`
	It      string   `xml:"it,attr"`
}