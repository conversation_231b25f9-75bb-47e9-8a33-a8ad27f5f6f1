package converter

import (
	"fmt"
	"hotel/mapping/supplier/olivier/domain"
	"hotel/mapping/supplier/olivier/model"
)

// ToMatchRoomsRequest 将领域模型转换为API请求模型 (根据新的结构)
func ToMatchRoomsRequest(req *domain.RoomMatchingReq) *model.MatchRoomsRequest {
	apiList := make([]model.RoomInfo, len(req.List))
	for i, room := range req.List {
		apiList[i] = model.RoomInfo{
			Hid:       room.HotelID,
			Supplier:  room.Supplier,
			RoomCode1: room.RoomCode1,
			RoomCode2: room.RoomCode2,
			Amount:    room.Amount,
			Name:      room.Name,
		}
	}

	return &model.MatchRoomsRequest{
		Iso2: req.Iso2,
		List: apiList,
	}
}

// ToRoomMatchingResp 将API响应模型转换为领域模型 (根据新的结构)
func ToRoomMatchingResp(resp *model.MatchRoomsResponse) *domain.RoomMatchingResp {
	matchings := make([]domain.RoomMatch, 0)
	for roomName, room := range resp.Matching {
		roomMatch := domain.RoomMatch{RoomName: roomName}

		for supplierName, supplierInfo := range room {
			v := domain.RoomMatchSupplierItem{Supplier: supplierName}
			for supplierHotelID, roomKey := range supplierInfo {
				v.HotelID = supplierHotelID
				for i := 0; i < len(roomKey.Rcode1); i++ {
					roomID := ""
					if str, ok := roomKey.Rcode1[i].(string); ok {
						roomID = str
					} else if num, ok := roomKey.Rcode1[i].(float64); ok {
						roomID = fmt.Sprintf("%.0f", num)
					} else if num, ok := roomKey.Rcode1[i].(int); ok {
						roomID = fmt.Sprintf("%d", num)
					}

					v.RoomKey = domain.RoomKey{
						RoomID:   roomID,
						RoomCode: roomKey.Rcode2[i],
					}
				}
			}
			roomMatch.Suppliers = append(roomMatch.Suppliers, v)
		}

		matchings = append(matchings, roomMatch)
	}
	domainMapping := make(map[string]map[string][]domain.MappedRoomInfo)

	for supplier, hotelMap := range resp.Mapping {
		domainHotelMap := make(map[string][]domain.MappedRoomInfo)
		for hotelID, mappedRooms := range hotelMap {
			domainMappedRooms := make([]domain.MappedRoomInfo, len(mappedRooms))
			for i, room := range mappedRooms {
				domainMappedRooms[i] = domain.MappedRoomInfo{
					HotelID:  room.HotelID,
					Supplier: room.Suppl,
					RoomID:   room.Rcode1,
					RoomCode: room.Rcode2,
					Amount:   room.Amount,
					Name:     room.Name,
					Group:    room.Group,
					Range:    room.Range,
					Curated:  room.Curated,
					Dataset:  room.Dataset,
					AMN:      room.AMN,
					LangEN:   room.LangEN,
					LangES:   room.LangES,
					LangPT:   room.LangPT,
					LangFR:   room.LangFR,
					LangIT:   room.LangIT,
					LangDE:   room.LangDE,
					Score:    room.Score,
				}
			}
			domainHotelMap[hotelID] = domainMappedRooms
		}
		domainMapping[supplier] = domainHotelMap
	}

	return &domain.RoomMatchingResp{Matching: matchings}
}
