package olivier

import (
	"context"
	"testing"

	commonProto "hotel/common/protocol"
	olivierDomain "hotel/mapping/supplier/olivier/domain"
	supplierDomain "hotel/supplier/domain"
)

func TestNewOlivierClient(t *testing.T) {
	client := NewOlivierClient()
	if client == nil {
		t.Fatal("NewOlivierClient() returned nil")
	}

	if client.Supplier() != supplierDomain.Supplier_Olivier {
		t.<PERSON>("Expected supplier to be Supplier_<PERSON>, got %v", client.Supplier())
	}
}

func TestOlivierClient_RoomMatching(t *testing.T) {
	client := NewOlivierClient()

	// 创建测试请求
	req := &olivierDomain.RoomMatchingReq{
		Iso2: "US",
		List: []olivierDomain.RoomInfo{
			{
				HotelID:   "12345",
				Supplier:  "dida",
				RoomCode1: "3858",
				RoomCode2: "APARTMENTO...refundable-False",
				Amount:    "100.00",
				Name:      "Standard Room",
			},
		},
	}

	// 创建带有认证信息的上下文
	payload := supplierDomain.BaseRequestContextPayload{
		Supplier:   supplierDomain.Supplier_Olivier,
		Properties: `{"apiKey":"amitGodMode"}`,
		Header: commonProto.Header{
			RequestTimeOption: commonProto.RequestTimeOption{
				ServerRequestTimestamp: 1753505108, // 当前时间戳
			},
			TimeoutOption: commonProto.TimeoutOption{
				TimeoutMilliseconds: 30000, // 30秒超时
			},
		},
	}
	ctx := supplierDomain.InjectBaseRequestContextPayload(context.Background(), payload)

	// 调用房间匹配服务
	resp, err := client.RoomMatching(ctx, req)

	// 由于没有真实的API环境，这里只测试客户端创建和基本结构
	if err != nil {
		// In a real test environment, this might fail due to network or API issues, which is normal.
		// We log it and return, as the primary goal is to ensure the structure and flow are correct.
		t.Logf("RoomMatching failed as expected in test environment: %v", err)
		return
	}

	if resp == nil {
		t.Fatal("Expected response to be non-nil")
	}

	// 验证响应结构
	if len(resp.Matching) == 0 {
		t.Log("No matching results returned, which is expected in test environment")
	} else {
		// 验证匹配结果
		for _, match := range resp.Matching {
			t.Logf("Room: %s", match.RoomName)
			for _, supplier := range match.Suppliers {
				t.Logf("  Supplier: %s, Hotel: %s", supplier.Supplier, supplier.HotelID)
				t.Logf("    RoomID: %s, RoomCode: %s", supplier.RoomKey.RoomID, supplier.RoomKey.RoomCode)
			}
		}
	}
}
