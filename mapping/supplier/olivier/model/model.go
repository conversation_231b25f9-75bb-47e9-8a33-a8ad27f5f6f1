package model

import "encoding/json"

// RoomInfo represents a single room entry in the request list.
type RoomInfo struct {
	Hid       string `json:"hid"`      // Hotel SupplierHotelId
	Supplier  string `json:"supplier"` // Supplier name
	RoomCode1 string `json:"rcode1"`   // Room code 1
	RoomCode2 string `json:"rcode2"`   // Room code 2
	Amount    string `json:"amount"`   // Amount as string
	Name      string `json:"name"`     // Room name
}

// MatchRoomsRequest 房间匹配请求 (based on demo_request.json)
type MatchRoomsRequest struct {
	Credential string     `json:"credential"` // Credential for authentication
	Iso2       string     `json:"iso2"`       // ISO2 country code
	List       []RoomInfo `json:"list"`       // List of rooms to match
}

// MappedRoomInfo represents a single mapped room entry in the response.
type MappedRoomInfo struct {
	HotelID string          `json:"hotelID"` // Hotel SupplierHotelId
	Suppl   string          `json:"suppl"`   // Supplier name
	Rcode1  string          `json:"rcode1"`  // Room code 1
	Rcode2  string          `json:"rcode2"`  // Room code 2
	Amount  float64         `json:"amount"`  // Amount as float
	Name    string          `json:"name"`    // Room name
	Group   string          `json:"group"`   // Room group
	Range   string          `json:"range"`   // Room range
	Curated int             `json:"curated"` // Curated score
	Dataset json.RawMessage `json:"dataset"` // Raw JSON dataset
	AMN     string          `json:"AMN"`     // Amenity code?
	LangEN  string          `json:"LangEN"`  // English description
	LangES  string          `json:"LangES"`  // Spanish description
	LangPT  string          `json:"LangPT"`  // Portuguese description
	LangFR  string          `json:"LangFR"`  // French description
	LangIT  string          `json:"LangIT"`  // Italian description
	LangDE  string          `json:"LangDE"`  // German description
	Score   float64         `json:"score"`   // Matching score
}

// MatchRoomsResponse 房间匹配响应 (based on demo_response.json)
type MatchRoomsResponse struct {
	// Mapping structure: supplier -> hotel_id -> []MappedRoomInfo
	Mapping map[string]map[string][]MappedRoomInfo `json:"mapping"`

	// Matching structure: room name -> supplier -> hotel_id -> {}
	Matching map[string]map[string]map[string]RoomKeys `json:"matching"`
}

type RoomKeys struct {
	Rcode1 []interface{} `json:"rcode1"` // 3858,3859 (can be string or number)
	Rcode2 []string      `json:"rcode2"` // "APARTMENTO...refundable-False","APARTMENTO...refundable-True"
}
