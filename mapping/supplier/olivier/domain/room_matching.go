package domain

import "encoding/json"

// RoomInfo represents a single room entry in the request list.
type RoomInfo struct {
	HotelID   string `json:"hotelID"`   // Hotel SupplierHotelId
	Supplier  string `json:"supplier"`  // Supplier name
	RoomCode1 string `json:"roomCode1"` // Room code 1
	RoomCode2 string `json:"roomCode2"` // Room code 2
	Amount    string `json:"amount"`    // Amount as string
	Name      string `json:"name"`      // Room name
}

// RoomMatchingReq 房间匹配请求领域模型 (based on demo_request.json)
type RoomMatchingReq struct {
	Iso2 string     `json:"iso2"` // ISO2 country code
	List []RoomInfo `json:"list"` // List of rooms to match
}

// MappedRoomInfo represents a single mapped room entry in the response.
type MappedRoomInfo struct {
	HotelID  string `json:"hotelID"`  // Hotel SupplierHotelId
	Supplier string `json:"supplier"` // Supplier name

	RoomID   string `json:"roomID"`   // Room id (number)
	RoomCode string `json:"roomCode"` // Room code

	// optional
	Amount  float64         `json:"amount"`  // Amount as float
	Name    string          `json:"name"`    // Room name
	Group   string          `json:"group"`   // Room group
	Range   string          `json:"range"`   // Room range
	Curated int             `json:"curated"` // Curated score
	Dataset json.RawMessage `json:"dataset"` // Raw JSON dataset
	AMN     string          `json:"AMN"`     // Amenity code?
	LangEN  string          `json:"LangEN"`  // English description
	LangES  string          `json:"LangES"`  // Spanish description
	LangPT  string          `json:"LangPT"`  // Portuguese description
	LangFR  string          `json:"LangFR"`  // French description
	LangIT  string          `json:"LangIT"`  // Italian description
	LangDE  string          `json:"LangDE"`  // German description
	Score   float64         `json:"score"`   // Matching score
}

// RoomMatchingResp 房间匹配响应领域模型 (based on demo_response.json)
type RoomMatchingResp struct {
	Matching []RoomMatch `json:"matching"`
}

type RoomMatch struct {
	RoomName  string                  `json:"roomName"`
	Suppliers []RoomMatchSupplierItem `json:"suppliers"`
}

type RoomMatchSupplierItem struct {
	Supplier string  `json:"supplier"`
	HotelID  string  `json:"HotelId"`
	RoomKey  RoomKey `json:"roomKey"`
}
type RoomKey struct {
	RoomID     string  `json:"roomID,omitempty"`
	RoomCode   string  `json:"roomCode,omitempty"`
	Confidence float64 `json:"confidence,omitempty"`
}
