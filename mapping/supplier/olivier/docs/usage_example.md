# Olivier 房间匹配服务使用示例

## 概述

Olivier 是一个房间匹配算法服务，提供高准确率的房间匹配能力。本示例展示如何在房型映射系统中集成和使用 Olivier 服务。

## 基本使用

### 1. 初始化客户端

```go
import "hotel/mapping/supplier/olivier"

// 创建 Olivier 客户端
client := olivier.NewOlivierClient()
```

### 2. 创建房间匹配请求

```go
// 创建房间匹配请求
req := &domain.RoomMatchingReq{
    Iso2: "US", // ISO2 国家代码
    List: []domain.RoomInfo{
        {
            HotelID:   "12345",                    // 酒店ID
            Supplier:  "dida",                     // 供应商名称
            RoomCode1: "3858",                     // 房间代码1
            RoomCode2: "APARTMENTO...refundable-False", // 房间代码2
            Amount:    "100.00",                   // 价格
            Name:      "Standard Room",            // 房间名称
        },
        {
            HotelID:   "67890",
            Supplier:  "ctrip",
            RoomCode1: "1234",
            RoomCode2: "DELUXE...refundable-True",
            Amount:    "150.00",
            Name:      "Deluxe Room",
        },
    },
}
```

### 3. 调用房间匹配服务

```go
// 调用房间匹配服务
ctx := context.Background()
resp, err := client.RoomMatching(ctx, req)
if err != nil {
    log.Printf("房间匹配失败: %v", err)
    return err
}

// 处理匹配结果
for _, match := range resp.Matching {
    fmt.Printf("房间名称: %s\n", match.RoomName)
    for _, supplier := range match.Suppliers {
        fmt.Printf("  供应商: %s, 酒店ID: %s\n", supplier.Supplier, supplier.HotelID)
        fmt.Printf("    房间ID: %s, 房间代码: %s\n", 
            supplier.RoomKey.RoomID, supplier.RoomKey.RoomCode)
    }
}
```

## 响应结构

### 匹配结果

```go
type RoomMatchingResp struct {
    Matching []RoomMatch `json:"matching"`
}

type RoomMatch struct {
    RoomName  string                  `json:"roomName"`
    Suppliers []RoomMatchSupplierItem `json:"suppliers"`
}

type RoomMatchSupplierItem struct {
    Supplier string  `json:"supplier"`
    HotelID  string  `json:"HotelId"`
    RoomKey  RoomKey `json:"roomKey"`
}

type RoomKey struct {
    RoomID     string  `json:"roomID,omitempty"`
    RoomCode   string  `json:"roomCode,omitempty"`
    Confidence float64 `json:"confidence,omitempty"`
}
```

## 配置说明

### 硬编码配置

配置直接硬编码在代码中，简化了配置管理：

```go
type Config struct {
    Host   string `json:"host"`   // API服务器地址
    APIKey string `json:"apiKey"` // API密钥
}
```

当前配置：
- Host: `https://meta.room-matching.com`
- APIKey: `amitGodMode`

## 错误处理

```go
resp, err := client.RoomMatching(ctx, req)
if err != nil {
    log.Printf("房间匹配服务调用失败: %v", err)
    return err
}
```

## 性能优化

1. **连接池**: 使用 req 库的HTTP连接池
2. **重试机制**: 自动重试失败的请求（3次）
3. **超时控制**: 30秒请求超时
4. **简单配置**: 无需复杂的配置管理

## 监控和日志

服务会自动记录以下信息：
- 请求和响应日志
- 错误信息
- API调用统计

## 注意事项

1. **API Key**: 使用硬编码的API Key
2. **国家代码**: 使用正确的ISO2国家代码
3. **房间代码**: 确保房间代码格式正确
4. **网络连接**: 确保能够访问 Olivier API 服务器
5. **专注映射**: 只提供房型映射功能，不涉及其他供应商功能

## 与根目录 supplier 的区别

| 特性 | mapping/supplier | 根目录 supplier |
|------|------------------|-----------------|
| 职责 | 专注房型映射 | 完整供应商接入 |
| 注册 | 无需注册到 factory | 需要注册到 factory |
| 认证 | 硬编码配置 | 从 ctx 获取认证 |
| 接口 | 单一映射接口 | 多种供应商接口 |
| 配置 | 简单硬编码 | 复杂配置管理 |
| 复杂度 | 低 | 高 | 