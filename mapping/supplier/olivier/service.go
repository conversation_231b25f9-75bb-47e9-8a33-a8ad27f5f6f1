package olivier

import (
	"context"
	"time"

	"hotel/mapping/supplier/olivier/converter"
	olivierDomain "hotel/mapping/supplier/olivier/domain"
	"hotel/mapping/supplier/olivier/model"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"

	"github.com/imroc/req/v3"
)

type OlivierClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func NewOlivierClient() *OlivierClient {
	// 使用标准的 supplierDomain.Config
	config := domain.Config{
		API: map[string]domain.APIEndpoint{
			"RoomMatching": {
				BaseURL:    "https://meta.room-matching.com",
				Path:       "/room",
				HttpMethod: "POST",
			},
		},
	}

	// 创建 SupplierUtil，使用 supplier_util 的 middleware
	su := &middleware.SupplierUtil{
		Config: config,
		HttpCli: req.C().
			SetTimeout(30 * time.Second).
			SetCommonRetryCount(3).
			OnBeforeRequest(middleware.RequestLogMiddleware).
			OnAfterResponse(middleware.ResponseLogMiddleware),
		Supplier: domain.Supplier_Olivier,
	}

	wrapper := &middleware.SupplierUtilWrapper[model.Properties]{
		SupplierUtil: su,
	}

	return &OlivierClient{
		SupplierUtilWrapper: wrapper,
	}
}

func (s *OlivierClient) Supplier() domain.Supplier {
	return domain.Supplier_Olivier
}

// RoomMatching 提供房间匹配服务
func (s *OlivierClient) RoomMatching(ctx context.Context, req *olivierDomain.RoomMatchingReq) (*olivierDomain.RoomMatchingResp, error) {
	// 转换领域请求到 API 请求
	apiReq := converter.ToMatchRoomsRequest(req)

	// 硬编码 API Key，不从 ctx 获取
	apiReq.Credential = "amitGodMode"

	// 调用外部 API，使用 supplier_util 的 Execute 方法
	apiResp := new(model.MatchRoomsResponse)

	// 创建认证信息
	auth := &model.Properties{
		APIKey: "amitGodMode",
	}

	err := s.Execute(ctx, "RoomMatching", apiReq, apiResp, middleware.WithAuth(auth))
	if err != nil {
		return nil, err
	}

	// 转换 API 响应到领域响应
	domainResp := converter.ToRoomMatchingResp(apiResp)

	return domainResp, nil
}
