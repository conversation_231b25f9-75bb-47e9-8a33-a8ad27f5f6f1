# mapping/supplier/olivier

本目录为 Olivier 房间匹配供应商接入模块，专注房型映射功能，使用 supplier_util 架构但简化认证和注册。

## 简介

Olivier是一个房间匹配算法服务，宣称具有高准确率的匹配能力。本模块提供了对Olivier API的封装，使用 supplier_util 架构但简化了认证和注册机制，专注房型映射功能。

## 主要内容

- `model/`：API数据结构
- `converter/`：数据转换逻辑
- `domain/`：领域模型
- `service.go`：主要服务实现（使用 supplier_util）
- `service_test.go`：测试文件

## 架构特点

### 使用 supplier_util 架构
- 使用 `middleware.SupplierUtil` 进行HTTP请求
- 支持统一的日志记录和错误处理
- 支持重试机制和超时控制
- 使用统一的中间件流程
- **使用 `Execute` 方法进行API调用**

### 简化认证和注册
- 硬编码API Key，无需从ctx获取认证
- 不注册到 factory，专注房型映射功能
- 简化的配置管理
- 专注单一映射接口

## 使用方法

### 1. 初始化客户端

```go
import "hotel/mapping/supplier/olivier"

// 创建 Olivier 客户端
client := olivier.NewOlivierClient()
```

### 2. 创建房间匹配请求

```go
// 创建房间匹配请求
req := &domain.RoomMatchingReq{
    Iso2: "US", // ISO2 国家代码
    List: []domain.RoomInfo{
        {
            HotelID:   "12345",                    // 酒店ID
            Supplier:  "dida",                     // 供应商名称
            RoomCode1: "3858",                     // 房间代码1
            RoomCode2: "APARTMENTO...refundable-False", // 房间代码2
            Amount:    "100.00",                   // 价格
            Name:      "Standard Room",            // 房间名称
        },
        {
            HotelID:   "67890",
            Supplier:  "ctrip",
            RoomCode1: "1234",
            RoomCode2: "DELUXE...refundable-True",
            Amount:    "150.00",
            Name:      "Deluxe Room",
        },
    },
}
```

### 3. 调用房间匹配服务

```go
// 调用房间匹配服务
ctx := context.Background()
resp, err := client.RoomMatching(ctx, req)
if err != nil {
    log.Printf("房间匹配失败: %v", err)
    return err
}

// 处理匹配结果
for _, match := range resp.Matching {
    fmt.Printf("房间名称: %s\n", match.RoomName)
    for _, supplier := range match.Suppliers {
        fmt.Printf("  供应商: %s, 酒店ID: %s\n", supplier.Supplier, supplier.HotelID)
        fmt.Printf("    房间ID: %s, 房间代码: %s\n",
            supplier.RoomKey.RoomID, supplier.RoomKey.RoomCode)
    }
}
```

## 配置

配置直接硬编码在代码中，简化了配置管理：

```go
// 使用标准的 supplierDomain.Config
config := domain.Config{
    API: map[string]domain.APIEndpoint{
        "RoomMatching": {
            BaseURL:    "https://meta.room-matching.com",
            Path:       "/room",
            HttpMethod: "POST",
        },
    },
}
```

当前配置：
- Host: `https://meta.room-matching.com`
- APIKey: `amitGodMode`

## API文档

API文档地址：<https://documenter.getpostman.com/view/12680266/2sA2rGuejB>

## 迭代开发约定

- 使用 supplier_util 架构
- 简化认证和注册机制
- 专注房型映射功能
- 变更需同步更新README

## 注意事项

- 使用 supplier_util 的 `Execute` 方法进行API调用
- 硬编码配置，无需复杂认证
- 专注房型映射，不涉及其他供应商功能
- 遵循领域驱动设计，分离API模型和领域模型
- 不注册到 factory，保持独立性
- 测试时需要提供 `BaseRequestContextPayload` 上下文
