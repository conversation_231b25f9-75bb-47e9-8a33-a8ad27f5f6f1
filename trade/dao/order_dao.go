package dao

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"hotel/common/money"
	"hotel/common/types"
	"hotel/trade/domain"
	supplierDomain "hotel/supplier/domain"
)

// OrderDao 订单数据访问对象
type OrderDao struct {
	orderModel       *OrderModel
	supplierOrderModel *SupplierOrderModel
	refundOrderModel RefundOrderModel
}

// NewOrderDao 创建新的订单DAO
func NewOrderDao(orderModel *OrderModel, supplierOrderModel *SupplierOrderModel, refundOrderModel RefundOrderModel) *OrderDao {
	return &OrderDao{
		orderModel:         orderModel,
		supplierOrderModel: supplierOrderModel,
		refundOrderModel:   refundOrderModel,
	}
}

// FindByCriteria 根据条件查询订单
func (d *OrderDao) FindByCriteria(ctx context.Context, criteria OrderQueryCriteria) ([]*domain.Order, error) {
	daoOrders, err := d.orderModel.FindByCriteria(ctx, &criteria)
	if err != nil {
		return nil, err
	}

	var domainOrders []*domain.Order
	for _, daoOrder := range daoOrders {
		domainOrder := ConvertDAOOrderToDomain(daoOrder)
		domainOrders = append(domainOrders, &domainOrder)
	}

	return domainOrders, nil
}

// GetByID 根据ID获取订单
func (d *OrderDao) GetByID(ctx context.Context, id types.ID) (*domain.Order, error) {
	daoOrder, err := d.orderModel.FindOne(ctx, uint64(id))
	if err != nil {
		return nil, err
	}

	domainOrder := ConvertDAOOrderToDomain(daoOrder)
	return &domainOrder, nil
}

// Insert 插入新订单
func (d *OrderDao) Insert(ctx context.Context, order *domain.Order) error {
	daoOrder := ConvertDomainOrderToDAO(*order)
	_, err := d.orderModel.Insert(ctx, daoOrder)
	return err
}

// Update 更新订单
func (d *OrderDao) Update(ctx context.Context, order *domain.Order) error {
	daoOrder := ConvertDomainOrderToDAO(*order)
	return d.orderModel.Update(ctx, daoOrder)
}

// FindSupplierOrdersByOrderId 根据订单ID查找供应商订单
func (d *OrderDao) FindSupplierOrdersByOrderId(ctx context.Context, orderId types.ID) ([]*domain.SupplierOrder, error) {
	// 这里需要实现具体的查询逻辑
	// 暂时返回空切片
	return []*domain.SupplierOrder{}, nil
}

// InsertSupplierOrder 插入供应商订单
func (d *OrderDao) InsertSupplierOrder(ctx context.Context, supplierOrder *domain.SupplierOrder) error {
	daoOrder := ConvertDomainSupplierOrderToDAO(*supplierOrder)
	_, err := d.supplierOrderModel.Insert(ctx, daoOrder)
	return err
}

// UpdateStatus 更新订单状态
func (d *OrderDao) UpdateStatus(ctx context.Context, id types.ID, status domain.OrderStatus) error {
	return d.orderModel.UpdateStatus(ctx, id, status)
}

// RefundOrder 退款订单操作
func (d *OrderDao) RefundOrder() *RefundOrderOperations {
	return &RefundOrderOperations{
		model: d.refundOrderModel,
	}
}

type RefundOrderOperations struct {
	model RefundOrderModel
}

func (r *RefundOrderOperations) Insert(ctx context.Context, refundOrder *domain.RefundOrder) error {
	daoOrder := convertDomainRefundOrderToDAO(*refundOrder)
	_, err := r.model.Insert(ctx, daoOrder)
	return err
}

// CountByCriteria 根据条件统计订单数量
func (d *OrderDao) CountByCriteria(ctx context.Context, criteria *OrderQueryCriteria) (int64, error) {
	// 这里需要实现具体的统计逻辑
	// 暂时返回0
	return 0, nil
}

// FindList 查询订单列表
func (d *OrderDao) FindList(ctx context.Context, limit int) ([]*domain.Order, error) {
	// 这里需要实现具体的查询逻辑
	return nil, nil
}



// SupplierOrder 获取供应商订单相关操作
type SupplierOrderOperations struct {
	model *SupplierOrderModel
}

// Insert 插入供应商订单
func (s *SupplierOrderOperations) Insert(ctx context.Context, supplierOrder *domain.SupplierOrder) error {
	daoOrder := ConvertDomainSupplierOrderToDAO(*supplierOrder)
	_, err := s.model.Insert(ctx, daoOrder)
	return err
}

func (s *SupplierOrderOperations) FindByOrderId(ctx context.Context, orderId types.ID) ([]*domain.SupplierOrder, error) {
	// 这里需要实现具体的查询逻辑
	return nil, nil
}

// SupplierOrder 返回供应商订单操作对象
func (d *OrderDao) SupplierOrder() *SupplierOrderOperations {
	return &SupplierOrderOperations{model: d.supplierOrderModel}
}

// --- model <-> domain 转换 ---
// ConvertDAOOrderToDomain 将 DAO 层的 Order 转换为 Domain 层的 Order
func ConvertDAOOrderToDomain(daoOrder *Order) domain.Order {
	// 状态映射
	status := domain.OrderStatus(daoOrder.Status)

	// 解析 BizInfo
	bizInfo := parseBizInfo(daoOrder.BizInfo)

	// 提取预订人信息，进行类型断言
	var booker supplierDomain.Booker
	if bookerInfo := extractBookerInfoFromBizInfo(bizInfo); bookerInfo != nil {
		if b, ok := bookerInfo.(supplierDomain.Booker); ok {
			booker = b
		}
	}

	return domain.Order{
		Id:          types.ID(daoOrder.Id),
		ReferenceNo: daoOrder.ReferenceNo,
		Status:      status,
		CreateTime:  daoOrder.CreateTime,
		Rooms:       extractRoomInfoFromBizInfo(bizInfo),
		Booker:      booker,
		Tags:        extractTagsFromBizInfo(bizInfo),
		BizInfo:     bizInfo,
	}
}

func ConvertDomainOrderToDAO(domainOrder domain.Order) *Order {
	// 序列化 BizInfo
	bizInfoStr := serializeBizInfo(domainOrder.BizInfo)

	return &Order{
		Id:          uint64(domainOrder.Id),
		ReferenceNo: domainOrder.ReferenceNo,
		Status:      int64(domainOrder.Status),
		CreateTime:  domainOrder.CreateTime,
		UpdateTime:  time.Now(),
		BizInfo:     bizInfoStr,
	}
}

// ConvertDAOSupplierOrderToDomain 将 DAO 层的 SupplierOrder 转换为 Domain 层的 SupplierOrder
func ConvertDAOSupplierOrderToDomain(daoOrder *SupplierOrder) domain.SupplierOrder {
	return domain.SupplierOrder{
		ID:              types.ID(daoOrder.Id),
		OrderID:         types.ID(daoOrder.OrderId),
		SupplierID:      fmt.Sprintf("%d", daoOrder.SupplierId),
		SupplierOrderID: daoOrder.SupplierOrderId,
		Status:          domain.SupplierOrderStatus(daoOrder.Status),
		RawData:         daoOrder.BizInfo,
		CreateTime:      daoOrder.CreateTime,
		UpdateTime:      daoOrder.UpdateTime,
	}
}

func ConvertDomainSupplierOrderToDAO(domainOrder domain.SupplierOrder) *SupplierOrder {
	supplierId, _ := strconv.ParseInt(domainOrder.SupplierID, 10, 64)
	return &SupplierOrder{
		Id:              uint64(domainOrder.ID),
		OrderId:         int64(domainOrder.OrderID),
		SupplierId:      supplierId,
		SupplierOrderId: domainOrder.SupplierOrderID,
		Status:          int64(domainOrder.Status),
		BizInfo:         domainOrder.RawData,
		CreateTime:      domainOrder.CreateTime,
		UpdateTime:      domainOrder.UpdateTime,
	}
}

// convertDAORefundOrderToDomain 将 DAO 层的 RefundOrder 转换为 Domain 层的 RefundOrder
func convertDAORefundOrderToDomain(daoOrder *RefundOrder) domain.RefundOrder {
	// 将分为单位的金额转换为元
	amount := float64(daoOrder.BuyerRefundAmount) / 100.0
	return domain.RefundOrder{
		ID:           types.ID(daoOrder.Id),
		OrderID:      types.ID(daoOrder.OrderId),
		RefundAmount: money.NewMoney("CNY", amount),
		Status:       domain.RefundOrderStatus(daoOrder.Status),
		CreateTime:   daoOrder.CreateTime,
		UpdateTime:   daoOrder.UpdateTime,
		// 其他字段从 BizInfo 中解析，这里先设置默认值
		RefundReason: "退款",
	}
}

func convertDomainRefundOrderToDAO(domainOrder domain.RefundOrder) *RefundOrder {
	// 将元转换为分为单位存储
	amountInCents := int64(domainOrder.RefundAmount.Amount * 100)
	return &RefundOrder{
		Id:                 uint64(domainOrder.ID),
		OrderId:            uint64(domainOrder.OrderID),
		Status:             int64(domainOrder.Status),
		BuyerRefundAmount:  amountInCents,
		SellerRefundAmount: amountInCents, // 简化处理
		BizInfo:            "", // 可以序列化更多信息到这里
		CreateTime:         domainOrder.CreateTime,
		UpdateTime:         domainOrder.UpdateTime,
	}
}

// 辅助函数
func parseBizInfo(bizInfoStr string) *domain.OrderBizInfo {
	// 这里应该实现 JSON 解析逻辑
	// 暂时返回空对象
	return &domain.OrderBizInfo{}
}

func serializeBizInfo(bizInfo *domain.OrderBizInfo) string {
	// 这里应该实现 JSON 序列化逻辑
	// 暂时返回空字符串
	return ""
}

func extractRoomInfoFromBizInfo(bizInfo *domain.OrderBizInfo) []domain.OrderRoom {
	// 这里应该从 bizInfo 中提取房间信息
	// 暂时返回空切片
	return []domain.OrderRoom{}
}

func extractBookerInfoFromBizInfo(bizInfo *domain.OrderBizInfo) interface{} {
	// 这里应该从 bizInfo 中提取预订人信息
	// 暂时返回 nil
	return nil
}

func extractTagsFromBizInfo(bizInfo *domain.OrderBizInfo) []string {
	// 这里应该从 bizInfo 中提取标签信息
	// 暂时返回空切片
	return []string{}
}
