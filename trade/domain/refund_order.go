package domain

import (
	"hotel/common/money"
	"hotel/common/types"
	"time"
)

// RefundOrderStatus 退款订单状态
type RefundOrderStatus int64

const (
	RefundOrderStatusCreated    RefundOrderStatus = 1 // 已创建
	RefundOrderStatusProcessing RefundOrderStatus = 2 // 处理中
	RefundOrderStatusCompleted  RefundOrderStatus = 3 // 已完成
	RefundOrderStatusFailed     RefundOrderStatus = 4 // 失败
	RefundOrderStatusCancelled  RefundOrderStatus = 5 // 已取消
)

func (s RefundOrderStatus) String() string {
	switch s {
	case RefundOrderStatusCreated:
		return "已创建"
	case RefundOrderStatusProcessing:
		return "处理中"
	case RefundOrderStatusCompleted:
		return "已完成"
	case RefundOrderStatusFailed:
		return "失败"
	case RefundOrderStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

func (s RefundOrderStatus) Int64() int64 {
	return int64(s)
}

// RefundOrder 退款订单
type RefundOrder struct {
	ID              types.ID          `json:"id"`
	OrderID         types.ID          `json:"orderId"`
	RefundAmount    money.Money       `json:"refundAmount"`
	RefundReason    string            `json:"refundReason"`
	Status          RefundOrderStatus `json:"status"`
	CreateTime      time.Time         `json:"createTime"`
	UpdateTime      time.Time         `json:"updateTime"`
	ProcessTime     *time.Time        `json:"processTime,omitempty"`
	CompleteTime    *time.Time        `json:"completeTime,omitempty"`
	OperatorID      *types.ID         `json:"operatorId,omitempty"`
	OperatorName    string            `json:"operatorName,omitempty"`
	RefundMethod    string            `json:"refundMethod,omitempty"`
	RefundAccount   string            `json:"refundAccount,omitempty"`
	RefundReference string            `json:"refundReference,omitempty"`
	Remark          string            `json:"remark,omitempty"`
}
