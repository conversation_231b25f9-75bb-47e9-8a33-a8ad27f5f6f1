package domain

import (
	"hotel/common/types"
	"time"
)

// SupplierOrder 供应商订单
type SupplierOrder struct {
	ID              types.ID            `json:"id"`
	OrderID         types.ID            `json:"orderId"`
	SupplierID      string              `json:"supplierId"`
	SupplierOrderID string              `json:"supplierOrderId"`
	Status          SupplierOrderStatus `json:"status"`
	CreateTime      time.Time           `json:"createTime"`
	UpdateTime      time.Time           `json:"updateTime"`
	RawData         string              `json:"rawData,omitempty"`
}
