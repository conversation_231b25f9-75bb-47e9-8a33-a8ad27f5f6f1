package domain

import (
	supplierDomain "hotel/supplier/domain"
)

// OrderBizInfo represents business-specific information and metadata for an order
// It stores supplier responses, seller inputs, and additional labels for business logic
type OrderBizInfo struct {
	CheckAvailResp      *supplierDomain.CheckAvailResp      `json:"checkAvailResp,omitempty"` // Response from supplier's availability check
	SellerInputPayloads *supplierDomain.SellerInputPayloads `json:"sellerPayload,omitempty"`  // Input payloads from the seller/agent
	Label               []string                            `json:"label,omitempty"`          // Additional business labels for categorization
}
