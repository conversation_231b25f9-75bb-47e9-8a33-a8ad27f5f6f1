package domain

import (
	"fmt"
	"math"

	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

// PriceCalculationResult 价格计算结果
type PriceCalculationResult struct {
	BuyerAmount              int64  `json:"buyerAmount"`              // 买方金额（分）
	BuyerCurrency            string `json:"buyerCurrency"`            // 买方币种
	SellerAmount             int64  `json:"sellerAmount"`             // 卖方金额（分）
	SellerCurrency           string `json:"sellerCurrency"`           // 卖方币种
	TenantRevenueAmountUsd   int64  `json:"tenantRevenueAmountUsd"`   // 租户收益（美元分）
	CustomerRevenueAmountUsd int64  `json:"customerRevenueAmountUsd"` // 客户收益（美元分）
	PlatformRevenueAmountUsd int64  `json:"platformRevenueAmountUsd"` // 平台收益（美元分）
}

// PriceCalculationInput 价格计算输入
type PriceCalculationInput struct {
	RatePkgInfo  *supplierDomain.RoomRatePkg `json:"ratePkgInfo"`
	Operator     *userDomain.User            `json:"operator"`
	ExchangeRate float64                     `json:"exchangeRate"` // 汇率（如果需要货币转换）
}

// PriceCalculator 价格计算器
type PriceCalculator struct {
	// 可以添加配置参数，如默认利润率、汇率服务等
	DefaultTenantMarginPercent   float64 // 默认租户利润率
	DefaultCustomerMarginPercent float64 // 默认客户利润率
	DefaultPlatformMarginPercent float64 // 默认平台利润率
}

// NewPriceCalculator 创建价格计算器
func NewPriceCalculator() *PriceCalculator {
	return &PriceCalculator{
		DefaultTenantMarginPercent:   0.10, // 10%
		DefaultCustomerMarginPercent: 0.05, // 5%
		DefaultPlatformMarginPercent: 0.03, // 3%
	}
}

// CalculatePrice 计算订单价格
func (pc *PriceCalculator) CalculatePrice(input *PriceCalculationInput) (*PriceCalculationResult, error) {
	if input == nil || input.RatePkgInfo == nil {
		return nil, fmt.Errorf("invalid input: ratePkgInfo is required")
	}

	// 获取基础价格
	basePrice := input.RatePkgInfo.Rate.FinalRate

	// 确保货币一致性
	currency := basePrice.Currency

	// 转换为分（避免浮点数精度问题）todo: by 币种处理
	basePriceCents := int64(basePrice.Amount * 100)

	// 计算各层级的利润
	result := &PriceCalculationResult{
		BuyerCurrency:  currency,
		SellerCurrency: currency,
	}

	// 根据用户层级计算利润分配
	if input.Operator != nil {
		result = pc.calculateByUserLevel(result, basePriceCents, input.Operator)
	} else {
		// 默认计算逻辑
		result = pc.calculateDefault(result, basePriceCents)
	}

	// 汇率转换（如果需要）
	if input.ExchangeRate > 0 && input.ExchangeRate != 1.0 {
		result = pc.applyExchangeRate(result, input.ExchangeRate)
	}

	// 转换为美元（用于收益计算）
	result = pc.convertRevenueToUSD(result, currency)

	return result, nil
}

// calculateByUserLevel 根据用户层级计算价格
func (pc *PriceCalculator) calculateByUserLevel(result *PriceCalculationResult, basePriceCents int64, operator *userDomain.User) *PriceCalculationResult {
	// 这里可以根据用户的实体层级来计算不同的利润率
	// 暂时使用默认逻辑
	return pc.calculateDefault(result, basePriceCents)
}

// calculateDefault 默认价格计算逻辑
func (pc *PriceCalculator) calculateDefault(result *PriceCalculationResult, basePriceCents int64) *PriceCalculationResult {
	// 计算各层级加价
	tenantMargin := int64(float64(basePriceCents) * pc.DefaultTenantMarginPercent)
	customerMargin := int64(float64(basePriceCents) * pc.DefaultCustomerMarginPercent)
	platformMargin := int64(float64(basePriceCents) * pc.DefaultPlatformMarginPercent)

	// 卖方金额 = 基础价格
	result.SellerAmount = basePriceCents

	// 买方金额 = 基础价格 + 所有加价
	result.BuyerAmount = basePriceCents + tenantMargin + customerMargin + platformMargin

	// 各层级收益
	result.TenantRevenueAmountUsd = tenantMargin
	result.CustomerRevenueAmountUsd = customerMargin
	result.PlatformRevenueAmountUsd = platformMargin

	return result
}

// applyExchangeRate 应用汇率转换
func (pc *PriceCalculator) applyExchangeRate(result *PriceCalculationResult, rate float64) *PriceCalculationResult {
	result.BuyerAmount = int64(float64(result.BuyerAmount) * rate)
	result.SellerAmount = int64(float64(result.SellerAmount) * rate)
	return result
}

// convertRevenueToUSD 将收益转换为美元
func (pc *PriceCalculator) convertRevenueToUSD(result *PriceCalculationResult, currency string) *PriceCalculationResult {
	// 如果已经是美元，无需转换
	if currency == "USD" {
		return result
	}

	// 这里应该调用汇率服务进行转换
	// 暂时使用固定汇率作为示例
	var usdRate float64 = 1.0
	switch currency {
	case "CNY":
		usdRate = 0.14 // 1 CNY = 0.14 USD (示例汇率)
	case "EUR":
		usdRate = 1.1 // 1 EUR = 1.1 USD (示例汇率)
	case "GBP":
		usdRate = 1.25 // 1 GBP = 1.25 USD (示例汇率)
	default:
		usdRate = 1.0 // 未知货币，按1:1处理
	}

	result.TenantRevenueAmountUsd = int64(float64(result.TenantRevenueAmountUsd) * usdRate)
	result.CustomerRevenueAmountUsd = int64(float64(result.CustomerRevenueAmountUsd) * usdRate)
	result.PlatformRevenueAmountUsd = int64(float64(result.PlatformRevenueAmountUsd) * usdRate)

	return result
}

// ValidatePrice 验证价格计算结果
func (pc *PriceCalculator) ValidatePrice(result *PriceCalculationResult) error {
	if result == nil {
		return fmt.Errorf("price calculation result is nil")
	}

	if result.BuyerAmount <= 0 {
		return fmt.Errorf("invalid buyer amount: %d", result.BuyerAmount)
	}

	if result.SellerAmount <= 0 {
		return fmt.Errorf("invalid seller amount: %d", result.SellerAmount)
	}

	if result.BuyerAmount < result.SellerAmount {
		return fmt.Errorf("buyer amount (%d) should not be less than seller amount (%d)",
			result.BuyerAmount, result.SellerAmount)
	}

	// 检查收益是否合理（不应该超过总金额的50%）
	totalRevenue := result.TenantRevenueAmountUsd + result.CustomerRevenueAmountUsd + result.PlatformRevenueAmountUsd
	maxRevenue := result.BuyerAmount / 2
	if totalRevenue > maxRevenue {
		return fmt.Errorf("total revenue (%d) exceeds maximum allowed (%d)", totalRevenue, maxRevenue)
	}

	return nil
}

// FormatPrice 格式化价格显示
func (pc *PriceCalculator) FormatPrice(amount int64, currency string) string {
	// 将分转换为元
	dollars := float64(amount) / 100
	return fmt.Sprintf("%.2f %s", dollars, currency)
}

// CalculateTotalNights 计算总住宿夜数
func CalculateTotalNights(checkIn, checkOut int64) int {
	// 简单的日期差计算，实际应该使用更精确的日期库
	return int(math.Max(1, float64(checkOut-checkIn)))
}

// CalculateAverageNightlyRate 计算平均每晚价格
func (pc *PriceCalculator) CalculateAverageNightlyRate(totalAmount int64, nights int) int64 {
	if nights <= 0 {
		return totalAmount
	}
	return totalAmount / int64(nights)
}
