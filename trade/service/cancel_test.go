package service

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"hotel/trade/dao"
	"hotel/trade/domain"
	"hotel/trade/protocol"
)

// MockOrderDao for testing
type MockOrderDao struct {
	mock.Mock
}

func (m *MockOrderDao) FindOne(ctx context.Context, id uint64) (*dao.Order, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*dao.Order), args.Error(1)
}

func (m *MockOrderDao) Update(ctx context.Context, order *dao.Order) error {
	args := m.Called(ctx, order)
	return args.Error(0)
}

func (m *MockOrderDao) FindByOrderId(ctx context.Context, orderId int64) ([]*dao.SupplierOrder, error) {
	args := m.Called(ctx, orderId)
	return args.Get(0).([]*dao.SupplierOrder), args.Error(1)
}

// MockCQRSProducer for testing
type MockCQRSProducer struct {
	mock.Mock
}

func (m *MockCQRSProducer) Publish(ctx context.Context, topic string, message interface{}) error {
	args := m.Called(ctx, topic, message)
	return args.Error(0)
}

func (m *MockCQRSProducer) PublishWithOptions(ctx context.Context, topic string, message interface{}, opts interface{}) error {
	args := m.Called(ctx, topic, message, opts)
	return args.Error(0)
}

func (m *MockCQRSProducer) PublishRaw(ctx context.Context, topic string, message []byte) error {
	args := m.Called(ctx, topic, message)
	return args.Error(0)
}

func (m *MockCQRSProducer) PublishRawWithOptions(ctx context.Context, topic string, message []byte, opts interface{}) error {
	args := m.Called(ctx, topic, message, opts)
	return args.Error(0)
}

func (m *MockCQRSProducer) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockCancelAuditor for testing
type MockCancelAuditor struct {
	mock.Mock
}

func (m *MockCancelAuditor) RecordCancelRequest(ctx context.Context, orderID uint64, userID uint64, reason string) {
	m.Called(ctx, orderID, userID, reason)
}

func (m *MockCancelAuditor) RecordCancelSuccess(ctx context.Context, order *dao.Order, originalStatus domain.OrderStatus, supplierCancelSuccess bool, processingTimeMs int64) {
	m.Called(ctx, order, originalStatus, supplierCancelSuccess, processingTimeMs)
}

func (m *MockCancelAuditor) RecordCancelFailure(ctx context.Context, orderID uint64, originalStatus domain.OrderStatus, failureType string, errorMsg string, processingTimeMs int64) {
	m.Called(ctx, orderID, originalStatus, failureType, errorMsg, processingTimeMs)
}

// TestTradeService_Cancel tests the Cancel method
func TestTradeService_Cancel(t *testing.T) {
	tests := []struct {
		name           string
		req            *protocol.CancelReq
		mockOrder      *dao.Order
		mockFindError  error
		mockUpdateError error
		mockPublishError error
		expectedError  bool
	}{
		{
			name: "successful_cancel",
			req: &protocol.CancelReq{
				OrderId: "12345",
			},
			mockOrder: &dao.Order{
				Id:     12345,
				Status: int64(domain.OrderStatePaid),
			},
			mockFindError:   nil,
			mockUpdateError: nil,
			mockPublishError: nil,
			expectedError:   false,
		},
		{
			name: "order_not_found",
			req: &protocol.CancelReq{
				OrderId: "99999",
			},
			mockOrder:     nil,
			mockFindError: assert.AnError,
			expectedError: true,
		},
		{
			name: "invalid_order_id",
			req: &protocol.CancelReq{
				OrderId: "",
			},
			expectedError: true,
		},
		{
			name: "order_already_cancelled",
			req: &protocol.CancelReq{
				OrderId: "12345",
			},
			mockOrder: &dao.Order{
				Id:     12345,
				Status: int64(domain.OrderStateCancelled),
			},
			mockFindError: nil,
			expectedError: true,
		},
		{
			name: "database_update_failure",
			req: &protocol.CancelReq{
				OrderId: "12345",
			},
			mockOrder: &dao.Order{
				Id:     12345,
				Status: int64(domain.OrderStatePaid),
			},
			mockFindError:   nil,
			mockUpdateError: assert.AnError,
			expectedError:   true,
		},
		{
			name: "message_queue_failure",
			req: &protocol.CancelReq{
				OrderId: "12345",
			},
			mockOrder: &dao.Order{
				Id:     12345,
				Status: int64(domain.OrderStatePaid),
			},
			mockFindError:    nil,
			mockUpdateError:  nil,
			mockPublishError: assert.AnError,
			expectedError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockOrderDao := &MockOrderDao{}
			mockProducer := &MockCQRSProducer{}
			mockAuditor := &MockCancelAuditor{}

			// Create service with mocks
			service := &TradeService{
				orderDao: &dao.OrderDao{
					Order: mockOrderDao,
				},
				cqrsProducer:  mockProducer,
				cancelAuditor: mockAuditor,
			}

			ctx := context.Background()

			// Setup expectations
			if tt.req.OrderId != "" {
				mockAuditor.On("RecordCancelRequest", ctx, uint64(12345), uint64(0), "User requested cancellation").Return()

				if tt.mockFindError == nil && tt.mockOrder != nil {
					mockOrderDao.On("FindOne", ctx, uint64(12345)).Return(tt.mockOrder, tt.mockFindError)

					if tt.mockOrder.Status != int64(domain.OrderStateCancelled) && tt.mockOrder.Status != int64(domain.OrderStateCompleted) {
						if tt.mockUpdateError == nil {
							mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(tt.mockUpdateError)

							if tt.mockPublishError == nil {
								mockProducer.On("Publish", ctx, CancelTopic, mock.AnythingOfType("*dao.Order")).Return(tt.mockPublishError)
								mockAuditor.On("RecordCancelSuccess", ctx, mock.AnythingOfType("*dao.Order"), mock.AnythingOfType("domain.OrderStatus"), true, mock.AnythingOfType("int64")).Return()
							} else {
								mockProducer.On("Publish", ctx, CancelTopic, mock.AnythingOfType("*dao.Order")).Return(tt.mockPublishError)
								mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(nil) // rollback
								mockAuditor.On("RecordCancelFailure", ctx, uint64(12345), mock.AnythingOfType("domain.OrderStatus"), "message_queue", mock.AnythingOfType("string"), mock.AnythingOfType("int64")).Return()
							}
						} else {
							mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(tt.mockUpdateError)
						}
					}
				} else {
					mockOrderDao.On("FindOne", ctx, uint64(12345)).Return(tt.mockOrder, tt.mockFindError)
				}
			}

			// Execute
			err := service.Cancel(ctx, tt.req)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify mocks
			mockOrderDao.AssertExpectations(t)
			mockProducer.AssertExpectations(t)
			mockAuditor.AssertExpectations(t)
		})
	}
}

// TestTradeService_handleCancelMessage tests the handleCancelMessage method
func TestTradeService_handleCancelMessage(t *testing.T) {
	tests := []struct {
		name          string
		message       []byte
		mockOrder     *dao.Order
		expectedError bool
	}{
		{
			name: "successful_handle",
			message: func() []byte {
				order := &dao.Order{
					Id:     12345,
					Status: int64(domain.OrderStateNeedCancel),
				}
				data, _ := json.Marshal(order)
				return data
			}(),
			mockOrder: &dao.Order{
				Id:     12345,
				Status: int64(domain.OrderStateNeedCancel),
			},
			expectedError: false,
		},
		{
			name:          "invalid_json",
			message:       []byte("invalid json"),
			expectedError: true,
		},
		{
			name: "wrong_status",
			message: func() []byte {
				order := &dao.Order{
					Id:     12345,
					Status: int64(domain.OrderStatePaid),
				}
				data, _ := json.Marshal(order)
				return data
			}(),
			expectedError: false, // Should not error, just skip
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockOrderDao := &MockOrderDao{}
			mockAuditor := &MockCancelAuditor{}

			service := &TradeService{
				orderDao: &dao.OrderDao{
					Order: mockOrderDao,
				},
				cancelAuditor: mockAuditor,
			}

			ctx := context.Background()

			// Setup expectations for successful cases
			if !tt.expectedError && tt.mockOrder != nil && tt.mockOrder.Status == int64(domain.OrderStateNeedCancel) {
				mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(nil)
				mockAuditor.On("RecordCancelSuccess", ctx, mock.AnythingOfType("*dao.Order"), domain.OrderStateNeedCancel, false, mock.AnythingOfType("int64")).Return()
			}

			// Execute
			err := service.handleCancelMessage(ctx, tt.message)

			// Assert
			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify mocks
			mockOrderDao.AssertExpectations(t)
			mockAuditor.AssertExpectations(t)
		})
	}
}

// TestCancelAuditor tests the cancel auditor functionality
func TestCancelAuditor(t *testing.T) {
	auditor := NewCancelAuditor(nil)
	ctx := context.Background()

	// Test RecordCancelRequest
	auditor.RecordCancelRequest(ctx, 12345, 1, "test reason")
	assert.Equal(t, int64(1), auditor.GetMetrics().TotalCancelRequests)

	// Test RecordCancelSuccess
	order := &dao.Order{Id: 12345}
	auditor.RecordCancelSuccess(ctx, order, domain.OrderStatePaid, true, 100)
	assert.Equal(t, int64(1), auditor.GetMetrics().SuccessfulCancellations)

	// Test RecordCancelFailure
	auditor.RecordCancelFailure(ctx, 12345, domain.OrderStatePaid, "supplier", "test error", 200)
	assert.Equal(t, int64(1), auditor.GetMetrics().FailedCancellations)
	assert.Equal(t, int64(1), auditor.GetMetrics().SupplierCancelFailures)

	// Test metrics
	metrics := auditor.GetMetrics()
	assert.Equal(t, int64(1), metrics.TotalCancelRequests)
	assert.Equal(t, int64(1), metrics.SuccessfulCancellations)
	assert.Equal(t, int64(1), metrics.FailedCancellations)
}
