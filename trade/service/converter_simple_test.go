package service

import (
	"testing"
	"time"

	"hotel/common/types"
	"hotel/trade/dao"
	tradeDomain "hotel/trade/domain"
)

// 简单测试转换函数，不依赖外部包
func TestSimpleConvertDAOOrderToDomain(t *testing.T) {
	// 测试数据
	daoOrder := &dao.Order{
		Id:          123,
		ReferenceNo: "TEST001",
		Status:      1, // OrderStateCreated
		CreateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
	}

	// 执行转换
	result := convertDAOOrderToDomain(daoOrder)

	// 验证结果
	if result.Id != types.ID(123) {
		t.<PERSON><PERSON><PERSON>("Expected Id to be 123, got %v", result.Id)
	}

	if result.ReferenceNo != "TEST001" {
		t.<PERSON><PERSON>rf("Expected ReferenceNo to be TEST001, got %s", result.ReferenceNo)
	}

	if result.Status != tradeDomain.OrderStateCreated {
		t.<PERSON><PERSON><PERSON>("Expected Status to be OrderStateCreated, got %v", result.Status)
	}

	expectedTime := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
	if !result.CreateTime.Equal(expectedTime) {
		t.<PERSON><PERSON><PERSON>("Expected CreateTime to be %v, got %v", expectedTime, result.CreateTime)
	}
}

func TestSimpleConvertDAOOrdersToDomain(t *testing.T) {
	// 测试数据
	daoOrders := []*dao.Order{
		{
			Id:          123,
			ReferenceNo: "TEST001",
			Status:      1, // OrderStateCreated
			CreateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
		},
		{
			Id:          456,
			ReferenceNo: "TEST002",
			Status:      4, // OrderStateConfirmed
			CreateTime:  time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
		},
	}

	// 执行转换
	result := convertDAOOrdersToDomain(daoOrders)

	// 验证结果
	if len(result) != 2 {
		t.Errorf("Expected 2 orders, got %d", len(result))
	}

	// 验证第一个订单
	if result[0].Id != types.ID(123) {
		t.Errorf("Expected first order Id to be 123, got %v", result[0].Id)
	}

	if result[0].ReferenceNo != "TEST001" {
		t.Errorf("Expected first order ReferenceNo to be TEST001, got %s", result[0].ReferenceNo)
	}

	// 验证第二个订单
	if result[1].Id != types.ID(456) {
		t.Errorf("Expected second order Id to be 456, got %v", result[1].Id)
	}

	if result[1].ReferenceNo != "TEST002" {
		t.Errorf("Expected second order ReferenceNo to be TEST002, got %s", result[1].ReferenceNo)
	}
}

// 测试状态映射
func TestStatusMapping(t *testing.T) {
	testCases := []struct {
		daoStatus      int64
		expectedStatus tradeDomain.OrderStatus
	}{
		{1, tradeDomain.OrderStateCreated},
		{2, tradeDomain.OrderStatePaid},
		{3, tradeDomain.OrderStateNeedSupplierConfirmed},
		{4, tradeDomain.OrderStateConfirmed},
		{5, tradeDomain.OrderStateCompleted},
		{6, tradeDomain.OrderStateCancelled},
		{7, tradeDomain.OrderStateNeedCancel},
		{8, tradeDomain.OrderStateNeedRefund},
		{999, tradeDomain.OrderStatus(0)}, // 未知状态应该返回0
	}

	for _, tc := range testCases {
		daoOrder := &dao.Order{
			Id:          1,
			ReferenceNo: "TEST",
			Status:      tc.daoStatus,
			CreateTime:  time.Now(),
		}

		result := convertDAOOrderToDomain(daoOrder)

		if result.Status != tc.expectedStatus {
			t.Errorf("For DAO status %d, expected %d, got %d",
				tc.daoStatus, int64(tc.expectedStatus), int64(result.Status))
		}
	}
}
