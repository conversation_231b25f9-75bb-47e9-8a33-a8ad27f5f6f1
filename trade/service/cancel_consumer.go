package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	supplierDomain "hotel/supplier/domain"
	"hotel/trade/domain"

	"hotel/common/cqrs"
	cqrsTypes "hotel/common/cqrs/types"
	"hotel/common/log"
	"hotel/common/money"
)

func (s *TradeService) startConsumerCancelMessage() error {
	cancelCfg := &cqrsTypes.ConsumerConfig{
		TopicName:    "cancel_book",
		ChannelName:  "cancel_book_channel",
		ConsumerName: "cancel_consumer_1",
	}

	// 创建消息处理器，适配新的接口
	handler := func(ctx context.Context, message []byte) error {
		return s.handleCancelMessage(ctx, message)
	}

	consumer, err := cqrs.NewConsumer(s.cqrsConfig, cancelCfg, handler)
	if err != nil {
		log.Error("Failed to create cancel consumer: %+v", err)
		return err
	}

	err = consumer.Start()
	if err != nil {
		log.Error("Failed to start cancel consumer: %v", err)
		return err
	}

	log.Info("Cancel Consumer started successfully. Waiting for messages...")
	return nil
}

func (s *TradeService) handleCancelMessage(ctx context.Context, msg []byte) error {
	// 解析订单消息
	order := &domain.Order{}
	if err := json.Unmarshal(msg, order); err != nil {
		log.Errorc(ctx, "handleCancel: failed to unmarshal order message: %+v", err)
		return err
	}

	log.Infoc(ctx, "Processing cancel message for order: %d", order.Id)

	status := domain.OrderStatus(order.Status)
	if status != domain.OrderStateNeedCancel {
		log.Warnc(ctx, "Order %d is not in NeedCancel state, current status: %s", order.Id, status.String())
		return nil
	}

	// 创建状态机进行状态验证
	sm, err := domain.NewOrderStateMachine(status)
	if err != nil {
		log.Errorc(ctx, "Failed to create state machine for order %d: %v", order.Id, err)
		return err
	}

	// 检查是否可以转换到取消状态
	if !sm.CanTransitionTo(domain.OrderStateCancelled) {
		log.Errorc(ctx, "Order %d cannot transition from %s to Cancelled", order.Id, status.String())
		return fmt.Errorf("invalid state transition for order %d", order.Id)
	}

	// 执行取消操作，包含事务一致性保证
	if err := s.executeCancelWithTransaction(ctx, order, sm); err != nil {
		log.Errorc(ctx, "Failed to execute cancel transaction for order %d: %v", order.Id, err)
		return err
	}

	log.Infoc(ctx, "Order %d cancelled successfully", order.Id)
	return nil
}

// executeCancelWithTransaction 在事务中执行取消操作
func (s *TradeService) executeCancelWithTransaction(ctx context.Context, order *domain.Order, sm *domain.OrderStateMachine) error {
	// 记录原始状态用于回滚
	originalStatus := order.Status

	// 第一步：调用供应商取消接口
	supplierCancelSuccess := false
	var refundAmount money.Money
	if err := s.cancelSupplierOrder(ctx, order); err != nil {
		log.Errorc(ctx, "Failed to cancel supplier order for order %d: %v", order.Id, err)
		// 供应商取消失败，根据业务规则决定是否继续
		// 这里我们记录失败但继续处理本地状态
	} else {
		supplierCancelSuccess = true
		// 计算退款金额（这里需要根据实际业务逻辑计算）
		refundAmount = s.calculateRefundAmount(order)
	}

	// 第二步：使用状态机进行状态转换
	if err := sm.TransitionToWithReason(domain.OrderStateCancelled, "Order cancelled via message queue"); err != nil {
		log.Errorc(ctx, "Failed to transition order %d state: %v", order.Id, err)
		// 如果供应商取消成功但状态转换失败，需要考虑补偿操作
		if supplierCancelSuccess {
			log.Warnc(ctx, "Supplier cancellation succeeded but state transition failed for order %d", order.Id)
		}
		return err
	}

	// 第三步：更新订单状态到数据库
	order.Status = domain.OrderStateCancelled
	if err := s.orderDao.UpdateStatus(ctx, order.Id, order.Status); err != nil {
		log.Errorc(ctx, "UpdateOrderFailed: order=%+v, error=%+v", order, err)

		// 数据库更新失败，尝试回滚状态机
		if rollbackErr := sm.TransitionToWithReason(originalStatus, "Rollback due to database update failure"); rollbackErr != nil {
			log.Errorc(ctx, "Failed to rollback state machine for order %d: %v", order.Id, rollbackErr)
		}

		return fmt.Errorf("failed to update order status in database: %w", err)
	}

	// 第四步：创建退款订单（如果供应商取消成功）
	if supplierCancelSuccess && refundAmount.Amount > 0 {
		if err := s.createRefundOrder(ctx, order, refundAmount); err != nil {
			log.Errorc(ctx, "Failed to create refund order for order %d: %v", order.Id, err)
			// 退款订单创建失败不影响主流程，但需要记录
		}
	}

	// 第五步：记录审计日志
	if err := s.recordCancelAuditLog(ctx, order, supplierCancelSuccess); err != nil {
		log.Errorc(ctx, "Failed to record audit log for order %d: %v", order.Id, err)
		// 审计日志失败不影响主流程，但需要记录
	}

	return nil
}

// createRefundOrder 创建退款订单
func (s *TradeService) createRefundOrder(ctx context.Context, order *domain.Order, refundAmount money.Money) error {
	// 保存到数据库
	err := s.orderDao.RefundOrder().Insert(ctx, &domain.RefundOrder{
		OrderID:    order.Id,
		Status:     domain.RefundOrderStatusCreated,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	})
	if err != nil {
		return fmt.Errorf("failed to create refund order: %w", err)
	}

	log.Infoc(ctx, "Created refund order for order %s, amount: %f", order.Id, refundAmount.Amount)
	return nil
}

// calculateRefundAmount 计算退款金额
func (s *TradeService) calculateRefundAmount(order *domain.Order) money.Money {
	// 这里需要根据实际的订单结构计算退款金额
	// 暂时返回订单总金额的80%作为退款金额
	// 实际实现需要根据订单的 Rooms 等信息计算
	return money.NewMoney("CNY", 0)
}

// recordCancelAuditLog 记录取消操作的审计日志
func (s *TradeService) recordCancelAuditLog(ctx context.Context, order *domain.Order, supplierCancelSuccess bool) error {
	// 使用专门的审计器记录
	originalStatus := domain.OrderStateNeedCancel // 在这个阶段，原始状态是NeedCancel
	processingTime := int64(100)                  // todo：这里应该计算实际的处理时间

	s.cancelAuditor.RecordCancelSuccess(ctx, order, originalStatus, supplierCancelSuccess, processingTime)

	return nil
}

// cancelSupplierOrder 调用供应商接口取消订单
func (s *TradeService) cancelSupplierOrder(ctx context.Context, order *domain.Order) error {
	// 查询该订单的所有供应商订单
	supplierOrders, err := s.orderDao.SupplierOrder().FindByOrderId(ctx, order.Id)
	if err != nil {
		log.Errorc(ctx, "Failed to find supplier orders for order %d: %v", order.Id, err)
		return err
	}

	if len(supplierOrders) == 0 {
		log.Infoc(ctx, "Order %d has no supplier orders, skipping supplier cancellation", order.Id)
		return nil
	}

	// 遍历所有供应商订单进行取消
	for _, supplierOrder := range supplierOrders {
		if err := s.cancelSingleSupplierOrder(ctx, supplierOrder); err != nil {
			log.Errorc(ctx, "Failed to cancel supplier order %s: %v", supplierOrder.SupplierOrderID, err)
			// 继续处理其他供应商订单，不因为一个失败而停止
		}
	}

	return nil
}

// cancelSingleSupplierOrder 取消单个供应商订单
func (s *TradeService) cancelSingleSupplierOrder(ctx context.Context, supplierOrder *domain.SupplierOrder) error {
	// 将字符串转换为供应商类型，这里需要根据实际的映射逻辑
	// 暂时使用默认值，实际应该有一个映射函数
	supplierType := supplierDomain.Supplier_Trip // 使用Trip(携程)作为默认供应商类型
	// 获取供应商实例
	supplierInstance := s.supplier.GetSupplier(supplierType)
	if supplierInstance == nil {
		return fmt.Errorf("supplier instance not found for type %s", supplierType)
	}

	// 构建取消请求
	cancelReq := &supplierDomain.CancelReq{
		SupplierOrderId: supplierOrder.SupplierOrderID,
		CancelType:      supplierDomain.CancelTypeAll,
		CancelReason:    "Customer requested cancellation",
	}

	// 调用供应商取消接口
	cancelResp, err := supplierInstance.Cancel(ctx, cancelReq)
	if err != nil {
		return fmt.Errorf("supplier cancellation failed: %w", err)
	}

	log.Infoc(ctx, "Supplier order %s cancelled successfully, status: %v",
		supplierOrder.SupplierOrderID, cancelResp.Status)

	return nil
}
