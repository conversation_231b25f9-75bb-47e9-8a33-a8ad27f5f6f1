package service

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"

	"hotel/common/cqrs"
	supplierDomain "hotel/supplier/domain"
	"hotel/trade/dao"
	"hotel/trade/domain"
	"hotel/trade/protocol"
)

// MockSupplier for testing supplier integration
type MockSupplier struct {
	mock.Mock
}

func (m *MockSupplier) Cancel(ctx context.Context, req *supplierDomain.CancelReq) (*supplierDomain.CancelResp, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*supplierDomain.CancelResp), args.Error(1)
}

func (m *MockSupplier) Book(ctx context.Context, req *supplierDomain.BookReq) (*supplierDomain.BookResp, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*supplierDomain.BookResp), args.Error(1)
}

func (m *MockSupplier) QueryOrderByIDs(ctx context.Context, req *supplierDomain.QueryOrdersReq) (*supplierDomain.QueryOrdersResp, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(*supplierDomain.QueryOrdersResp), args.Error(1)
}

func (m *MockSupplier) Supplier() supplierDomain.Supplier {
	return supplierDomain.Supplier_Simulator
}

// MockSupplierFactory for testing
type MockSupplierFactory struct {
	mock.Mock
	suppliers map[supplierDomain.Supplier]*MockSupplier
}

func NewMockSupplierFactory() *MockSupplierFactory {
	return &MockSupplierFactory{
		suppliers: make(map[supplierDomain.Supplier]*MockSupplier),
	}
}

func (f *MockSupplierFactory) GetSupplier(supplierType supplierDomain.Supplier) *MockSupplier {
	if supplier, exists := f.suppliers[supplierType]; exists {
		return supplier
	}
	supplier := &MockSupplier{}
	f.suppliers[supplierType] = supplier
	return supplier
}

// MockSupplierOrderDao for testing
type MockSupplierOrderDao struct {
	mock.Mock
}

func (m *MockSupplierOrderDao) FindByOrderId(ctx context.Context, orderId int64) ([]*dao.SupplierOrder, error) {
	args := m.Called(ctx, orderId)
	return args.Get(0).([]*dao.SupplierOrder), args.Error(1)
}

// CancelIntegrationTestSuite contains integration tests for cancel functionality
type CancelIntegrationTestSuite struct {
	suite.Suite
	service           *TradeService
	mockOrderDao      *MockOrderDao
	mockSupplierDao   *MockSupplierOrderDao
	mockProducer      *MockCQRSProducer
	mockAuditor       *MockCancelAuditor
	mockSupplierFactory *MockSupplierFactory
}

func (suite *CancelIntegrationTestSuite) SetupTest() {
	suite.mockOrderDao = &MockOrderDao{}
	suite.mockSupplierDao = &MockSupplierOrderDao{}
	suite.mockProducer = &MockCQRSProducer{}
	suite.mockAuditor = &MockCancelAuditor{}
	suite.mockSupplierFactory = NewMockSupplierFactory()

	suite.service = &TradeService{
		orderDao: &dao.OrderDao{
			Order:         suite.mockOrderDao,
			SupplierOrder: suite.mockSupplierDao,
		},
		supplier:      suite.mockSupplierFactory,
		cqrsProducer:  suite.mockProducer,
		cancelAuditor: suite.mockAuditor,
	}
}

func (suite *CancelIntegrationTestSuite) TestCancelOrderWithSupplierIntegration() {
	ctx := context.Background()
	orderID := uint64(12345)
	supplierOrderID := "SUP123"

	// Setup test data
	order := &dao.Order{
		Id:     orderID,
		Status: int64(domain.OrderStatePaid),
	}

	supplierOrders := []*dao.SupplierOrder{
		{
			Id:              1,
			OrderId:         int64(orderID),
			SupplierId:      int64(supplierDomain.Supplier_Simulator),
			SupplierOrderId: supplierOrderID,
			Status:          int64(supplierDomain.OrderStatus_Confirmed),
		},
	}

	// Setup mocks
	suite.mockAuditor.On("RecordCancelRequest", ctx, orderID, uint64(0), "User requested cancellation").Return()
	suite.mockOrderDao.On("FindOne", ctx, orderID).Return(order, nil)
	suite.mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(nil)
	suite.mockProducer.On("Publish", ctx, CancelTopic, mock.AnythingOfType("*dao.Order")).Return(nil)
	suite.mockAuditor.On("RecordCancelSuccess", ctx, mock.AnythingOfType("*dao.Order"), domain.OrderStatePaid, true, mock.AnythingOfType("int64")).Return()

	// Execute cancel request
	req := &protocol.CancelReq{OrderId: "12345"}
	err := suite.service.Cancel(ctx, req)

	// Verify
	suite.NoError(err)
	suite.mockOrderDao.AssertExpectations(suite.T())
	suite.mockProducer.AssertExpectations(suite.T())
	suite.mockAuditor.AssertExpectations(suite.T())

	// Verify order status was updated to NeedCancel
	updateCalls := suite.mockOrderDao.Calls
	var updatedOrder *dao.Order
	for _, call := range updateCalls {
		if call.Method == "Update" {
			updatedOrder = call.Arguments[1].(*dao.Order)
			break
		}
	}
	suite.NotNil(updatedOrder)
	suite.Equal(int64(domain.OrderStateNeedCancel), updatedOrder.Status)
}

func (suite *CancelIntegrationTestSuite) TestHandleCancelMessageWithSupplierCall() {
	ctx := context.Background()
	orderID := uint64(12345)
	supplierOrderID := "SUP123"

	// Setup test data
	order := &dao.Order{
		Id:     orderID,
		Status: int64(domain.OrderStateNeedCancel),
	}

	supplierOrders := []*dao.SupplierOrder{
		{
			Id:              1,
			OrderId:         int64(orderID),
			SupplierId:      int64(supplierDomain.Supplier_Simulator),
			SupplierOrderId: supplierOrderID,
			Status:          int64(supplierDomain.OrderStatus_Confirmed),
		},
	}

	// Setup supplier mock
	mockSupplier := suite.mockSupplierFactory.GetSupplier(supplierDomain.Supplier_Simulator)
	cancelResp := &supplierDomain.CancelResp{
		Status: supplierDomain.OrderStatus_Cancelled,
	}
	mockSupplier.On("Cancel", ctx, mock.MatchedBy(func(req *supplierDomain.CancelReq) bool {
		return req.SupplierOrderId == supplierOrderID &&
			req.CancelType == supplierDomain.CancelTypeAll &&
			req.CancelReason == "Customer requested cancellation"
	})).Return(cancelResp, nil)

	// Setup other mocks
	suite.mockSupplierDao.On("FindByOrderId", ctx, int64(orderID)).Return(supplierOrders, nil)
	suite.mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(nil)
	suite.mockAuditor.On("RecordCancelSuccess", ctx, mock.AnythingOfType("*dao.Order"), domain.OrderStateNeedCancel, false, mock.AnythingOfType("int64")).Return()

	// Prepare message
	message, err := json.Marshal(order)
	suite.NoError(err)

	// Execute
	err = suite.service.handleCancelMessage(ctx, message)

	// Verify
	suite.NoError(err)
	mockSupplier.AssertExpectations(suite.T())
	suite.mockSupplierDao.AssertExpectations(suite.T())
	suite.mockOrderDao.AssertExpectations(suite.T())
	suite.mockAuditor.AssertExpectations(suite.T())

	// Verify order status was updated to Cancelled
	updateCalls := suite.mockOrderDao.Calls
	var updatedOrder *dao.Order
	for _, call := range updateCalls {
		if call.Method == "Update" {
			updatedOrder = call.Arguments[1].(*dao.Order)
			break
		}
	}
	suite.NotNil(updatedOrder)
	suite.Equal(int64(domain.OrderStateCancelled), updatedOrder.Status)
}

func (suite *CancelIntegrationTestSuite) TestSupplierCancelFailureHandling() {
	ctx := context.Background()
	orderID := uint64(12345)
	supplierOrderID := "SUP123"

	// Setup test data
	order := &dao.Order{
		Id:     orderID,
		Status: int64(domain.OrderStateNeedCancel),
	}

	supplierOrders := []*dao.SupplierOrder{
		{
			Id:              1,
			OrderId:         int64(orderID),
			SupplierId:      int64(supplierDomain.Supplier_Simulator),
			SupplierOrderId: supplierOrderID,
			Status:          int64(supplierDomain.OrderStatus_Confirmed),
		},
	}

	// Setup supplier mock to fail
	mockSupplier := suite.mockSupplierFactory.GetSupplier(supplierDomain.Supplier_Simulator)
	mockSupplier.On("Cancel", ctx, mock.AnythingOfType("*supplierDomain.CancelReq")).Return((*supplierDomain.CancelResp)(nil), assert.AnError)

	// Setup other mocks
	suite.mockSupplierDao.On("FindByOrderId", ctx, int64(orderID)).Return(supplierOrders, nil)
	suite.mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(nil)
	suite.mockAuditor.On("RecordCancelSuccess", ctx, mock.AnythingOfType("*dao.Order"), domain.OrderStateNeedCancel, false, mock.AnythingOfType("int64")).Return()

	// Prepare message
	message, err := json.Marshal(order)
	suite.NoError(err)

	// Execute
	err = suite.service.handleCancelMessage(ctx, message)

	// Verify - should still succeed even if supplier cancel fails
	suite.NoError(err)
	mockSupplier.AssertExpectations(suite.T())
	suite.mockSupplierDao.AssertExpectations(suite.T())
	suite.mockOrderDao.AssertExpectations(suite.T())
	suite.mockAuditor.AssertExpectations(suite.T())
}

func (suite *CancelIntegrationTestSuite) TestMultipleSupplierOrdersCancellation() {
	ctx := context.Background()
	orderID := uint64(12345)

	// Setup test data with multiple supplier orders
	order := &dao.Order{
		Id:     orderID,
		Status: int64(domain.OrderStateNeedCancel),
	}

	supplierOrders := []*dao.SupplierOrder{
		{
			Id:              1,
			OrderId:         int64(orderID),
			SupplierId:      int64(supplierDomain.Supplier_Simulator),
			SupplierOrderId: "SUP123",
			Status:          int64(supplierDomain.OrderStatus_Confirmed),
		},
		{
			Id:              2,
			OrderId:         int64(orderID),
			SupplierId:      int64(supplierDomain.Supplier_Dida),
			SupplierOrderId: "DIDA456",
			Status:          int64(supplierDomain.OrderStatus_Confirmed),
		},
	}

	// Setup supplier mocks
	mockSimulator := suite.mockSupplierFactory.GetSupplier(supplierDomain.Supplier_Simulator)
	mockDida := suite.mockSupplierFactory.GetSupplier(supplierDomain.Supplier_Dida)

	cancelResp := &supplierDomain.CancelResp{Status: supplierDomain.OrderStatus_Cancelled}
	mockSimulator.On("Cancel", ctx, mock.AnythingOfType("*supplierDomain.CancelReq")).Return(cancelResp, nil)
	mockDida.On("Cancel", ctx, mock.AnythingOfType("*supplierDomain.CancelReq")).Return(cancelResp, nil)

	// Setup other mocks
	suite.mockSupplierDao.On("FindByOrderId", ctx, int64(orderID)).Return(supplierOrders, nil)
	suite.mockOrderDao.On("Update", ctx, mock.AnythingOfType("*dao.Order")).Return(nil)
	suite.mockAuditor.On("RecordCancelSuccess", ctx, mock.AnythingOfType("*dao.Order"), domain.OrderStateNeedCancel, false, mock.AnythingOfType("int64")).Return()

	// Prepare message
	message, err := json.Marshal(order)
	suite.NoError(err)

	// Execute
	err = suite.service.handleCancelMessage(ctx, message)

	// Verify
	suite.NoError(err)
	mockSimulator.AssertExpectations(suite.T())
	mockDida.AssertExpectations(suite.T())
	suite.mockSupplierDao.AssertExpectations(suite.T())
	suite.mockOrderDao.AssertExpectations(suite.T())
	suite.mockAuditor.AssertExpectations(suite.T())
}

func (suite *CancelIntegrationTestSuite) TestCancelAuditorIntegration() {
	auditor := NewCancelAuditor(suite.service)
	ctx := context.Background()

	// Test complete audit flow
	orderID := uint64(12345)
	userID := uint64(1)
	order := &dao.Order{Id: orderID}

	// Record cancel request
	auditor.RecordCancelRequest(ctx, orderID, userID, "test reason")

	// Record success
	auditor.RecordCancelSuccess(ctx, order, domain.OrderStatePaid, true, 150)

	// Record failure
	auditor.RecordCancelFailure(ctx, orderID, domain.OrderStatePaid, "supplier", "test error", 200)

	// Verify metrics
	metrics := auditor.GetMetrics()
	suite.Equal(int64(1), metrics.TotalCancelRequests)
	suite.Equal(int64(1), metrics.SuccessfulCancellations)
	suite.Equal(int64(1), metrics.FailedCancellations)
	suite.Equal(int64(1), metrics.SupplierCancelFailures)

	// Test monitoring
	auditor.MonitorCancelOperations(ctx)
}

// Run the integration test suite
func TestCancelIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(CancelIntegrationTestSuite))
}
