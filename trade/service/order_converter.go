package service

import (
	"hotel/common/types"
	"hotel/trade/dao"
	"hotel/trade/protocol"
)

// convertProtocolCriteriaToDAO 将 protocol 的查询条件转换为 DAO 的查询条件
func convertProtocolCriteriaToDAO(criteria protocol.QueryOrderCriteria) *dao.OrderQueryCriteria {
	daoCriteria := &dao.OrderQueryCriteria{}

	// 转换平台订单ID
	if len(criteria.PlatformOrderIds) > 0 {
		for _, id := range criteria.PlatformOrderIds {
			daoCriteria.PlatformOrderIds = append(daoCriteria.PlatformOrderIds, uint64(id))
		}
	}

	// 转换外部订单号
	daoCriteria.ReferenceNos = criteria.ReferenceNos

	// 转换时间窗口 - 检查是否为零值
	if !criteria.CheckInTimeWindow.Start.IsZero() || !criteria.CheckInTimeWindow.End.IsZero() {
		daoCriteria.CheckInTimeWindow = &types.TimeWindow{}
		if !criteria.CheckInTimeWindow.Start.IsZero() {
			daoCriteria.CheckInTimeWindow.Start = criteria.CheckInTimeWindow.Start
		}
		if !criteria.CheckInTimeWindow.End.IsZero() {
			daoCriteria.CheckInTimeWindow.End = criteria.CheckInTimeWindow.End
		}
	}

	if !criteria.CheckOutTimeWindow.Start.IsZero() || !criteria.CheckOutTimeWindow.End.IsZero() {
		daoCriteria.CheckOutTimeWindow = &types.TimeWindow{}
		if !criteria.CheckOutTimeWindow.Start.IsZero() {
			daoCriteria.CheckOutTimeWindow.Start = criteria.CheckOutTimeWindow.Start
		}
		if !criteria.CheckOutTimeWindow.End.IsZero() {
			daoCriteria.CheckOutTimeWindow.End = criteria.CheckOutTimeWindow.End
		}
	}

	if !criteria.CreateTimeWindow.Start.IsZero() || !criteria.CreateTimeWindow.End.IsZero() {
		daoCriteria.CreateTimeWindow = &types.TimeWindow{}
		if !criteria.CreateTimeWindow.Start.IsZero() {
			daoCriteria.CreateTimeWindow.Start = criteria.CreateTimeWindow.Start
		}
		if !criteria.CreateTimeWindow.End.IsZero() {
			daoCriteria.CreateTimeWindow.End = criteria.CreateTimeWindow.End
		}
	}

	if !criteria.CancelTimeWindow.Start.IsZero() || !criteria.CancelTimeWindow.End.IsZero() {
		daoCriteria.CancelTimeWindow = &types.TimeWindow{}
		if !criteria.CancelTimeWindow.Start.IsZero() {
			daoCriteria.CancelTimeWindow.Start = criteria.CancelTimeWindow.Start
		}
		if !criteria.CancelTimeWindow.End.IsZero() {
			daoCriteria.CancelTimeWindow.End = criteria.CancelTimeWindow.End
		}
	}

	// 转换状态列表
	if len(criteria.StatusList) > 0 {
		for _, status := range criteria.StatusList {
			daoCriteria.StatusList = append(daoCriteria.StatusList, int64(status))
		}
	}

	// 转换标签 (暂时忽略，因为数据库表中可能没有标签字段)
	daoCriteria.Tags = criteria.Tags

	return daoCriteria
}

// 注意：转换函数已移至 dao 层，service 层不再直接处理 model 到 domain 的转换

// 注意：所有 DAO 到 Domain 的转换函数已移至 dao 层
// service 层不再直接处理 model 到 domain 的转换
