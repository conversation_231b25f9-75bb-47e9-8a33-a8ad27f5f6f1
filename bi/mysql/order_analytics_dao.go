package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

// OrderAnalyticsDAO 订单分析数据访问对象 - 基于BIOrder表
type OrderAnalyticsDAO struct {
	conn       sqlx.SqlConn
	table      string
	biOrderDAO *BIOrderDAO
}

// NewOrderAnalyticsDAO 创建订单分析DAO
func NewOrderAnalyticsDAO(conn sqlx.SqlConn) *OrderAnalyticsDAO {
	return &OrderAnalyticsDAO{
		conn:       conn,
		table:      "`bi_order`", // 使用bi_order表
		biOrderDAO: NewBIOrderDAO(conn),
	}
}

// GetBIOrderDAO 获取BIOrder DAO
func (dao *OrderAnalyticsDAO) GetBIOrderDAO() *BIOrderDAO {
	return dao.biOrderDAO
}

// ====================================================================
// 数据结构定义
// ====================================================================

// OrderMetrics 订单指标结构
type OrderMetrics struct {
	Date            time.Time `db:"date"`
	TotalOrders     int64     `db:"total_orders"`
	TotalRevenue    int64     `db:"total_revenue"`
	TotalCost       int64     `db:"total_cost"`
	TotalProfit     int64     `db:"total_profit"`
	CompletedOrders int64     `db:"completed_orders"`
	CancelledOrders int64     `db:"cancelled_orders"`
	PendingOrders   int64     `db:"pending_orders"`
	RevenueCurrency string    `db:"revenue_currency"`
}

// StatusMetrics 状态指标
type StatusMetrics struct {
	Status   int64  `db:"status"`
	Count    int64  `db:"count"`
	Revenue  int64  `db:"revenue"`
	Currency string `db:"currency"`
}

// EntityMetrics 实体指标
type EntityMetrics struct {
	EntityId   int64  `db:"entity_id"`
	EntityName string `db:"entity_name"`
	OrderCount int64  `db:"order_count"`
	Revenue    int64  `db:"revenue"`
	Profit     int64  `db:"profit"`
	Currency   string `db:"currency"`
}

// TaggedOrderMetrics SmartBook标签订单指标
type TaggedOrderMetrics struct {
	ProfitGenCount       int64   `db:"profit_gen_count"`
	ErrorRecoveryCount   int64   `db:"error_recovery_count"`
	TotalCount           int64   `db:"total_count"`
	TotalProfitAmount    float64 `db:"total_profit_amount"`
	TotalProcessedAmount float64 `db:"total_processed_amount"`
}

// RebookMetrics 重预订指标
type RebookMetrics struct {
	RebookCount       int64   `db:"rebook_count"`
	TotalProfitAmount float64 `db:"total_profit_amount"`
	TotalRebookAmount float64 `db:"total_rebook_amount"`
}

// SupplierTransfer 供应商转换记录
type SupplierTransfer struct {
	OldSupplier      string  `db:"old_supplier"`
	NewSupplier      string  `db:"new_supplier"`
	TransferCount    int64   `db:"transfer_count"`
	ProfitDifference float64 `db:"profit_difference"`
}

// ClientConfigMetrics 客户配置指标
type ClientConfigMetrics struct {
	RoomMappingVersion    string    `json:"room_mapping_version"`
	SmartBookModes        []string  `json:"smart_book_modes"`
	DynamicMarkupsEnabled bool      `json:"dynamic_markups_enabled"`
	BestPackagesEnabled   bool      `json:"best_packages_enabled"`
	TwoFactorAuthEnabled  bool      `json:"two_factor_auth_enabled"`
	LastConfigUpdate      time.Time `json:"last_config_update"`
}

// RequestMetrics 请求指标
type RequestMetrics struct {
	GeoSearchCount     int64 `db:"geo_search_count"`
	HotelSearchCount   int64 `db:"hotel_search_count"`
	KeywordSearchCount int64 `db:"keyword_search_count"`
	ListSearchCount    int64 `db:"list_search_count"`
	DetailSearchCount  int64 `db:"detail_search_count"`
	CheckAvailCount    int64 `db:"check_avail_count"`
	TotalRequests      int64 `db:"total_requests"`
}

// ResponseMetrics 响应指标
type ResponseMetrics struct {
	SuccessCount      int64   `db:"success_count"`
	FailureCount      int64   `db:"failure_count"`
	TotalResponses    int64   `db:"total_responses"`
	AvgDurationMs     float64 `db:"avg_duration_ms"`
	P50DurationMs     float64 `db:"p50_duration_ms"`
	P95DurationMs     float64 `db:"p95_duration_ms"`
	SuccessRate       float64 `db:"success_rate"`
}

// SupplierBookingStat 供应商预订统计
type SupplierBookingStat struct {
	Supplier         string  `db:"supplier"`
	BookingCount     int64   `db:"booking_count"`
	BookingVolume    float64 `db:"booking_volume"`
	AvgBookingAmount float64 `db:"avg_booking_amount"`
	SuccessRate      float64 `db:"success_rate"`
}

// ====================================================================
// 基础查询方法 (使用bi_order表)
// ====================================================================

// GetOrderMetricsByDateRange 获取指定日期范围的订单指标
func (dao *OrderAnalyticsDAO) GetOrderMetricsByDateRange(ctx context.Context, startDate, endDate time.Time, granularity string) ([]*OrderMetrics, error) {
	var dateFormat string
	switch granularity {
	case "day":
		dateFormat = "%Y-%m-%d"
	case "week":
		dateFormat = "%Y-%u"
	case "month":
		dateFormat = "%Y-%m"
	default:
		dateFormat = "%Y-%m-%d"
	}

	query := fmt.Sprintf(`
		SELECT 
			DATE_FORMAT(create_time, '%s') as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			0 as total_cost,
			COALESCE(SUM(profit_gen_amount), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ? AND status >= 0
		GROUP BY DATE_FORMAT(create_time, '%s')
		ORDER BY date ASC`, dateFormat, dao.table, dateFormat)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate)
	return metrics, err
}

// GetOrderOverview 获取订单概览
func (dao *OrderAnalyticsDAO) GetOrderOverview(ctx context.Context, startDate, endDate time.Time, entityId *int64) (*OrderMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ? AND status >= 0"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			0 as total_cost,
			COALESCE(SUM(profit_gen_amount), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s %s`, dao.table, whereClause)

	var overview OrderMetrics
	err := dao.conn.QueryRowCtx(ctx, &overview, query, args...)
	return &overview, err
}

// GetStatusBreakdown 获取状态分布
func (dao *OrderAnalyticsDAO) GetStatusBreakdown(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*StatusMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ?"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			status,
			COUNT(*) as count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			buyer_currency as currency
		FROM %s %s
		GROUP BY status, buyer_currency
		ORDER BY status ASC`, dao.table, whereClause)

	var breakdown []*StatusMetrics
	err := dao.conn.QueryRowsCtx(ctx, &breakdown, query, args...)
	return breakdown, err
}

// ====================================================================
// BIOrder专用分析方法
// ====================================================================

// GetOrdersByTags 根据Tags获取订单统计（SmartBook分析） - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetOrdersByTags(ctx context.Context, startDate, endDate time.Time, entityID *int64) (*TaggedOrderMetrics, error) {
	stats, err := dao.biOrderDAO.GetOrdersByTags(ctx, startDate, endDate, entityID)
	if err != nil {
		return nil, err
	}
	
	return &TaggedOrderMetrics{
		ProfitGenCount:       int64(stats.ProfitGenCount),
		ErrorRecoveryCount:   int64(stats.ErrorRecoveryCount),
		TotalCount:           int64(stats.TotalCount),
		TotalProfitAmount:    stats.TotalProfitGenAmount,
		TotalProcessedAmount: stats.TotalBuyerAmount,
	}, nil
}

// GetRebookMetrics 获取重预订指标 - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetRebookMetrics(ctx context.Context, startDate, endDate time.Time, entityID *int64) (*RebookMetrics, error) {
	metrics, err := dao.biOrderDAO.GetRebookMetrics(ctx, startDate, endDate, entityID)
	if err != nil {
		return nil, err
	}
	
	return &RebookMetrics{
		RebookCount:       int64(metrics.RebookCount),
		TotalProfitAmount: metrics.TotalProfitGenAmount,
		TotalRebookAmount: metrics.TotalRebookAmount,
	}, nil
}

// GetSupplierTransfers 获取供应商转换记录 - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetSupplierTransfers(ctx context.Context, startDate, endDate time.Time, entityID *int64) ([]*SupplierTransfer, error) {
	transfers, err := dao.biOrderDAO.GetSupplierTransfers(ctx, startDate, endDate, entityID)
	if err != nil {
		return nil, err
	}
	
	// 转换为原有的结构体格式
	result := make([]*SupplierTransfer, len(transfers))
	for i, transfer := range transfers {
		result[i] = &SupplierTransfer{
			OldSupplier:      transfer.OldSupplier,
			NewSupplier:      transfer.NewSupplier,
			TransferCount:    int64(transfer.TransferCount),
			ProfitDifference: transfer.TotalProfitDifference,
		}
	}
	
	return result, nil
}

// GetClientConfiguration 获取客户配置 - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetClientConfiguration(ctx context.Context, entityID int64) (*ClientConfigMetrics, error) {
	config, err := dao.biOrderDAO.GetClientConfiguration(ctx, entityID)
	if err != nil {
		return nil, err
	}
	
	return &ClientConfigMetrics{
		RoomMappingVersion:    config.RoomMappingVersion,
		SmartBookModes:        []string(config.SmartBookMode),
		DynamicMarkupsEnabled: config.DynamicMarkupsEnabled,
		BestPackagesEnabled:   config.BestPackagesEnabled,
		TwoFactorAuthEnabled:  config.TwoFactorAuthEnabled,
		LastConfigUpdate:      config.ConfigUpdateTime,
	}, nil
}

// GetRequestMetrics 获取请求指标 - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetRequestMetrics(ctx context.Context, entityID int64, startDate, endDate time.Time) (*RequestMetrics, error) {
	analysis, err := dao.biOrderDAO.GetRequestMetrics(ctx, startDate, endDate, &entityID)
	if err != nil {
		return nil, err
	}
	
	return &RequestMetrics{
		GeoSearchCount:     int64(analysis.GeoSearchCount),
		HotelSearchCount:   int64(analysis.HotelSearchCount),
		KeywordSearchCount: int64(analysis.KeywordSearchCount),
		ListSearchCount:    int64(analysis.ListSearchCount),
		DetailSearchCount:  int64(analysis.DetailSearchCount),
		CheckAvailCount:    int64(analysis.CheckAvailCount),
		TotalRequests:      int64(analysis.TotalRequests),
	}, nil
}

// GetResponseMetrics 获取响应指标 - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetResponseMetrics(ctx context.Context, entityID int64, startDate, endDate time.Time) (*ResponseMetrics, error) {
	analysis, err := dao.biOrderDAO.GetResponseMetrics(ctx, startDate, endDate, &entityID)
	if err != nil {
		return nil, err
	}
	
	return &ResponseMetrics{
		SuccessCount:      int64(analysis.SuccessCount),
		FailureCount:      int64(analysis.FailureCount),
		TotalResponses:    int64(analysis.TotalResponses),
		AvgDurationMs:     analysis.AvgDurationMs,
		P50DurationMs:     analysis.P50DurationMs,
		P95DurationMs:     analysis.P95DurationMs,
		SuccessRate:       analysis.SuccessRate,
	}, nil
}

// GetSupplierBookingStats 获取供应商预订统计 - 使用BIOrderDAO
func (dao *OrderAnalyticsDAO) GetSupplierBookingStats(ctx context.Context, entityID int64, startDate, endDate time.Time) ([]*SupplierBookingStat, error) {
	stats, err := dao.biOrderDAO.GetSupplierBookingStats(ctx, startDate, endDate, &entityID)
	if err != nil {
		return nil, err
	}
	
	// 转换为原有的结构体格式
	result := make([]*SupplierBookingStat, len(stats))
	for i, stat := range stats {
		result[i] = &SupplierBookingStat{
			Supplier:         stat.Supplier,
			BookingCount:     int64(stat.BookingCount),
			BookingVolume:    stat.BookingVolume,
			AvgBookingAmount: stat.AvgBookingAmount,
			SuccessRate:      stat.SuccessRate,
		}
	}
	
	return result, nil
}

// ====================================================================
// 其他分析方法 (继续使用原有实现，基于bi_order表)
// ====================================================================

// GetEntityPerformance 获取实体性能
func (dao *OrderAnalyticsDAO) GetEntityPerformance(ctx context.Context, startDate, endDate time.Time, limit int64) ([]*EntityMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			customer_entity_id as entity_id,
			'Unknown' as entity_name,
			COUNT(*) as order_count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			COALESCE(SUM(profit_gen_amount), 0) as profit,
			buyer_currency as currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ? AND status >= 0
		GROUP BY customer_entity_id, buyer_currency
		ORDER BY revenue DESC
		LIMIT ?`, dao.table)

	var metrics []*EntityMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate, limit)
	return metrics, err
}

// GetRevenueByEntity 获取按实体分组的收入
func (dao *OrderAnalyticsDAO) GetRevenueByEntity(ctx context.Context, startDate, endDate time.Time) ([]*EntityMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			customer_entity_id as entity_id,
			'Unknown' as entity_name,
			COUNT(*) as order_count,
			COALESCE(SUM(buyer_amount), 0) as revenue,
			COALESCE(SUM(profit_gen_amount), 0) as profit,
			buyer_currency as currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ? AND status >= 0
		GROUP BY customer_entity_id, buyer_currency
		ORDER BY revenue DESC`, dao.table)

	var metrics []*EntityMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate)
	return metrics, err
}

// GetDailyTrend 获取每日趋势
func (dao *OrderAnalyticsDAO) GetDailyTrend(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*OrderMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ? AND status >= 0"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			DATE(create_time) as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			0 as total_cost,
			COALESCE(SUM(profit_gen_amount), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s %s
		GROUP BY DATE(create_time)
		ORDER BY date ASC`, dao.table, whereClause)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, args...)
	return metrics, err
}

// GetRealTimeMetrics 获取实时指标
func (dao *OrderAnalyticsDAO) GetRealTimeMetrics(ctx context.Context) (*OrderMetrics, error) {
	today := time.Now().Truncate(24 * time.Hour)
	query := fmt.Sprintf(`
		SELECT 
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			0 as total_cost,
			COALESCE(SUM(profit_gen_amount), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= ? AND status >= 0`, dao.table)

	var metrics OrderMetrics
	err := dao.conn.QueryRowCtx(ctx, &metrics, query, today)
	return &metrics, err
}

// GetRevenueByCurrency 获取按币种分组的收入
func (dao *OrderAnalyticsDAO) GetRevenueByCurrency(ctx context.Context, startDate, endDate time.Time, entityId *int64) ([]*CurrencyMetrics, error) {
	whereClause := "WHERE create_time >= ? AND create_time <= ? AND status >= 0"
	args := []interface{}{startDate, endDate}

	if entityId != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityId)
	}

	query := fmt.Sprintf(`
		SELECT 
			buyer_currency as currency,
			COUNT(*) as count,
			COALESCE(SUM(buyer_amount), 0) as revenue
		FROM %s %s
		GROUP BY buyer_currency
		ORDER BY revenue DESC`, dao.table, whereClause)

	var metrics []*CurrencyMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, args...)
	return metrics, err
}

// GetMonthlyComparison 获取月度对比
func (dao *OrderAnalyticsDAO) GetMonthlyComparison(ctx context.Context, months int) ([]*OrderMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			DATE_FORMAT(create_time, '%%Y-%%m') as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			0 as total_cost,
			COALESCE(SUM(profit_gen_amount), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= DATE_SUB(NOW(), INTERVAL ? MONTH) AND status >= 0
		GROUP BY DATE_FORMAT(create_time, '%%Y-%%m')
		ORDER BY date ASC`, dao.table)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, months)
	return metrics, err
}

// GetTopPerformingDays 获取表现最佳的日期
func (dao *OrderAnalyticsDAO) GetTopPerformingDays(ctx context.Context, startDate, endDate time.Time, limit int64) ([]*OrderMetrics, error) {
	query := fmt.Sprintf(`
		SELECT 
			DATE(create_time) as date,
			COUNT(*) as total_orders,
			COALESCE(SUM(buyer_amount), 0) as total_revenue,
			0 as total_cost,
			COALESCE(SUM(profit_gen_amount), 0) as total_profit,
			SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as completed_orders,
			SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) as cancelled_orders,
			SUM(CASE WHEN status IN (1,2,3,4) THEN 1 ELSE 0 END) as pending_orders,
			'USD' as revenue_currency
		FROM %s 
		WHERE create_time >= ? AND create_time <= ? AND status >= 0
		GROUP BY DATE(create_time)
		ORDER BY total_revenue DESC
		LIMIT ?`, dao.table)

	var metrics []*OrderMetrics
	err := dao.conn.QueryRowsCtx(ctx, &metrics, query, startDate, endDate, limit)
	return metrics, err
}

// CurrencyMetrics 货币指标
type CurrencyMetrics struct {
	Currency string `db:"currency"`
	Count    int64  `db:"count"`
	Revenue  int64  `db:"revenue"`
}