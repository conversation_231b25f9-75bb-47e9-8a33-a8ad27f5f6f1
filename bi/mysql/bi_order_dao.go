package mysql

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/bi/domain"
)

// BIOrderDAO BI订单数据访问对象
type BIOrderDAO struct {
	conn sqlx.SqlConn
}

// NewBIOrderDAO 创建BI订单DAO
func NewBIOrderDAO(conn sqlx.SqlConn) *BIOrderDAO {
	return &BIOrderDAO{conn: conn}
}

// JSONArray JSON数组类型，用于处理tags字段
type JSONArray []string

// Value 实现driver.Valuer接口
func (j JSONArray) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONArray) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONArray", value)
	}
	
	return json.Unmarshal(bytes, j)
}

// BIOrderModel 数据库模型
type BIOrderModel struct {
	ID                 int64     `db:"id"`
	Status             int64     `db:"status"`
	Tags               JSONArray `db:"tags"`
	Supplier           string    `db:"supplier"`
	CreateTime         time.Time `db:"create_time"`
	TenantEntityID     int64     `db:"tenant_entity_id"`
	CustomerEntityID   int64     `db:"customer_entity_id"`
	ProfitGenAmount    float64   `db:"profit_gen_amount"`
	ProfitGenCurrency  string    `db:"profit_gen_currency"`
	BuyerAmount        float64   `db:"buyer_amount"`
	BuyerCurrency      string    `db:"buyer_currency"`
	HotelID            string    `db:"hotel_id"`
	CheckInDate        *time.Time `db:"check_in_date"`
	CheckOutDate       *time.Time `db:"check_out_date"`
	RoomCount          int       `db:"room_count"`
	GuestCount         int       `db:"guest_count"`
	SessionID          string    `db:"session_id"`
	SearchID           string    `db:"search_id"`
	IsRebook           bool      `db:"is_rebook"`
	OriginalOrderID    *int64    `db:"original_order_id"`
	OldSupplier        string    `db:"old_supplier"`
	NewSupplier        string    `db:"new_supplier"`
	UpdateTime         time.Time `db:"update_time"`
}

// ====================================================================
// CRUD 操作
// ====================================================================

// CreateBIOrder 创建BI订单记录
func (dao *BIOrderDAO) CreateBIOrder(ctx context.Context, order *domain.BIOrder) error {
	query := `
		INSERT INTO bi_order (
			id, status, tags, supplier, create_time, tenant_entity_id, customer_entity_id,
			profit_gen_amount, profit_gen_currency, buyer_amount, buyer_currency,
			hotel_id, check_in_date, check_out_date, room_count, guest_count,
			session_id, search_id, is_rebook, original_order_id, old_supplier, new_supplier
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`
	
	tagsJSON, _ := json.Marshal(order.Tags)
	
	_, err := dao.conn.ExecCtx(ctx, query,
		order.ID, order.Status, string(tagsJSON), order.Supplier.GetEnglishName(), 
		order.CreateTime, order.TenantEntityID, order.CustomerEntityID,
		order.ProfitGenAmount, "USD", // 暂时硬编码货币
		0.0, "USD", // BuyerAmount等字段待补充
		"", nil, nil, 1, 1, // HotelID等字段待补充
		"", "", false, nil, "", "",
	)
	
	return err
}

// GetBIOrderByID 根据ID获取BI订单
func (dao *BIOrderDAO) GetBIOrderByID(ctx context.Context, id int64) (*domain.BIOrder, error) {
	query := `
		SELECT id, status, tags, supplier, create_time, tenant_entity_id, customer_entity_id,
			   profit_gen_amount, profit_gen_currency
		FROM bi_order 
		WHERE id = ?
	`
	
	var model BIOrderModel
	err := dao.conn.QueryRowCtx(ctx, &model, query, id)
	if err != nil {
		return nil, err
	}
	
	return dao.modelToDomain(&model), nil
}

// UpdateBIOrder 更新BI订单
func (dao *BIOrderDAO) UpdateBIOrder(ctx context.Context, order *domain.BIOrder) error {
	query := `
		UPDATE bi_order 
		SET status = ?, tags = ?, profit_gen_amount = ?, update_time = CURRENT_TIMESTAMP
		WHERE id = ?
	`
	
	tagsJSON, _ := json.Marshal(order.Tags)
	
	_, err := dao.conn.ExecCtx(ctx, query,
		order.Status, string(tagsJSON), order.ProfitGenAmount, order.ID,
	)
	
	return err
}

// DeleteBIOrder 删除BI订单（软删除，更新状态）
func (dao *BIOrderDAO) DeleteBIOrder(ctx context.Context, id int64) error {
	query := `UPDATE bi_order SET status = -1, update_time = CURRENT_TIMESTAMP WHERE id = ?`
	_, err := dao.conn.ExecCtx(ctx, query, id)
	return err
}

// ====================================================================
// 分析查询方法
// ====================================================================

// GetOrdersByTags 根据Tags获取订单统计（SmartBook分析）
func (dao *BIOrderDAO) GetOrdersByTags(ctx context.Context, startDate, endDate time.Time, entityID *int64) (*TaggedOrderStats, error) {
	whereClause := "WHERE create_time BETWEEN ? AND ?"
	args := []interface{}{startDate, endDate}
	
	if entityID != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityID)
	}
	
	query := fmt.Sprintf(`
		SELECT 
			COUNT(*) as total_count,
			SUM(CASE WHEN JSON_CONTAINS(tags, '"ProfitGen"') THEN 1 ELSE 0 END) as profit_gen_count,
			SUM(CASE WHEN JSON_CONTAINS(tags, '"ErrorRecovery"') THEN 1 ELSE 0 END) as error_recovery_count,
			SUM(profit_gen_amount) as total_profit_gen_amount,
			SUM(buyer_amount) as total_buyer_amount
		FROM bi_order 
		%s AND status >= 0
	`, whereClause)
	
	var stats TaggedOrderStats
	err := dao.conn.QueryRowCtx(ctx, &stats, query, args...)
	return &stats, err
}

// GetRebookMetrics 获取重预订指标
func (dao *BIOrderDAO) GetRebookMetrics(ctx context.Context, startDate, endDate time.Time, entityID *int64) (*RebookMetricsInternal, error) {
	whereClause := "WHERE create_time BETWEEN ? AND ? AND is_rebook = 1"
	args := []interface{}{startDate, endDate}
	
	if entityID != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityID)
	}
	
	query := fmt.Sprintf(`
		SELECT 
			COUNT(*) as rebook_count,
			SUM(profit_gen_amount) as total_profit_gen_amount,
			SUM(buyer_amount) as total_rebook_amount
		FROM bi_order 
		%s AND status >= 0
	`, whereClause)
	
	var metrics RebookMetricsInternal
	err := dao.conn.QueryRowCtx(ctx, &metrics, query, args...)
	return &metrics, err
}

// GetSupplierTransfers 获取供应商转换记录
func (dao *BIOrderDAO) GetSupplierTransfers(ctx context.Context, startDate, endDate time.Time, entityID *int64) ([]*SupplierTransferInternal, error) {
	whereClause := "WHERE transfer_time BETWEEN ? AND ?"
	args := []interface{}{startDate, endDate}
	
	if entityID != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityID)
	}
	
	query := fmt.Sprintf(`
		SELECT old_supplier, new_supplier, COUNT(*) as transfer_count,
			   SUM(profit_difference) as total_profit_difference
		FROM bi_rebook_transfers 
		%s
		GROUP BY old_supplier, new_supplier
		ORDER BY transfer_count DESC
	`, whereClause)
	
	var transfers []*SupplierTransferInternal
	err := dao.conn.QueryRowsCtx(ctx, &transfers, query, args...)
	return transfers, err
}

// GetClientConfiguration 获取客户配置
func (dao *BIOrderDAO) GetClientConfiguration(ctx context.Context, entityID int64) (*ClientConfiguration, error) {
	query := `
		SELECT room_mapping_version, smart_book_mode, dynamic_markups_enabled,
			   best_packages_enabled, two_factor_auth_enabled, config_update_time
		FROM bi_client_config 
		WHERE customer_entity_id = ?
	`
	
	var config ClientConfiguration
	err := dao.conn.QueryRowCtx(ctx, &config, query, entityID)
	return &config, err
}

// GetRequestMetrics 获取请求指标
func (dao *BIOrderDAO) GetRequestMetrics(ctx context.Context, startDate, endDate time.Time, entityID *int64) (*RequestAnalysis, error) {
	whereClause := "WHERE date BETWEEN ? AND ?"
	args := []interface{}{startDate, endDate}
	
	if entityID != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityID)
	}
	
	query := fmt.Sprintf(`
		SELECT 
			SUM(search_type_geo_count) as geo_search_count,
			SUM(search_type_hotel_count) as hotel_search_count,
			SUM(search_type_keyword_count) as keyword_search_count,
			SUM(list_search_count) as list_search_count,
			SUM(detail_search_count) as detail_search_count,
			SUM(check_avail_count) as check_avail_count,
			SUM(total_requests) as total_requests
		FROM bi_request_metrics 
		%s
	`, whereClause)
	
	var analysis RequestAnalysis
	err := dao.conn.QueryRowCtx(ctx, &analysis, query, args...)
	return &analysis, err
}

// GetResponseMetrics 获取响应指标
func (dao *BIOrderDAO) GetResponseMetrics(ctx context.Context, startDate, endDate time.Time, entityID *int64) (*ResponseAnalysis, error) {
	whereClause := "WHERE date BETWEEN ? AND ?"
	args := []interface{}{startDate, endDate}
	
	if entityID != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityID)
	}
	
	query := fmt.Sprintf(`
		SELECT 
			SUM(success_count) as success_count,
			SUM(failure_count) as failure_count,
			SUM(total_responses) as total_responses,
			AVG(avg_duration_ms) as avg_duration_ms,
			AVG(p50_duration_ms) as p50_duration_ms,
			AVG(p95_duration_ms) as p95_duration_ms,
			AVG(success_rate) as success_rate
		FROM bi_response_metrics 
		%s
	`, whereClause)
	
	var analysis ResponseAnalysis
	err := dao.conn.QueryRowCtx(ctx, &analysis, query, args...)
	return &analysis, err
}

// GetSupplierBookingStats 获取供应商预订统计
func (dao *BIOrderDAO) GetSupplierBookingStats(ctx context.Context, startDate, endDate time.Time, entityID *int64) ([]*SupplierBookingStatInternal, error) {
	whereClause := "WHERE date BETWEEN ? AND ?"
	args := []interface{}{startDate, endDate}
	
	if entityID != nil {
		whereClause += " AND customer_entity_id = ?"
		args = append(args, *entityID)
	}
	
	query := fmt.Sprintf(`
		SELECT supplier, SUM(booking_count) as booking_count,
			   SUM(booking_volume) as booking_volume,
			   AVG(avg_booking_amount) as avg_booking_amount,
			   AVG(success_rate) as success_rate
		FROM bi_supplier_booking_stats 
		%s
		GROUP BY supplier
		ORDER BY booking_volume DESC
	`, whereClause)
	
	var stats []*SupplierBookingStatInternal
	err := dao.conn.QueryRowsCtx(ctx, &stats, query, args...)
	return stats, err
}

// BatchInsertBIOrders 批量插入BI订单
func (dao *BIOrderDAO) BatchInsertBIOrders(ctx context.Context, orders []*domain.BIOrder) error {
	if len(orders) == 0 {
		return nil
	}
	
	query := `
		INSERT INTO bi_order (
			id, status, tags, supplier, create_time, tenant_entity_id, customer_entity_id,
			profit_gen_amount, profit_gen_currency
		) VALUES 
	`
	
	values := make([]string, len(orders))
	args := make([]interface{}, 0, len(orders)*9)
	
	for i, order := range orders {
		values[i] = "(?, ?, ?, ?, ?, ?, ?, ?, ?)"
		tagsJSON, _ := json.Marshal(order.Tags)
		args = append(args, 
			order.ID, order.Status, string(tagsJSON), order.Supplier.GetEnglishName(),
			order.CreateTime, order.TenantEntityID, order.CustomerEntityID,
			order.ProfitGenAmount, "USD",
		)
	}
	
	query += fmt.Sprintf("%s", fmt.Sprintf("%v", values)[1:len(fmt.Sprintf("%v", values))-1])
	query = fmt.Sprintf(query, values)
	
	_, err := dao.conn.ExecCtx(ctx, query, args...)
	return err
}

// ====================================================================
// 辅助方法
// ====================================================================

// modelToDomain 将数据库模型转换为域对象
func (dao *BIOrderDAO) modelToDomain(model *BIOrderModel) *domain.BIOrder {
	return &domain.BIOrder{
		ID:               model.ID,
		Status:           model.Status,
		Tags:             []string(model.Tags),
		CreateTime:       model.CreateTime,
		TenantEntityID:   model.TenantEntityID,
		CustomerEntityID: model.CustomerEntityID,
		ProfitGenAmount:  model.ProfitGenAmount,
		ProfitGenCurrency: model.ProfitGenAmount,
	}
}

// ====================================================================
// 分析结果结构体 (moved to order_analytics_dao.go to avoid duplication)
// ====================================================================

// TaggedOrderStats 带标签的订单统计 (internal use)
type TaggedOrderStats struct {
	TotalCount             int     `db:"total_count"`
	ProfitGenCount         int     `db:"profit_gen_count"`
	ErrorRecoveryCount     int     `db:"error_recovery_count"`
	TotalProfitGenAmount   float64 `db:"total_profit_gen_amount"`
	TotalBuyerAmount       float64 `db:"total_buyer_amount"`
}

// RebookMetricsInternal 重预订指标 (internal use)
type RebookMetricsInternal struct {
	RebookCount            int     `db:"rebook_count"`
	TotalProfitGenAmount   float64 `db:"total_profit_gen_amount"`
	TotalRebookAmount      float64 `db:"total_rebook_amount"`
}

// SupplierTransferInternal 供应商转换 (internal use)
type SupplierTransferInternal struct {
	OldSupplier            string  `db:"old_supplier"`
	NewSupplier            string  `db:"new_supplier"`
	TransferCount          int     `db:"transfer_count"`
	TotalProfitDifference  float64 `db:"total_profit_difference"`
}

// ClientConfiguration 客户配置 (internal use)
type ClientConfiguration struct {
	RoomMappingVersion     string    `db:"room_mapping_version"`
	SmartBookMode          JSONArray `db:"smart_book_mode"`
	DynamicMarkupsEnabled  bool      `db:"dynamic_markups_enabled"`
	BestPackagesEnabled    bool      `db:"best_packages_enabled"`
	TwoFactorAuthEnabled   bool      `db:"two_factor_auth_enabled"`
	ConfigUpdateTime       time.Time `db:"config_update_time"`
}

// RequestAnalysis 请求分析 (internal use)
type RequestAnalysis struct {
	GeoSearchCount         int `db:"geo_search_count"`
	HotelSearchCount       int `db:"hotel_search_count"`
	KeywordSearchCount     int `db:"keyword_search_count"`
	ListSearchCount        int `db:"list_search_count"`
	DetailSearchCount      int `db:"detail_search_count"`
	CheckAvailCount        int `db:"check_avail_count"`
	TotalRequests          int `db:"total_requests"`
}

// ResponseAnalysis 响应分析 (internal use)
type ResponseAnalysis struct {
	SuccessCount           int     `db:"success_count"`
	FailureCount           int     `db:"failure_count"`
	TotalResponses         int     `db:"total_responses"`
	AvgDurationMs          float64 `db:"avg_duration_ms"`
	P50DurationMs          float64 `db:"p50_duration_ms"`
	P95DurationMs          float64 `db:"p95_duration_ms"`
	SuccessRate            float64 `db:"success_rate"`
}

// SupplierBookingStatInternal 供应商预订统计 (internal use)
type SupplierBookingStatInternal struct {
	Supplier               string  `db:"supplier"`
	BookingCount           int     `db:"booking_count"`
	BookingVolume          float64 `db:"booking_volume"`
	AvgBookingAmount       float64 `db:"avg_booking_amount"`
	SuccessRate            float64 `db:"success_rate"`
}