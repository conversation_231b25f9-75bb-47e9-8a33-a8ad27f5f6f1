package protocol

import (
	"hotel/common/i18n"
	"hotel/common/money"
	"time"
)

// OrderAnalyticsReq represents the request structure for order analytics operation with 5 fields
type // OrderAnalyticsReq represents the request structure for OrderAnalytics operation with 5 fields
OrderAnalyticsReq struct {
		// StartDate represents the date for this Start
StartDate    *time.Time `json:"startDate,omitempty"`    // StartDate represents the start date for this analytics
		// EndDate represents the date for this End
EndDate      *time.Time `json:"endDate,omitempty"`      // EndDate represents the end date for this analytics
		// Granularity contains the granularity value
Granularity  string     `json:"granularity,omitempty"`  // Granularity contains the granularity value for this analytics
		// StatusFilter represents the current status of this StatusFilter
StatusFilter []int64    `json:"statusFilter,omitempty"` // StatusFilter contains a list of status filter items
		// EntityId is the unique identifier for this Entity
EntityId     *int64     `json:"entityId,omitempty"`     // EntityId represents the entity identifier as an integer for this analytics
}

// OrderAnalyticsResp represents the response structure for order analytics operation with 8 fields
type // OrderAnalyticsResp represents the response structure for OrderAnalytics operation with 8 fields
OrderAnalyticsResp struct {
		// Overview contains the overview data
Overview          *OrderOverview     `json:"overview"`          // Overview contains the overview data for this analytics
		// TrendData contains the main data for this Trend
TrendData         []*OrderTrendData  `json:"trendData"`         // TrendData contains a list of trend data items
		// StatusBreakdown represents the current status of this StatusBreakdown
StatusBreakdown   []*OrderStatusStat `json:"statusBreakdown"`   // StatusBreakdown contains a list of status breakdown items
		// RevenueAnalysis contains the revenueanalysis data
RevenueAnalysis   *RevenueAnalysis   `json:"revenueAnalysis"`   // RevenueAnalysis contains the revenue analysis data
		// TopPerformers contains the topperformers data
TopPerformers     *TopPerformers     `json:"topPerformers"`     // TopPerformers contains the top performers data
		// RegionalAnalysis contains the regionalanalysis data
RegionalAnalysis  []*RegionalStat    `json:"regionalAnalysis"`  // RegionalAnalysis contains a list of regional analysis items
		// SmartBookOverview contains the smartbookoverview data
SmartBookOverview *SmartBookOverview `json:"smartBookOverview"` // SmartBookOverview contains the SmartBook overview data
		// RebookOverview contains the rebookoverview data
RebookOverview    *RebookOverview    `json:"rebookOverview"`    // RebookOverview contains the rebook overview data
}

// SmartBookOverview SmartBook分析总览 (基于BIOrder.Tags)
type // SmartBookOverview represents a data structure for API communication with 6 fields
SmartBookOverview struct {
		// ProcessedBookingsCount represents the count of ProcessedBookings
ProcessedBookingsCount      int64       `json:"processedBookingsCount"`      // 处理的预订数量
		// ProcessedBookingsAmount represents the monetary amount for this ProcessedBookings
ProcessedBookingsAmount     money.Money `json:"processedBookingsAmount"`     // 处理的预订金额
		// SmartBookProfitGenCount represents the count of SmartBookProfitGen
SmartBookProfitGenCount     int64       `json:"smartBookProfitGenCount"`     // SmartBook利润生成数量
		// OptimizedProfitGenAmount represents the monetary amount for this OptimizedProfitGen
OptimizedProfitGenAmount    money.Money `json:"optimizedProfitGenAmount"`    // 优化的利润生成金额
		// SmartBookErrorRecoveryCount represents the count of SmartBookErrorRecovery
SmartBookErrorRecoveryCount int64       `json:"smartBookErrorRecoveryCount"` // SmartBook错误恢复数量
		// SavedErrorRecoveryAmount represents the monetary amount for this SavedErrorRecovery
SavedErrorRecoveryAmount    money.Money `json:"savedErrorRecoveryAmount"`    // 节省的错误恢复金额
}

// RebookOverview Rebook分析总览
type // RebookOverview represents a data structure for API communication with 5 fields
RebookOverview struct {
		// ProcessedBookingsCount represents the count of ProcessedBookings
ProcessedBookingsCount   int64              `json:"processedBookingsCount"`   // 处理的重预订数量
		// ProcessedBookingsAmount represents the monetary amount for this ProcessedBookings
ProcessedBookingsAmount  money.Money        `json:"processedBookingsAmount"`  // 处理的重预订金额
		// RebookProfitGenCount represents the count of RebookProfitGen
RebookProfitGenCount     int64              `json:"rebookProfitGenCount"`     // Rebook利润生成数量
		// OptimizedProfitGenAmount represents the monetary amount for this OptimizedProfitGen
OptimizedProfitGenAmount money.Money        `json:"optimizedProfitGenAmount"` // 优化的利润生成金额
		// SupplierTransfers contains the suppliertransfers data
SupplierTransfers        []SupplierTransfer `json:"supplierTransfers"`        // 供应商转换记录
}

// SupplierTransfer 供应商转换记录
type // SupplierTransfer represents a data structure for API communication with 6 fields
SupplierTransfer struct {
		// OrderID is the unique identifier for this OrderID
OrderID      int64       `json:"orderID"`      // 订单ID
		// OldSupplier contains the oldsupplier value
OldSupplier  string      `json:"oldSupplier"`  // 原供应商
		// NewSupplier contains the newsupplier value
NewSupplier  string      `json:"newSupplier"`  // 新供应商
		// Amount represents the monetary amount for this 
Amount       money.Money `json:"amount"`       // 转换金额
		// TransferTime represents the timestamp when this Transfer occurred
TransferTime time.Time   `json:"transferTime"` // 转换时间
		// Reason contains the reason value
Reason       string      `json:"reason"`       // 转换原因
}

// ClientAnalytics 客户分析 (基于"Know your clients"需求)
type // ClientAnalytics represents a data structure for API communication with 5 fields
ClientAnalytics struct {
		// EntityID is the unique identifier for this EntityID
EntityID         int64                 `json:"entityID"`         // 实体ID
		// Configuration contains configuration for this Configuration
Configuration    ClientConfiguration   `json:"configuration"`    // 客户配置分析
		// RequestAnalysis contains the requestanalysis data
RequestAnalysis  RequestAnalysis       `json:"requestAnalysis"`  // 请求分析
		// ResponseAnalysis contains the responseanalysis data
ResponseAnalysis ResponseAnalysis      `json:"responseAnalysis"` // 响应分析
		// SupplierBookings contains the supplierbookings data
SupplierBookings []SupplierBookingStat `json:"supplierBookings"` // 供应商预订统计
}

// ClientConfiguration 客户配置分析
type // ClientConfiguration represents a data structure for API communication with 5 fields
ClientConfiguration struct {
		// RoomMappingVersion contains the roommappingversion value
RoomMappingVersion string `json:"roomMappingVersion"` // 房型映射版本
		// SmartBookMode contains the smartbookmode value
SmartBookMode      string `json:"smartBookMode"`      // SmartBook模式
		// DynamicMarkups indicates whether dynamicmarkups is enabled
DynamicMarkups     bool   `json:"dynamicMarkups"`     // 动态加价是否启用
		// BestPackages indicates whether bestpackages is enabled
BestPackages       bool   `json:"bestPackages"`       // 最佳套餐是否启用
		// TwoFactorAuth indicates whether twofactorauth is enabled
TwoFactorAuth      bool   `json:"twoFactorAuth"`      // 双因素认证是否启用
}

// RequestAnalysis 请求分析
type // RequestAnalysis represents a data structure for API communication with 4 fields
RequestAnalysis struct {
		// SearchTypeCount represents the count of SearchType
SearchTypeCount   map[string]int64 `json:"searchTypeCount"`   // 搜索类型计数(geo,...)
		// ListSearchCount contains a list of ListSearchCount items
ListSearchCount   int64            `json:"listSearchCount"`   // 列表搜索计数
		// DetailSearchCount represents the count of DetailSearch
DetailSearchCount int64            `json:"detailSearchCount"` // 详情搜索计数
		// CheckAvailCount represents the count of CheckAvail
CheckAvailCount   int64            `json:"checkAvailCount"`   // 可用性检查计数
}

// ResponseAnalysis 响应分析
type // ResponseAnalysis represents a data structure for API communication with 5 fields
ResponseAnalysis struct {
		// SuccessRate indicates whether this SuccessRate was successful
SuccessRate float64 `json:"successRate"` // 成功率
		// FailureRate represents the failurerate as a decimal number
FailureRate float64 `json:"failureRate"` // 失败率
		// AvgDuration represents the avgduration as a decimal number
AvgDuration float64 `json:"avgDuration"` // 平均响应时间
		// P50Duration represents the p50duration as a decimal number
P50Duration float64 `json:"p50Duration"` // P50响应时间
		// P95Duration represents the p95duration as a decimal number
P95Duration float64 `json:"p95Duration"` // P95响应时间
}

// SupplierBookingStat 供应商预订统计
type // SupplierBookingStat represents a data structure for API communication with 3 fields
SupplierBookingStat struct {
		// Supplier contains the supplier value
Supplier      string      `json:"supplier"`      // 供应商
		// BookingCount represents the count of Booking
BookingCount  int64       `json:"bookingCount"`  // 预订数量
		// BookingVolume contains the bookingvolume data
BookingVolume money.Money `json:"bookingVolume"` // 预订金额
}

// OrderOverview 订单总览
type // OrderOverview represents a data structure for API communication with 8 fields
OrderOverview struct {
		// TotalOrders represents the total number of TotalOrders
TotalOrders       int64       `json:"totalOrders"`       // 总订单数
		// TotalRevenue represents the total number of TotalRevenue
TotalRevenue      money.Money `json:"totalRevenue"`      // 总收入
		// TotalProfit represents the total number of TotalProfit
TotalProfit       money.Money `json:"totalProfit"`       // 总利润
		// AverageOrderValue contains the averageordervalue data
AverageOrderValue money.Money `json:"averageOrderValue"` // 平均订单价值
		// BookingRate represents the bookingrate as a decimal number
BookingRate       float64     `json:"bookingRate"`       // 预订转化率
		// CancellationRate represents the cancellationrate as a decimal number
CancellationRate  float64     `json:"cancellationRate"`  // 取消率
		// CompletionRate represents the completionrate as a decimal number
CompletionRate    float64     `json:"completionRate"`    // 完成率
		// GrowthRate represents the growthrate as a decimal number
GrowthRate        float64     `json:"growthRate"`        // 增长率(相比上期)
}

// OrderTrendData 订单趋势数据
type // OrderTrendData represents a data structure for API communication with 6 fields
OrderTrendData struct {
		// Date represents the date for this 
Date         time.Time   `json:"date"`         // 日期
		// OrderCount represents the count of Order
OrderCount   int64       `json:"orderCount"`   // 订单数量
		// Revenue contains the revenue data
Revenue      money.Money `json:"revenue"`      // 收入
		// Profit contains the profit data
Profit       money.Money `json:"profit"`       // 利润
		// BookingCount represents the count of Booking
BookingCount int64       `json:"bookingCount"` // 预订数量
		// CancelCount represents the count of Cancel
CancelCount  int64       `json:"cancelCount"`  // 取消数量
}

// OrderStatusStat 订单状态统计
type // OrderStatusStat represents a data structure for API communication with 6 fields
OrderStatusStat struct {
		// Status represents the current status of this 
Status      int64          `json:"status"`      // 状态码
		// StatusName is the display name for this Status
StatusName  i18n.I18N      `json:"statusName"`  // 状态名称
		// Count represents the count of 
Count       int64          `json:"count"`       // 数量
		// Percentage represents the percentage as a decimal number
Percentage  float64        `json:"percentage"`  // 占比
		// Revenue contains the revenue data
Revenue     money.Money    `json:"revenue"`     // 该状态订单总收入
		// AvgDuration contains the avgduration data
AvgDuration *time.Duration `json:"avgDuration"` // 平均持续时间
}

// RevenueAnalysis 收入分析
type // RevenueAnalysis represents a data structure for API communication with 7 fields
RevenueAnalysis struct {
		// TotalRevenue represents the total number of TotalRevenue
TotalRevenue      money.Money          `json:"totalRevenue"`      // 总收入
		// TotalCost represents the total number of TotalCost
TotalCost         money.Money          `json:"totalCost"`         // 总成本
		// TotalProfit represents the total number of TotalProfit
TotalProfit       money.Money          `json:"totalProfit"`       // 总利润
		// ProfitMargin represents the profitmargin as a decimal number
ProfitMargin      float64              `json:"profitMargin"`      // 利润率
		// RevenueByCurrency represents the currency code for this RevenueBy
RevenueByCurrency []*CurrencyRevenue   `json:"revenueByCurrency"` // 按币种收入
		// RevenueByEntity contains the revenuebyentity data
RevenueByEntity   []*EntityRevenue     `json:"revenueByEntity"`   // 按实体收入
		// MonthlyComparison contains the monthlycomparison data
MonthlyComparison []*MonthlyComparison `json:"monthlyComparison"` // 月度对比
}

// CurrencyRevenue 币种收入
type // CurrencyRevenue represents a data structure for API communication with 4 fields
CurrencyRevenue struct {
		// Currency represents the currency code for this 
Currency   string      `json:"currency"`   // 币种
		// Revenue contains the revenue data
Revenue    money.Money `json:"revenue"`    // 收入
		// OrderCount represents the count of Order
OrderCount int64       `json:"orderCount"` // 订单数
		// Percentage represents the percentage as a decimal number
Percentage float64     `json:"percentage"` // 占比
}

// EntityRevenue 实体收入
type // EntityRevenue represents a data structure for API communication with 6 fields
EntityRevenue struct {
		// EntityId is the unique identifier for this Entity
EntityId   int64       `json:"entityId"`   // 实体ID
		// EntityName is the display name for this Entity
EntityName i18n.I18N   `json:"entityName"` // 实体名称
		// Revenue contains the revenue data
Revenue    money.Money `json:"revenue"`    // 收入
		// Profit contains the profit data
Profit     money.Money `json:"profit"`     // 利润
		// OrderCount represents the count of Order
OrderCount int64       `json:"orderCount"` // 订单数
		// GrowthRate represents the growthrate as a decimal number
GrowthRate float64     `json:"growthRate"` // 增长率
}

// MonthlyComparison 月度对比
type // MonthlyComparison represents a data structure for API communication with 5 fields
MonthlyComparison struct {
		// Month represents the timestamp for month
Month      time.Time   `json:"month"`      // 月份
		// Revenue contains the revenue data
Revenue    money.Money `json:"revenue"`    // 收入
		// Profit contains the profit data
Profit     money.Money `json:"profit"`     // 利润
		// OrderCount represents the count of Order
OrderCount int64       `json:"orderCount"` // 订单数
		// GrowthRate represents the growthrate as a decimal number
GrowthRate float64     `json:"growthRate"` // 增长率
}

// TopPerformers 表现排行
type // TopPerformers represents a data structure for API communication with 3 fields
TopPerformers struct {
		// TopCustomers contains the topcustomers data
TopCustomers []*CustomerPerformance `json:"topCustomers"` // 顶级客户
		// TopEntities contains the topentities data
TopEntities  []*EntityPerformance   `json:"topEntities"`  // 顶级实体
		// TopDays contains the topdays data
TopDays      []*DayPerformance      `json:"topDays"`      // 表现最佳日期
}

// CustomerPerformance 客户表现
type // CustomerPerformance represents a data structure for API communication with 6 fields
CustomerPerformance struct {
		// CustomerId is the unique identifier for this Customer
CustomerId    int64       `json:"customerId"`    // 客户ID
		// CustomerName is the display name for this Customer
CustomerName  i18n.I18N   `json:"customerName"`  // 客户名称
		// TotalRevenue represents the total number of TotalRevenue
TotalRevenue  money.Money `json:"totalRevenue"`  // 总收入
		// OrderCount represents the count of Order
OrderCount    int64       `json:"orderCount"`    // 订单数
		// AvgOrderValue contains the avgordervalue data
AvgOrderValue money.Money `json:"avgOrderValue"` // 平均订单价值
		// LastOrderDate represents the date for this LastOrder
LastOrderDate time.Time   `json:"lastOrderDate"` // 最后订单日期
}

// EntityPerformance 实体表现
type // EntityPerformance represents a data structure for API communication with 6 fields
EntityPerformance struct {
		// EntityId is the unique identifier for this Entity
EntityId     int64       `json:"entityId"`     // 实体ID
		// EntityName is the display name for this Entity
EntityName   i18n.I18N   `json:"entityName"`   // 实体名称
		// TotalRevenue represents the total number of TotalRevenue
TotalRevenue money.Money `json:"totalRevenue"` // 总收入
		// TotalProfit represents the total number of TotalProfit
TotalProfit  money.Money `json:"totalProfit"`  // 总利润
		// OrderCount represents the count of Order
OrderCount   int64       `json:"orderCount"`   // 订单数
		// ProfitMargin represents the profitmargin as a decimal number
ProfitMargin float64     `json:"profitMargin"` // 利润率
}

// DayPerformance 日期表现
type // DayPerformance represents a data structure for API communication with 4 fields
DayPerformance struct {
		// Date represents the date for this 
Date       time.Time   `json:"date"`       // 日期
		// Revenue contains the revenue data
Revenue    money.Money `json:"revenue"`    // 收入
		// OrderCount represents the count of Order
OrderCount int64       `json:"orderCount"` // 订单数
		// Profit contains the profit data
Profit     money.Money `json:"profit"`     // 利润
}

// RegionalStat 区域统计
type // RegionalStat represents a data structure for API communication with 5 fields
RegionalStat struct {
		// Region contains the region value
Region     string      `json:"region"`     // 区域
		// OrderCount represents the count of Order
OrderCount int64       `json:"orderCount"` // 订单数
		// Revenue contains the revenue data
Revenue    money.Money `json:"revenue"`    // 收入
		// Profit contains the profit data
Profit     money.Money `json:"profit"`     // 利润
		// GrowthRate represents the growthrate as a decimal number
GrowthRate float64     `json:"growthRate"` // 增长率
}

// OrderMetricsReq 订单指标请求
type // OrderMetricsReq represents the request structure for OrderMetrics operation with 5 fields
OrderMetricsReq struct {
		// MetricType contains the metrictype value
MetricType  string                 `json:"metricType"` // 指标类型: overview/trend/status/revenue
		// StartDate represents the date for this Start
StartDate   *time.Time             `json:"startDate,omitempty"`
		// EndDate represents the date for this End
EndDate     *time.Time             `json:"endDate,omitempty"`
		// Granularity contains the granularity value
Granularity string                 `json:"granularity,omitempty"` // day/week/month
		// Filters contains the filters data
Filters     map[string]interface{} `json:"filters,omitempty"`     // 其他过滤条件
}

// OrderMetricsResp 订单指标响应
type // OrderMetricsResp represents the response structure for OrderMetrics operation with 3 fields
OrderMetricsResp struct {
		// MetricType contains the metrictype value
MetricType string      `json:"metricType"` // 指标类型
		// Data contains the main data for this 
Data       interface{} `json:"data"`       // 具体数据，根据MetricType动态决定结构
		// UpdateTime represents the timestamp when this Update occurred
UpdateTime time.Time   `json:"updateTime"` // 数据更新时间
}

// RealTimeMetricsReq 实时指标请求
type // RealTimeMetricsReq represents the request structure for RealTimeMetrics operation with 1 fields
RealTimeMetricsReq struct {
		// Metrics contains the metrics data
Metrics []string `json:"metrics"` // 需要的指标列表
}

// RealTimeMetricsResp 实时指标响应
type // RealTimeMetricsResp represents the response structure for RealTimeMetrics operation with 7 fields
RealTimeMetricsResp struct {
		// TodayOrders represents the todayorders as an integer
TodayOrders      int64       `json:"todayOrders"`      // 今日订单
		// TodayRevenue contains the todayrevenue data
TodayRevenue     money.Money `json:"todayRevenue"`     // 今日收入
		// ActiveBookings represents the activebookings as an integer
ActiveBookings   int64       `json:"activeBookings"`   // 活跃预订
		// PendingOrders represents the pendingorders as an integer
PendingOrders    int64       `json:"pendingOrders"`    // 待处理订单
		// CompletionRate represents the completionrate as a decimal number
CompletionRate   float64     `json:"completionRate"`   // 完成率
		// CancellationRate represents the cancellationrate as a decimal number
CancellationRate float64     `json:"cancellationRate"` // 取消率
		// UpdateTime represents the timestamp when this Update occurred
UpdateTime       time.Time   `json:"updateTime"`       // 更新时间
}

// ExportReq 导出请求
type // ExportReq represents the request structure for Export operation with 6 fields
ExportReq struct {
		// ExportType contains the exporttype value
ExportType string                 `json:"exportType"` // 导出类型: excel/csv/pdf
		// DataType contains the main data for this DataType
DataType   string                 `json:"dataType"`   // 数据类型: orders/analytics/revenue
		// StartDate represents the date for this Start
StartDate  *time.Time             `json:"startDate,omitempty"`
		// EndDate represents the date for this End
EndDate    *time.Time             `json:"endDate,omitempty"`
		// Filters contains the filters data
Filters    map[string]interface{} `json:"filters,omitempty"`
		// Fields contains the fields data
Fields     []string               `json:"fields,omitempty"` // 需要导出的字段
}

// ExportResp 导出响应
type // ExportResp represents the response structure for Export operation with 4 fields
ExportResp struct {
		// FileUrl is the URL for this File
FileUrl    string    `json:"fileUrl"`    // 文件下载链接
		// FileName is the display name for this File
FileName   string    `json:"fileName"`   // 文件名
		// FileSize represents the filesize as an integer
FileSize   int64     `json:"fileSize"`   // 文件大小
		// ExpiryTime represents the timestamp when this Expiry occurred
ExpiryTime time.Time `json:"expiryTime"` // 过期时间
}
