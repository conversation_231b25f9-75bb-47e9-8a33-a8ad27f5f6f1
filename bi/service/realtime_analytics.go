package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"hotel/bi/mysql"
	"hotel/common/log"
)

// RealTimeAnalyticsService 实时分析数据收集服务
type RealTimeAnalyticsService struct {
	analyticsDAO      *mysql.OrderAnalyticsDAO
	metrics          *MetricsCollector
	updateInterval   time.Duration
	isRunning        bool
	stopChan         chan struct{}
	mu               sync.RWMutex
}

// MetricsCollector 实时指标收集器
type MetricsCollector struct {
	todayOrderCount    int64
	todayRevenue      float64
	activeBookings    int64
	pendingOrders     int64
	completionRate    float64
	cancellationRate  float64
	lastUpdateTime    time.Time
	mu                sync.RWMutex
}

// RealTimeMetrics 实时指标
type RealTimeMetrics struct {
	TodayOrders      int64     `json:"todayOrders"`
	TodayRevenue     float64   `json:"todayRevenue"`
	ActiveBookings   int64     `json:"activeBookings"`
	PendingOrders    int64     `json:"pendingOrders"`
	CompletionRate   float64   `json:"completionRate"`
	CancellationRate float64   `json:"cancellationRate"`
	UpdateTime       time.Time `json:"updateTime"`
}

// NewRealTimeAnalyticsService 创建实时分析服务
func NewRealTimeAnalyticsService(analyticsDAO *mysql.OrderAnalyticsDAO) *RealTimeAnalyticsService {
	return &RealTimeAnalyticsService{
		analyticsDAO:   analyticsDAO,
		metrics:        &MetricsCollector{},
		updateInterval: 5 * time.Minute, // 每5分钟更新一次
		stopChan:       make(chan struct{}),
	}
}

// Start 启动实时数据收集
func (s *RealTimeAnalyticsService) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.isRunning {
		return fmt.Errorf("real-time analytics service is already running")
	}
	
	s.isRunning = true
	
	// 立即执行一次数据收集
	if err := s.collectMetrics(ctx); err != nil {
		log.Errorc(ctx, "Failed to collect initial metrics: %v", err)
	}
	
	// 启动定时收集
	go s.startPeriodicCollection(ctx)
	
	log.Infoc(ctx, "Real-time analytics service started with update interval: %v", s.updateInterval)
	return nil
}

// Stop 停止实时数据收集
func (s *RealTimeAnalyticsService) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.isRunning {
		return
	}
	
	s.isRunning = false
	close(s.stopChan)
	log.Info("Real-time analytics service stopped")
}

// GetMetrics 获取当前实时指标
func (s *RealTimeAnalyticsService) GetMetrics() *RealTimeMetrics {
	s.metrics.mu.RLock()
	defer s.metrics.mu.RUnlock()
	
	return &RealTimeMetrics{
		TodayOrders:      s.metrics.todayOrderCount,
		TodayRevenue:     s.metrics.todayRevenue,
		ActiveBookings:   s.metrics.activeBookings,
		PendingOrders:    s.metrics.pendingOrders,
		CompletionRate:   s.metrics.completionRate,
		CancellationRate: s.metrics.cancellationRate,
		UpdateTime:       s.metrics.lastUpdateTime,
	}
}

// startPeriodicCollection 启动定时数据收集
func (s *RealTimeAnalyticsService) startPeriodicCollection(ctx context.Context) {
	ticker := time.NewTicker(s.updateInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			if err := s.collectMetrics(ctx); err != nil {
				log.Errorc(ctx, "Failed to collect metrics: %v", err)
			}
		case <-s.stopChan:
			return
		case <-ctx.Done():
			return
		}
	}
}

// collectMetrics 收集实时指标
func (s *RealTimeAnalyticsService) collectMetrics(ctx context.Context) error {
	now := time.Now()
	log.Infoc(ctx, "Starting real-time metrics collection at %v", now)
	
	// 获取今日实时指标
	metrics, err := s.analyticsDAO.GetRealTimeMetrics(ctx)
	if err != nil {
		return fmt.Errorf("failed to get real-time metrics: %w", err)
	}
	
	// 计算完成率和取消率
	totalOrders := metrics.TotalOrders
	if totalOrders == 0 {
		totalOrders = 1 // 避免除零错误
	}
	
	completionRate := float64(metrics.CompletedOrders) / float64(totalOrders) * 100
	cancellationRate := float64(metrics.CancelledOrders) / float64(totalOrders) * 100
	
	// 更新指标
	s.metrics.mu.Lock()
	s.metrics.todayOrderCount = metrics.TotalOrders
	s.metrics.todayRevenue = float64(metrics.TotalRevenue)
	s.metrics.activeBookings = metrics.CompletedOrders
	s.metrics.pendingOrders = metrics.PendingOrders
	s.metrics.completionRate = completionRate
	s.metrics.cancellationRate = cancellationRate
	s.metrics.lastUpdateTime = now
	s.metrics.mu.Unlock()
	
	log.Infoc(ctx, "Real-time metrics updated: orders=%d, revenue=%.2f, completion=%.2f%%, cancellation=%.2f%%",
		metrics.TotalOrders, float64(metrics.TotalRevenue), completionRate, cancellationRate)
	
	return nil
}

// SetUpdateInterval 设置更新间隔
func (s *RealTimeAnalyticsService) SetUpdateInterval(interval time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.updateInterval = interval
	log.Info("Real-time analytics update interval changed to: %v", interval)
}

// ForceUpdate 强制更新指标
func (s *RealTimeAnalyticsService) ForceUpdate(ctx context.Context) error {
	return s.collectMetrics(ctx)
}

// GetUpdateInterval 获取当前更新间隔
func (s *RealTimeAnalyticsService) GetUpdateInterval() time.Duration {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.updateInterval
}

// IsRunning 检查服务是否运行中
func (s *RealTimeAnalyticsService) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// GetStatus 获取服务状态
func (s *RealTimeAnalyticsService) GetStatus() map[string]interface{} {
	s.mu.RLock()
	s.metrics.mu.RLock()
	defer s.mu.RUnlock()
	defer s.metrics.mu.RUnlock()
	
	return map[string]interface{}{
		"isRunning":      s.isRunning,
		"updateInterval": s.updateInterval.String(),
		"lastUpdate":     s.metrics.lastUpdateTime,
		"metrics":        s.GetMetrics(),
	}
}