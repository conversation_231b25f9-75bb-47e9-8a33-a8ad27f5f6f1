package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"hotel/bi/mysql"
	"hotel/bi/protocol"
	"hotel/common/log"
)

// AnalyticsCache 分析数据缓存
type AnalyticsCache struct {
	data      map[string]interface{}
	expiry    map[string]time.Time
	mutex     sync.RWMutex
	defaultTTL time.Duration
}

// CacheKey 缓存键结构
type CacheKey struct {
	Type      string
	StartDate string
	EndDate   string
	EntityID  string
	Extra     string
}

// String 生成缓存键字符串
func (ck CacheKey) String() string {
	return ck.Type + ":" + ck.StartDate + ":" + ck.EndDate + ":" + ck.EntityID + ":" + ck.Extra
}

// NewAnalyticsCache 创建分析缓存
func NewAnalyticsCache(defaultTTL time.Duration) *AnalyticsCache {
	cache := &AnalyticsCache{
		data:       make(map[string]interface{}),
		expiry:     make(map[string]time.Time),
		defaultTTL: defaultTTL,
	}
	
	// 启动清理goroutine
	go cache.startCleanup()
	
	return cache
}

// Get 获取缓存数据
func (c *AnalyticsCache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	// 检查是否过期
	if expiry, exists := c.expiry[key]; exists && time.Now().After(expiry) {
		delete(c.data, key)
		delete(c.expiry, key)
		return nil, false
	}
	
	data, exists := c.data[key]
	return data, exists
}

// Set 设置缓存数据
func (c *AnalyticsCache) Set(key string, value interface{}, ttl ...time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	duration := c.defaultTTL
	if len(ttl) > 0 {
		duration = ttl[0]
	}
	
	c.data[key] = value
	c.expiry[key] = time.Now().Add(duration)
}

// Delete 删除缓存数据
func (c *AnalyticsCache) Delete(key string) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	delete(c.data, key)
	delete(c.expiry, key)
}

// Clear 清空所有缓存
func (c *AnalyticsCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.data = make(map[string]interface{})
	c.expiry = make(map[string]time.Time)
}

// startCleanup 启动定期清理过期缓存
func (c *AnalyticsCache) startCleanup() {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟清理一次
	defer ticker.Stop()
	
	for range ticker.C {
		c.cleanup()
	}
}

// cleanup 清理过期缓存
func (c *AnalyticsCache) cleanup() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	now := time.Now()
	expired := make([]string, 0)
	
	for key, expiry := range c.expiry {
		if now.After(expiry) {
			expired = append(expired, key)
		}
	}
	
	for _, key := range expired {
		delete(c.data, key)
		delete(c.expiry, key)
	}
	
	if len(expired) > 0 {
		log.Info("Cleaned up %d expired cache entries", len(expired))
	}
}

// OptimizedAnalyticsService 优化的分析服务
type OptimizedAnalyticsService struct {
	*OrderAnalyticsService
	cache           *AnalyticsCache
	batchProcessor  *BatchProcessor
	performanceMetrics *PerformanceMetrics
}

// BatchProcessor 批处理器
type BatchProcessor struct {
	queue    []interface{}
	mutex    sync.Mutex
	size     int
	interval time.Duration
	processor func([]interface{}) error
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	QueryCount      int64
	CacheHitCount   int64
	CacheMissCount  int64
	AvgQueryTime    time.Duration
	TotalQueryTime  time.Duration
	mutex           sync.RWMutex
}

// NewOptimizedAnalyticsService 创建优化的分析服务
func NewOptimizedAnalyticsService(orderDAO *mysql.OrderAnalyticsDAO) *OptimizedAnalyticsService {
	baseService := NewOrderAnalyticsService(orderDAO)
	cache := NewAnalyticsCache(15 * time.Minute) // 15分钟缓存
	
	return &OptimizedAnalyticsService{
		OrderAnalyticsService: baseService,
		cache:                 cache,
		performanceMetrics:    &PerformanceMetrics{},
	}
}

// GetOrderAnalyticsWithCache 带缓存的分析数据获取
func (s *OptimizedAnalyticsService) GetOrderAnalyticsWithCache(ctx context.Context, req *protocol.OrderAnalyticsReq) (*protocol.OrderAnalyticsResp, error) {
	startTime := time.Now()
	defer func() {
		s.recordQueryMetrics(time.Since(startTime))
	}()
	
	// 生成缓存键
	cacheKey := CacheKey{
		Type:      "analytics",
		StartDate: req.StartDate.Format("2006-01-02"),
		EndDate:   req.EndDate.Format("2006-01-02"),
		EntityID:  s.entityIDToString(req.EntityId),
		Extra:     req.Granularity,
	}.String()
	
	// 尝试从缓存获取
	if cached, exists := s.cache.Get(cacheKey); exists {
		s.recordCacheHit()
		return cached.(*protocol.OrderAnalyticsResp), nil
	}
	
	s.recordCacheMiss()
	
	// 从数据库获取
	resp, err := s.OrderAnalyticsService.GetOrderAnalytics(ctx, req)
	if err != nil {
		return nil, err
	}
	
	// 缓存结果
	s.cache.Set(cacheKey, resp, 10*time.Minute) // 10分钟缓存
	
	return resp, nil
}

// PrewarmCache 预热缓存
func (s *OptimizedAnalyticsService) PrewarmCache(ctx context.Context) error {
	log.Infoc(ctx, "Starting cache prewarming...")
	
	now := time.Now()
	
	// 预热常用的查询
	commonQueries := []*protocol.OrderAnalyticsReq{
		// 今日数据
		{
			StartDate:   &now,
			EndDate:     &now,
			Granularity: "day",
		},
		// 最近7天
		{
			StartDate:   func() *time.Time { t := now.AddDate(0, 0, -7); return &t }(),
			EndDate:     &now,
			Granularity: "day",
		},
		// 最近30天
		{
			StartDate:   func() *time.Time { t := now.AddDate(0, 0, -30); return &t }(),
			EndDate:     &now,
			Granularity: "day",
		},
	}
	
	for _, req := range commonQueries {
		if _, err := s.GetOrderAnalyticsWithCache(ctx, req); err != nil {
			log.Errorc(ctx, "Failed to prewarm cache for query: %v", err)
		}
	}
	
	log.Infoc(ctx, "Cache prewarming completed")
	return nil
}

// GetPerformanceMetrics 获取性能指标
func (s *OptimizedAnalyticsService) GetPerformanceMetrics() map[string]interface{} {
	s.performanceMetrics.mutex.RLock()
	defer s.performanceMetrics.mutex.RUnlock()
	
	var cacheHitRate float64
	totalCache := s.performanceMetrics.CacheHitCount + s.performanceMetrics.CacheMissCount
	if totalCache > 0 {
		cacheHitRate = float64(s.performanceMetrics.CacheHitCount) / float64(totalCache) * 100
	}
	
	return map[string]interface{}{
		"queryCount":      s.performanceMetrics.QueryCount,
		"cacheHitCount":   s.performanceMetrics.CacheHitCount,
		"cacheMissCount":  s.performanceMetrics.CacheMissCount,
		"cacheHitRate":    cacheHitRate,
		"avgQueryTime":    s.performanceMetrics.AvgQueryTime,
		"totalQueryTime":  s.performanceMetrics.TotalQueryTime,
	}
}

// recordQueryMetrics 记录查询指标
func (s *OptimizedAnalyticsService) recordQueryMetrics(duration time.Duration) {
	s.performanceMetrics.mutex.Lock()
	defer s.performanceMetrics.mutex.Unlock()
	
	s.performanceMetrics.QueryCount++
	s.performanceMetrics.TotalQueryTime += duration
	s.performanceMetrics.AvgQueryTime = s.performanceMetrics.TotalQueryTime / time.Duration(s.performanceMetrics.QueryCount)
}

// recordCacheHit 记录缓存命中
func (s *OptimizedAnalyticsService) recordCacheHit() {
	s.performanceMetrics.mutex.Lock()
	defer s.performanceMetrics.mutex.Unlock()
	s.performanceMetrics.CacheHitCount++
}

// recordCacheMiss 记录缓存未命中
func (s *OptimizedAnalyticsService) recordCacheMiss() {
	s.performanceMetrics.mutex.Lock()
	defer s.performanceMetrics.mutex.Unlock()
	s.performanceMetrics.CacheMissCount++
}

// entityIDToString 将实体ID转换为字符串
func (s *OptimizedAnalyticsService) entityIDToString(entityId *int64) string {
	if entityId == nil {
		return "nil"
	}
	return fmt.Sprintf("%d", *entityId)
}