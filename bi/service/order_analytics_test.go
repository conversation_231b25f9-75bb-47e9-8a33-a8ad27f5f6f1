package service

import (
	"context"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	"hotel/bi/mysql"
	"hotel/bi/protocol"
)

func TestOrderAnalyticsService_GetOrderAnalytics(t *testing.T) {
	mockey.PatchConvey("OrderAnalytics主流程", t, func() {
		ctx := context.Background()
		orderDAO := mysql.NewOrderAnalyticsDAO(nil)
		service := NewOrderAnalyticsService(orderDAO)

		startDate := time.Now().AddDate(0, -1, 0)
		endDate := time.Now()

		mockOverview := &mysql.OrderMetrics{
			Date:            time.Now(),
			TotalOrders:     100,
			TotalRevenue:    1000000,
			TotalCost:       800000,
			TotalProfit:     200000,
			CompletedOrders: 80,
			CancelledOrders: 10,
			PendingOrders:   10,
			RevenueCurrency: "USD",
		}
		mockTrendData := []*mysql.OrderMetrics{
			{
				Date:            startDate,
				TotalOrders:     10,
				TotalRevenue:    100000,
				TotalProfit:     20000,
				CompletedOrders: 8,
				CancelledOrders: 1,
				RevenueCurrency: "USD",
			},
			{
				Date:            startDate.AddDate(0, 0, 1),
				TotalOrders:     15,
				TotalRevenue:    150000,
				TotalProfit:     30000,
				CompletedOrders: 12,
				CancelledOrders: 2,
				RevenueCurrency: "USD",
			},
		}
		mockStatusData := []*mysql.StatusMetrics{
			{Status: 1, Count: 20, Revenue: 200000, Currency: "USD"},
			{Status: 2, Count: 30, Revenue: 300000, Currency: "USD"},
			{Status: 5, Count: 40, Revenue: 400000, Currency: "USD"},
			{Status: 6, Count: 10, Revenue: 100000, Currency: "USD"},
		}
		mockCurrencyData := []*mysql.CurrencyMetrics{
			{Currency: "USD", Count: 60, Revenue: 600000},
			{Currency: "EUR", Count: 30, Revenue: 300000},
			{Currency: "CNY", Count: 10, Revenue: 100000},
		}
		mockEntityData := []*mysql.EntityMetrics{
			{EntityId: 1, EntityName: "Entity_1", OrderCount: 50, Revenue: 500000, Profit: 100000, Currency: "USD"},
			{EntityId: 2, EntityName: "Entity_2", OrderCount: 30, Revenue: 300000, Profit: 60000, Currency: "USD"},
			{EntityId: 3, EntityName: "Entity_3", OrderCount: 20, Revenue: 200000, Profit: 40000, Currency: "USD"},
		}
		mockMonthlyData := []*mysql.OrderMetrics{
			{
				Date:            time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				TotalOrders:     80,
				TotalRevenue:    800000,
				TotalProfit:     160000,
				RevenueCurrency: "USD",
			},
			{
				Date:            time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC),
				TotalOrders:     100,
				TotalRevenue:    1000000,
				TotalProfit:     200000,
				RevenueCurrency: "USD",
			},
		}
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetOrderOverview).Return(mockOverview, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetDailyTrend).Return(mockTrendData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetStatusBreakdown).Return(mockStatusData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetRevenueByCurrency).Return(mockCurrencyData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetRevenueByEntity).Return(mockEntityData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetMonthlyComparison).Return(mockMonthlyData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetEntityPerformance).Return(mockEntityData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetTopPerformingDays).Return(mockTrendData, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetOrdersByTags).Return(&mysql.TaggedOrderMetrics{}, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetRebookMetrics).Return(&mysql.RebookMetrics{}, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetSupplierTransfers).Return([]*mysql.SupplierTransfer{}, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetClientConfiguration).Return(&mysql.ClientConfigMetrics{}, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetRequestMetrics).Return(&mysql.RequestMetrics{}, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetResponseMetrics).Return(&mysql.ResponseMetrics{}, nil).Build()
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetSupplierBookingStats).Return([]*mysql.SupplierBookingStat{}, nil).Build()

		req := &protocol.OrderAnalyticsReq{
			StartDate:   &startDate,
			EndDate:     &endDate,
			Granularity: "day",
		}
		resp, err := service.GetOrderAnalytics(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Overview)
		assert.Equal(t, int64(100), resp.Overview.TotalOrders)
		assert.Equal(t, float64(1000000), resp.Overview.TotalRevenue.Amount)
		assert.Equal(t, "USD", resp.Overview.TotalRevenue.Currency)
		assert.Len(t, resp.TrendData, 2)
		assert.Len(t, resp.StatusBreakdown, 4)
		assert.NotNil(t, resp.RevenueAnalysis)
		assert.NotNil(t, resp.TopPerformers)
	})
}

func TestOrderAnalyticsService_GetRealTimeMetrics(t *testing.T) {
	mockey.PatchConvey("OrderAnalytics实时指标", t, func() {
		ctx := context.Background()
		orderDAO := mysql.NewOrderAnalyticsDAO(nil)
		service := NewOrderAnalyticsService(orderDAO)

		mockMetrics := &mysql.OrderMetrics{
			Date:            time.Now(),
			TotalOrders:     50,
			TotalRevenue:    500000,
			TotalCost:       400000,
			TotalProfit:     100000,
			CompletedOrders: 40,
			CancelledOrders: 5,
			PendingOrders:   5,
			RevenueCurrency: "USD",
		}
		mockey.Mock((*mysql.OrderAnalyticsDAO).GetRealTimeMetrics).Return(mockMetrics, nil).Build()

		req := &protocol.RealTimeMetricsReq{
			Metrics: []string{"todayOrders", "todayRevenue", "completionRate"},
		}
		resp, err := service.GetRealTimeMetrics(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Equal(t, int64(50), resp.TodayOrders)
		assert.Equal(t, float64(500000), resp.TodayRevenue.Amount)
		assert.Equal(t, "USD", resp.TodayRevenue.Currency)
		assert.Equal(t, int64(40), resp.ActiveBookings)
		assert.Equal(t, int64(5), resp.PendingOrders)
		assert.Equal(t, float64(80), resp.CompletionRate)
		assert.Equal(t, float64(10), resp.CancellationRate)
	})
}

func TestOrderAnalyticsService_GetStatusName(t *testing.T) {
	service := &OrderAnalyticsService{}

	tests := []struct {
		status   int64
		expected string
	}{
		{1, "已创建"},
		{2, "已支付"},
		{3, "等待确认"},
		{4, "已确认"},
		{5, "已完成"},
		{6, "已取消"},
		{7, "等待取消"},
		{8, "等待退款"},
		{999, "状态_999"},
	}

	for _, tt := range tests {
		result := service.getStatusName(tt.status)
		assert.Equal(t, tt.expected, result.Zh)
	}
}
