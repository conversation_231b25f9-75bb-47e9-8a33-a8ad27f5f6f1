package domain

import (
	"fmt"
	"github.com/spf13/cast"
	"strings"
)

type RatePkgId string

func (r RatePkgId) GetSupplierRatePkgId() string {
	_, pkgid := r.Decode()
	return pkgid
}
func (r RatePkgId) Decode() (Supplier, string) {
	return GetDecodedRatePkgId(string(r))
}
func (r RatePkgId) Encode(sup Supplier) RatePkgId {
	return RatePkgId(GetEncodedRatePkgId(sup, string(r)))
}
func GetEncodedRatePkgId(sup Supplier, ratePkgId string) string {
	return fmt.Sprintf("%d:%s", sup.Int64(), ratePkgId)
}
func GetDecodedRatePkgId(encodedRatePkgId string) (sup Supplier, ratePkgId string) {
	parts := strings.Split(encodedRatePkgId, ":")
	if len(parts) != 2 {
		return Supplier_UNKNOWN, encodedRatePkgId
	}
	return Supplier(cast.ToInt64(parts[0])), parts[1]
}
