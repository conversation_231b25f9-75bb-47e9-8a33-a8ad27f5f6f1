/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CancelOrderCancelOrderResponse struct for CancelOrderCancelOrderResponse
type CancelOrderCancelOrderResponse struct {
	// This value will not be returned if an error occurs.
	CancelResult CancelOrderCancelResult `json:"CancelResult,omitempty"`
	// This value will be returned if an error occurs.
	Error CommonError `json:"Error,omitempty"`
	ResponseHeader CommonResponseHeader `json:"ResponseHeader,omitempty"`
}
