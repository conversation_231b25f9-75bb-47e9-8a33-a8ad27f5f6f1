/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RatePredictionPriceRange struct for RatePredictionPriceRange
type RatePredictionPriceRange struct {
	// The currency code for the prices (e.g., USD, EUR).
	CurrencyCode string `json:"CurrencyCode,omitempty"`
	// The maximum price during the low price period.
	Max float32 `json:"Max,omitempty"`
	// The minimum price during the low price period.
	Min float32 `json:"Min,omitempty"`
}
