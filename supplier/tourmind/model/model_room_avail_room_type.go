/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomAvailRoomType struct for RoomAvailRoomType
type RoomAvailRoomType struct {
	// Bed type description.
	BedTypeDesc string `json:"BedTypeDesc,omitempty"`
	// Bed type description in Chinese.
	BedTypeDescCN string `json:"BedTypeDescCN,omitempty"`
	// Room type name.
	Name string `json:"Name,omitempty"`
	// Room type name in Chinese.
	NameCN string `json:"NameCN,omitempty"`
	// A list of rates for a room type; at least one rate will be returned.
	RateInfos []RoomAvailRateInfo `json:"RateInfos,omitempty"`
	// TourMind room type code, which is a unique identifier across all TourMind hotels.
	RoomTypeCode string `json:"RoomTypeCode,omitempty"`
}
