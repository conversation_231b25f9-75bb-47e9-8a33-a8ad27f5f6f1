/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomAvailRoomAvailRequest struct for RoomAvailRoomAvailRequest
type RoomAvailRoomAvailRequest struct {
	// Check-in date, format: “2006-01-02”.
	CheckIn string `json:"CheckIn,omitempty"`
	// Check-out date, format: “2006-01-02”.
	CheckOut string `json:"CheckOut,omitempty"`
	// TourMind Hotel ID; only one ID is required in this request.
	HotelCodes []int32 `json:"HotelCodes,omitempty"`
	// Nationality.
	Nationality string `json:"Nationality,omitempty"`
	// Request room occupancies. Note: Currently, only the same number of adults and children is supported for each room.
	PaxRooms []RoomAvailPaxRoomRq `json:"PaxRooms,omitempty"`
	// TourMind Rate ID.
	RateCode string `json:"RateCode,omitempty"`
	RequestHeader CommonRequestHeader `json:"RequestHeader,omitempty"`
}
