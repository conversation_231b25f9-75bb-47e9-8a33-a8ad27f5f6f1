/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomAvailPaxRoomRq struct for RoomAvailPaxRoomRq
type RoomAvailPaxRoomRq struct {
	// Number of adults per room.
	Adults int32 `json:"Adults,omitempty"`
	// Number of children per room.
	Children int32 `json:"Children,omitempty"`
	// Children's ages; the count of the array must equal the number of children.
	ChildrenAges []int32 `json:"ChildrenAges,omitempty"`
	// Room count.
	RoomCount int32 `json:"RoomCount,omitempty"`
}
