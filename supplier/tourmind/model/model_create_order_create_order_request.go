/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CreateOrderCreateOrderRequest struct for CreateOrderCreateOrderRequest
type CreateOrderCreateOrderRequest struct {
	// Unique agent reference ID to identify a booking; maximum length is 128 characters.
	AgentRefID string `json:"AgentRefID,omitempty"`
	// Check-in date, format: “2006-01-02”.
	CheckIn string `json:"CheckIn,omitempty"`
	// Check-out date, format: “2006-01-02”.
	CheckOut string `json:"CheckOut,omitempty"`
	// Contact information for the booking.
	ContactInfo CreateOrderContactInfo `json:"ContactInfo,omitempty"`
	// Currency code.
	CurrencyCode string `json:"CurrencyCode,omitempty"`
	// TourMind Hotel ID; only one ID is required in this request.
	HotelCode int32 `json:"HotelCode,omitempty"`
	// Request room occupancies. Note: Currently, only the same number of adults and children is supported for each room; for multiple rooms, you only need to fill in one object.
	PaxRooms []CreateOrderPaxRoom `json:"PaxRooms,omitempty"`
	// TourMind Rate ID.
	RateCode string `json:"RateCode,omitempty"`
	RequestHeader CommonRequestHeader `json:"RequestHeader,omitempty"`
	// Special customer requests.
	SpecialRequest string `json:"SpecialRequest,omitempty"`
	// Total price for the booking returned from the CheckRoomRateResponse.
	TotalPrice float32 `json:"TotalPrice,omitempty"`
}
