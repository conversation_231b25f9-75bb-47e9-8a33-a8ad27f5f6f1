# RoomMatchRoomMatchReq

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AppKey** | **string** |  | [optional] 
**EnableDeepThinking** | **bool** |  | [optional] 
**MasterRoomList** | [**[]RoomMatchMasterRoomListReq**](RoomMatchMasterRoomListReq.md) |  | [optional] 
**PropertyInfo** | [**RoomMatchPropertyInfoReq**](RoomMatchPropertyInfoReq.md) |  | [optional] 
**RequestHeader** | [**CommonRequestHeader**](CommonRequestHeader.md) |  | [optional] 
**SupplierRoomList** | [**[]RoomMatchSupplierRoomListReq**](RoomMatchSupplierRoomListReq.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


