# CancelOrderCancelResult

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CancelFee** | **float32** | Cancellation penalty fee charged. | [optional] 
**CurrencyCode** | **string** | Currency code for the charge amount. | [optional] 
**OrderStatus** | **string** | Order status. &lt;br&gt;&lt;table&gt;&lt;tr&gt;&lt;th&gt;OrderStatus&lt;/th&gt;&lt;th&gt;Description&lt;/th&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;PENDING&lt;/td&gt;&lt;td&gt;Booking confirmation pending.&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;CONFIRMED&lt;/td&gt;&lt;td&gt;Booking confirmed.&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;CANCELLED&lt;/td&gt;&lt;td&gt;Booking cancelled.&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td&gt;FAILED&lt;/td&gt;&lt;td&gt;Booking confirmation failed.&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt; | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


