# CreateOrderCreateOrderRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**AgentRefID** | **string** | Unique agent reference ID to identify a booking; maximum length is 128 characters. | [optional] 
**CheckIn** | **string** | Check-in date, format: “2006-01-02”. | [optional] 
**CheckOut** | **string** | Check-out date, format: “2006-01-02”. | [optional] 
**ContactInfo** | [**CreateOrderContactInfo**](CreateOrderContactInfo.md) | Contact information for the booking. | [optional] 
**CurrencyCode** | **string** | Currency code. | [optional] 
**HotelCode** | **int32** | TourMind Hotel ID; only one ID is required in this request. | [optional] 
**PaxRooms** | [**[]CreateOrderPaxRoom**](CreateOrderPaxRoom.md) | Request room occupancies. Note: Currently, only the same number of adults and children is supported for each room; for multiple rooms, you only need to fill in one object. | [optional] 
**RateCode** | **string** | TourMind Rate ID. | [optional] 
**RequestHeader** | [**CommonRequestHeader**](CommonRequestHeader.md) |  | [optional] 
**SpecialRequest** | **string** | Special customer requests. | [optional] 
**TotalPrice** | **float32** | Total price for the booking returned from the CheckRoomRateResponse. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


