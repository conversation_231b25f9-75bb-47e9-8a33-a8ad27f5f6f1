# RatePredictionRatePredictionReq

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CheckIn** | **string** | Check-in date (ISO 8601 format) | [optional] 
**CheckOut** | **string** | Check-out date (ISO 8601 format) | [optional] 
**CurrencyCode** | **string** | Preferred currency code | [optional] 
**Hotels** | [**[]RatePredictionHotel**](RatePredictionHotel.md) | List of hotels to search | [optional] 
**PaxRooms** | [**[]RatePredictionPaxRoom**](RatePredictionPaxRoom.md) | Room occupancy details | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


