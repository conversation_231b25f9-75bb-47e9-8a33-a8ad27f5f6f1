# \AvailabilityBookingApi

All URIs are relative to *http://localhost*

Method | HTTP request | Description
------------- | ------------- | -------------
[**HotelDetail**](AvailabilityBookingApi.md#HotelDetail) | **Post** /v2/HotelDetail | Detailed Hotel Pricing Information
[**HotelDetail_0**](AvailabilityBookingApi.md#HotelDetail_0) | **Post** /v2/HotelDetail | Detailed Hotel Pricing Information



## HotelDetail

> HotelDetailHotelDetailResponse HotelDetail(ctx, optional)

Detailed Hotel Pricing Information

Search hotel room rates and available inventory for a specific property, enabling users to view detailed room availability and pricing information.,[Field updates.](#tag/Attachments)

### Required Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***HotelDetailOpts** | optional parameters | nil if no parameters

### Optional Parameters

Optional parameters are passed through a pointer to a HotelDetailOpts struct


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **hotelDetailHotelDetailRequest** | [**optional.Interface of HotelDetailHotelDetailRequest**](HotelDetailHotelDetailRequest.md)|  | 

### Return type

[**HotelDetailHotelDetailResponse**](HotelDetailHotelDetailResponse.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## HotelDetail_0

> HotelDetailHotelDetailResponse HotelDetail_0(ctx, optional)

Detailed Hotel Pricing Information

Search hotel room rates and available inventory for a specific property, enabling users to view detailed room availability and pricing information.,[Field updates.](#tag/Attachments)

### Required Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
 **optional** | ***HotelDetail_1Opts** | optional parameters | nil if no parameters

### Optional Parameters

Optional parameters are passed through a pointer to a HotelDetail_1Opts struct


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **hotelDetailHotelDetailRequest** | [**optional.Interface of HotelDetailHotelDetailRequest**](HotelDetailHotelDetailRequest.md)|  | 

### Return type

[**HotelDetailHotelDetailResponse**](HotelDetailHotelDetailResponse.md)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)

