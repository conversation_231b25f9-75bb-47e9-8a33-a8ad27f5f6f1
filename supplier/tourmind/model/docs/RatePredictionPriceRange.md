# RatePredictionPriceRange

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CurrencyCode** | **string** | The currency code for the prices (e.g., USD, EUR). | [optional] 
**Max** | **float32** | The maximum price during the low price period. | [optional] 
**Min** | **float32** | The minimum price during the low price period. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


