# RoomAvailRoomAvailRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CheckIn** | **string** | Check-in date, format: “2006-01-02”. | [optional] 
**CheckOut** | **string** | Check-out date, format: “2006-01-02”. | [optional] 
**HotelCodes** | **[]int32** | TourMind Hotel ID; only one ID is required in this request. | [optional] 
**Nationality** | **string** | Nationality. | [optional] 
**PaxRooms** | [**[]RoomAvailPaxRoomRq**](RoomAvailPaxRoomRq.md) | Request room occupancies. Note: Currently, only the same number of adults and children is supported for each room. | [optional] 
**RateCode** | **string** | TourMind Rate ID. | [optional] 
**RequestHeader** | [**CommonRequestHeader**](CommonRequestHeader.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


