# HotelDetailHotelDetailRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CheckIn** | **string** | Check-in date, format: “2006-01-02”. | [optional] 
**CheckOut** | **string** | Check-out date, format: “2006-01-02”. | [optional] 
**HotelCodes** | **[]int32** | TourMind hotel IDs; up to 20 IDs are supported in this request. | [optional] 
**IsDailyPrice** | **bool** | If true, daily price information will be included in the response. Default: false. | [optional] 
**Nationality** | **string** | Nationality. | [optional] 
**PaxRooms** | [**[]HotelDetailPaxRoomRq**](HotelDetailPaxRoomRq.md) | Request room occupancies. Note: Currently, only the same number of adults and children is supported for each room; for multiple rooms, you only need to fill in one object. | [optional] 
**RequestHeader** | [**CommonRequestHeader**](CommonRequestHeader.md) |  | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


