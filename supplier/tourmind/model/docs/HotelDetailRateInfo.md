# HotelDetailRateInfo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Allotment** | **int32** | Available room inventory count. | [optional] 
**CancelPolicyInfos** | [**[]HotelDetailCancelPolicyInfo**](HotelDetailCancelPolicyInfo.md) | Cancellation policy information. | [optional] 
**CurrencyCode** | **string** | Currency code. | [optional] 
**DailyPriceInfo** | [**[]HotelDetailDailyPriceInfo**](HotelDetailDailyPriceInfo.md) | Daily pricing information for the hotel. | [optional] 
**InvoiceInfo** | **int32** | 发票信息: 1: Hotel.开票, 2: 途灵开票 | [optional] 
**MealInfo** | [**HotelDetailMealInfo**](HotelDetailMealInfo.md) | Meal information. | [optional] 
**Name** | **string** | Additional rate description, such as meal type description. | [optional] 
**NameCN** | **string** | Additional rate description in Chinese, such as meal type description. | [optional] 
**RateCode** | **string** | Rate identifier for a sellable product; this code is unique across all TourMind hotels. | [optional] 
**Refundable** | **bool** |  | [optional] 
**TotalPrice** | **float32** | Total price; this is the total amount charged. | [optional] 
**BedTypeDesc** | **string** | Bed type description. | [optional] 
**BedTypeDescCN** | **string** | Bed type description in Chinese. | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


