/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RatePredictionDateRange struct for RatePredictionDateRange
type RatePredictionDateRange struct {
	// The end date of the low price period in ISO 8601 format.
	End string `json:"End,omitempty"`
	// The start date of the low price period in ISO 8601 format.
	Start string `json:"Start,omitempty"`
}
