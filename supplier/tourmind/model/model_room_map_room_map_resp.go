/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomMapRoomMapResp struct for RoomMapRoomMapResp
type RoomMapRoomMapResp struct {
	Aggregation []RoomMapAggregation `json:"Aggregation,omitempty"`
	// A unique identifier for the property.
	PropertyId string `json:"PropertyId,omitempty"`
	// The name of the property (hotel) being referenced.
	PropertyName string `json:"PropertyName,omitempty"`
	// A list of unmapped rooms or groups, if any.
	Unmapped []string `json:"Unmapped,omitempty"`
}
