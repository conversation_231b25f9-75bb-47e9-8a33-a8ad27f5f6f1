/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomMatchRoomMatchResp struct for RoomMatchRoomMatchResp
type RoomMatchRoomMatchResp struct {
	Code int32 `json:"Code,omitempty"`
	Data RoomMatchRoomMatchRespData `json:"Data,omitempty"`
	Error CommonError `json:"Error,omitempty"`
	ResponseHeader CommonResponseHeader `json:"ResponseHeader,omitempty"`
	Success bool `json:"Success,omitempty"`
	UnmatchedRooms []RoomMatchMatchedRoomDetailResp `json:"UnmatchedRooms,omitempty"`
}
