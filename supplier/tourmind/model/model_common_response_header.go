/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CommonResponseHeader struct for CommonResponseHeader
type CommonResponseHeader struct {
	// Response timestamp, format: “2006-01-02 15:04:05”.
	ResponseTime string `json:"ResponseTime,omitempty"`
	// This value must match the TransactionID in the request..
	TransactionID string `json:"TransactionID,omitempty"`
}
