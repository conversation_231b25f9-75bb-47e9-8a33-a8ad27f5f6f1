/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomAvailRoomAvailResponse struct for RoomAvailRoomAvailResponse
type RoomAvailRoomAvailResponse struct {
	// This value will be returned if an error occurs.
	Error CommonError `json:"Error,omitempty"`
	// Hotel information.
	Hotels []RoomAvailHotel `json:"Hotels,omitempty"`
	ResponseHeader CommonResponseHeader `json:"ResponseHeader,omitempty"`
}
