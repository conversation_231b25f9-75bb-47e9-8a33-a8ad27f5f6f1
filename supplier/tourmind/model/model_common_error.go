/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CommonError struct for CommonError
type CommonError struct {
	// Error code. <br><table><tr><th>ErrorCode</th><th>Description</th></tr><tr><td>101</td><td>No payload in the request.</td></tr><tr><td>102</td><td>Invalid format of the request.</td></tr><tr><td>103</td><td>Request data validation failed.</td></tr><tr><td>104</td><td>Service error.</td></tr><tr><td>105</td><td>API user authentication error; invalid AgentCode, Username, or Password.</td></tr></table>
	ErrorCode string `json:"ErrorCode,omitempty"`
	// Error message.
	ErrorMessage string `json:"ErrorMessage,omitempty"`
}
