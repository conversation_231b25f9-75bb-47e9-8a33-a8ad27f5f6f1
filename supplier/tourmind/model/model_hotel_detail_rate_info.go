/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelDetailRateInfo struct for HotelDetailRateInfo
type HotelDetailRateInfo struct {
	// Available room inventory count.
	Allotment int32 `json:"Allotment,omitempty"`
	// Cancellation policy information.
	CancelPolicyInfos []HotelDetailCancelPolicyInfo `json:"CancelPolicyInfos,omitempty"`
	// Currency code.
	CurrencyCode string `json:"CurrencyCode,omitempty"`
	// Daily pricing information for the hotel.
	DailyPriceInfo []HotelDetailDailyPriceInfo `json:"DailyPriceInfo,omitempty"`
	// 发票信息: 1: Hotel.开票, 2: 途灵开票
	InvoiceInfo int32 `json:"InvoiceInfo,omitempty"`
	// Meal information.
	MealInfo HotelDetailMealInfo `json:"MealInfo,omitempty"`
	// Additional rate description, such as meal type description.
	Name string `json:"Name,omitempty"`
	// Additional rate description in Chinese, such as meal type description.
	NameCN string `json:"NameCN,omitempty"`
	// Rate identifier for a sellable product; this code is unique across all TourMind hotels.
	RateCode string `json:"RateCode,omitempty"`
	Refundable bool `json:"Refundable,omitempty"`
	// Total price; this is the total amount charged.
	TotalPrice float32 `json:"TotalPrice,omitempty"`
	// Bed type description.
	BedTypeDesc string `json:"bedTypeDesc,omitempty"`
	// Bed type description in Chinese.
	BedTypeDescCN string `json:"bedTypeDescCN,omitempty"`
}
