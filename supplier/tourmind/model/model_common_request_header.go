/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CommonRequestHeader struct for CommonRequestHeader
type CommonRequestHeader struct {
	// Unique code for the agent provided by TourMind.
	AgentCode string `json:"AgentCode,omitempty"`
	// Password for the API request.
	Password string `json:"Password,omitempty"`
	// Request timestamp, format: “2006-01-02 15:04:05”.
	RequestTime string `json:"RequestTime,omitempty"`
	// Identifier for tracing API requests, such as using a GUID.
	TransactionID string `json:"TransactionID,omitempty"`
	// Username for the API request.
	UserName string `json:"UserName,omitempty"`
}
