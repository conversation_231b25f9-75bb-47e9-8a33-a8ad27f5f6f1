/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelDetailCancelPolicyInfo struct for HotelDetailCancelPolicyInfo
type HotelDetailCancelPolicyInfo struct {
	// Cancellation charge amount.
	Amount float32 `json:"Amount,omitempty"`
	// Currency for the cancellation charge amount.
	CurrencyCode string `json:"CurrencyCode,omitempty"`
	// Cancellation policy window end, format: “2006-01-02“.
	EndDateTime string `json:"EndDateTime,omitempty"`
	// 开始时间的具体的时间 格式： “2006-01-02 15:04:05”
	From string `json:"From,omitempty"`
	// Cancellation policy window start, format: “2006-01-02“.
	StartDateTime string `json:"StartDateTime,omitempty"`
	// 结束时间的具体的时间 格式： “2006-01-02 15:04:05”
	To string `json:"To,omitempty"`
}
