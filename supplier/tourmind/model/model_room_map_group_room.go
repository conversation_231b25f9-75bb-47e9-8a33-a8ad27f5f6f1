/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomMapGroupRoom struct for RoomMapGroupRoom
type RoomMapGroupRoom struct {
	// The unique identifier for the room.
	RoomIds string `json:"RoomIds,omitempty"`
	// The name of the individual room within the group.
	RoomName string `json:"RoomName,omitempty"`
	// The identifier for the supplier providing the room.
	SupplierId string `json:"SupplierId,omitempty"`
}
