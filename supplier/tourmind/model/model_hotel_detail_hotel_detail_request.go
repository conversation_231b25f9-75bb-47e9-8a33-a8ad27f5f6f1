/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelDetailHotelDetailRequest struct for HotelDetailHotelDetailRequest
type HotelDetailHotelDetailRequest struct {
	// Check-in date, format: “2006-01-02”.
	CheckIn string `json:"CheckIn,omitempty"`
	// Check-out date, format: “2006-01-02”.
	CheckOut string `json:"CheckOut,omitempty"`
	// TourMind hotel IDs; up to 20 IDs are supported in this request.
	HotelCodes []int32 `json:"HotelCodes,omitempty"`
	// If true, daily price information will be included in the response. Default: false.
	IsDailyPrice bool `json:"IsDailyPrice,omitempty"`
	// Nationality.
	Nationality string `json:"Nationality,omitempty"`
	// Request room occupancies. Note: Currently, only the same number of adults and children is supported for each room; for multiple rooms, you only need to fill in one object.
	PaxRooms []HotelDetailPaxRoomRq `json:"PaxRooms,omitempty"`
	RequestHeader CommonRequestHeader `json:"RequestHeader,omitempty"`
}
