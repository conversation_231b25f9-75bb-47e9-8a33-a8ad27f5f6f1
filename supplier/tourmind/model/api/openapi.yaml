openapi: 3.0.1
info:
  title: TourMind API
  version: 1.0.0
servers:
- url: /
tags:
- name: Availability & Booking
- name: Hotel Content
- name: AI Room Matching & Mapping
- name: AI Rate Prediction
paths:
  /v2/HotelDetail:
    post:
      deprecated: false
      description: Search hotel room rates and available inventory for a specific
        property, enabling users to view detailed room availability and pricing information.,[Field
        updates.](#tag/Attachments)
      operationId: HotelDetail
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelDetailHotelDetailRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelDetailHotelDetailResponse'
          description: Hotel details.
          x-apifox-name: 成功
      security: []
      summary: Detailed Hotel Pricing Information
      tags:
      - Availability & Booking
      - Availability & Booking
      x-apifox-folder: Availability & Booking
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259943-run
  /v2/CheckRoomRate:
    post:
      deprecated: false
      description: Check the availability and price of a specific room rate selected,
        usually requested when the user selects a room to book and submits a booking.
        <br><br>
      operationId: CheckRoomRate
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoomAvailRoomAvailRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoomAvailRoomAvailResponse'
          description: Check Room Rate
          x-apifox-name: 成功
      security: []
      summary: Check Room Rate
      tags:
      - Availability & Booking
      - Availability & Booking
      x-apifox-folder: Availability & Booking
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259944-run
  /v2/CreateOrder:
    post:
      deprecated: false
      description: Create Order
      operationId: CreateOrder
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderCreateOrderRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderCreateOrderResponse'
          description: Create Order
          x-apifox-name: 成功
      security: []
      summary: Create Order
      tags:
      - Availability & Booking
      - Availability & Booking
      x-apifox-folder: Availability & Booking
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259945-run
  /v2/CancelOrder:
    post:
      deprecated: false
      description: Send a cancel order request to cancel refundable and pending bookings.
      operationId: CancelOrder
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOrderCancelOrderRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelOrderCancelOrderResponse'
          description: Cancel Order
          x-apifox-name: 成功
      security: []
      summary: Cancel Order
      tags:
      - Availability & Booking
      - Availability & Booking
      x-apifox-folder: Availability & Booking
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259946-run
  /v2/SearchOrder:
    post:
      deprecated: false
      description: Search for an order to check its status in the booking process.
      operationId: SearchOrder
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchOrderQueryOrderRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchOrderQueryOrderResponse'
          description: Search Order
          x-apifox-name: 成功
      security: []
      summary: Search Order
      tags:
      - Availability & Booking
      - Availability & Booking
      x-apifox-folder: Availability & Booking
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259947-run
  /v2/HotelStaticList:
    post:
      deprecated: false
      description: Retrieve static data for hotels.
      operationId: HotelStaticList
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HotelstaticHotelStaticListRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HotelstaticHotelStaticListResponse'
          description: Hotel static information.
          x-apifox-name: 成功
      security: []
      summary: Hotel List
      tags:
      - Hotel Content
      - Hotel Content
      x-apifox-folder: Hotel Content
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259948-run
  /v2/RegionList:
    post:
      deprecated: false
      description: Retrieve the list of regions.
      operationId: RegionList
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegionListRegionListRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegionListRegionListResponse'
          description: Region List
          x-apifox-name: 成功
      security: []
      summary: Region List
      tags:
      - Hotel Content
      - Hotel Content
      x-apifox-folder: Hotel Content
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259949-run
  /v2/RoomStaticList:
    post:
      deprecated: false
      description: Retrieve the list of room information of sepecific hotels.
      operationId: RoomTypeStatic
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoomStaticModelRoomStaticRequest'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoomStaticModelRoomStaticResponse'
          description: Hotel room data
          x-apifox-name: 成功
      security: []
      summary: Hotel Room Type List
      tags:
      - Hotel Content
      - Hotel Content
      x-apifox-folder: Hotel Content
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259950-run
  /v2/RoomMap:
    post:
      deprecated: false
      description: The Room Map API aggregates and categorizes comparable rooms from
        various suppliers for a specific property. It processes room data and organizes
        rooms with similar attributes into unified groups. <br><br> If you do not
        have a self-maintained standardized room type list, the RoomMap API can be
        employed to derive a set of recommended standard room types based on historical
        data analysis, serving as an effective way for room type classification. <br><br>![room_map](../imgs/room_map.png)
      operationId: AI Room Map
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoomMapRoomMapReq'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoomMapRoomMapResp'
          description: OK
          x-apifox-name: 成功
      security: []
      summary: AI Room Map
      tags:
      - AI Room Matching & Mapping
      - AI Room Matching & Mapping
      x-apifox-folder: AI Room Matching & Mapping
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259951-run
  /v2/RoomMatch:
    post:
      deprecated: false
      description: 'The Room Match API provides an intelligent room matching service
        that establishes relationships between reference hotel rooms and supplier
        room listings. By AI algorithm, it automatically matches standard room types
        from reference hotels with room information provided by different suppliers.
        <br><br>If you already maintained your own standardized room types, it is
        recommended to utilize the Room Match feature to directly leverage their curated
        data for accurate room type matching.  <br><br>![room_match](../imgs/room_match.png)
        <br><br> <div style="height: 100vh; width: 100%;"><iframe src="http://developers.tourmind.cn:30903"
        style="width: 100%; height: 100%; border: none;"></iframe></div>'
      operationId: AI Room Match
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoomMatchRoomMatchReq'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoomMatchRoomMatchResp'
          description: RoomMatchResp
          x-apifox-name: 成功
      security: []
      summary: AI Room Match
      tags:
      - AI Room Matching & Mapping
      - AI Room Matching & Mapping
      x-apifox-folder: AI Room Matching & Mapping
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259952-run
  /v2/RatePrediction:
    post:
      deprecated: false
      description: The Price Prediction API provides a robust solution for forecasting
        future pricing trends for hotel room types. This API is designed to help users
        and businesses in the hospitality sector make informed decisions regarding
        pricing strategies by offering insights into expected low and high price ranges
        for various hotel room types over specified time periods. <br><br> ![rate_prediction](../imgs/rate_prediction.png)
      operationId: AI Rate Prediction
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RatePredictionRatePredictionReq'
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RatePredictionRatePredictionResp'
          description: RatePredictionResp
          x-apifox-name: 成功
      security: []
      summary: AI Rate Prediction
      tags:
      - AI Rate Prediction
      - AI Rate Prediction
      x-apifox-folder: AI Rate Prediction
      x-apifox-status: released
      x-run-in-apifox: https://apifox.com/web/project/6803146/apis/api-324259953-run
components:
  schemas:
    CancelOrderCancelOrderRequest:
      properties:
        AgentRefID:
          description: Agent reference ID provided in the CreateOrder request by the
            agent; maximum length is 128.
          type: string
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - AgentRefID
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CancelOrderCancelOrderResponse:
      properties:
        CancelResult:
          allOf:
          - $ref: '#/components/schemas/CancelOrderCancelResult'
          description: This value will not be returned if an error occurs.
        Error:
          allOf:
          - $ref: '#/components/schemas/CommonError'
          description: This value will be returned if an error occurs.
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - CancelResult
      - Error
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CancelOrderCancelResult:
      properties:
        CancelFee:
          description: Cancellation penalty fee charged.
          type: number
        CurrencyCode:
          description: Currency code for the charge amount.
          type: string
        OrderStatus:
          description: Order status. <br><table><tr><th>OrderStatus</th><th>Description</th></tr><tr><td>PENDING</td><td>Booking
            confirmation pending.</td></tr><tr><td>CONFIRMED</td><td>Booking confirmed.</td></tr><tr><td>CANCELLED</td><td>Booking
            cancelled.</td></tr><tr><td>FAILED</td><td>Booking confirmation failed.</td></tr></table>
          type: string
      type: object
      x-apifox-orders:
      - CancelFee
      - CurrencyCode
      - OrderStatus
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CommonError:
      properties:
        ErrorCode:
          description: Error code. <br><table><tr><th>ErrorCode</th><th>Description</th></tr><tr><td>101</td><td>No
            payload in the request.</td></tr><tr><td>102</td><td>Invalid format of
            the request.</td></tr><tr><td>103</td><td>Request data validation failed.</td></tr><tr><td>104</td><td>Service
            error.</td></tr><tr><td>105</td><td>API user authentication error; invalid
            AgentCode, Username, or Password.</td></tr></table>
          type: string
        ErrorMessage:
          description: Error message.
          type: string
      type: object
      x-apifox-orders:
      - ErrorCode
      - ErrorMessage
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CommonRequestHeader:
      properties:
        AgentCode:
          description: Unique code for the agent provided by TourMind.
          type: string
        Password:
          description: Password for the API request.
          type: string
        RequestTime:
          description: 'Request timestamp, format: “2006-01-02 15:04:05”.'
          type: string
        TransactionID:
          description: Identifier for tracing API requests, such as using a GUID.
          type: string
        UserName:
          description: Username for the API request.
          type: string
      type: object
      x-apifox-orders:
      - AgentCode
      - Password
      - RequestTime
      - TransactionID
      - UserName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CommonResponseHeader:
      properties:
        ResponseTime:
          description: 'Response timestamp, format: “2006-01-02 15:04:05”.'
          type: string
        TransactionID:
          description: This value must match the TransactionID in the request..
          type: string
      type: object
      x-apifox-orders:
      - ResponseTime
      - TransactionID
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CreateOrderContactInfo:
      properties:
        Email:
          description: Email of the booking contact.
          type: string
        FirstName:
          description: First Name of the booking contact.
          type: string
        LastName:
          description: Last Name of the booking contact.
          type: string
        PhoneNo:
          description: Phone number of the booking contact.
          type: string
      type: object
      x-apifox-orders:
      - Email
      - FirstName
      - LastName
      - PhoneNo
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CreateOrderCreateOrderRequest:
      properties:
        AgentRefID:
          description: Unique agent reference ID to identify a booking; maximum length
            is 128 characters.
          type: string
        CheckIn:
          description: 'Check-in date, format: “2006-01-02”.'
          type: string
        CheckOut:
          description: 'Check-out date, format: “2006-01-02”.'
          type: string
        ContactInfo:
          allOf:
          - $ref: '#/components/schemas/CreateOrderContactInfo'
          description: Contact information for the booking.
        CurrencyCode:
          description: Currency code.
          type: string
        HotelCode:
          description: TourMind Hotel ID; only one ID is required in this request.
          type: integer
        PaxRooms:
          description: 'Request room occupancies. Note: Currently, only the same number
            of adults and children is supported for each room; for multiple rooms,
            you only need to fill in one object.'
          items:
            $ref: '#/components/schemas/CreateOrderPaxRoom'
          type: array
        RateCode:
          description: TourMind Rate ID.
          type: string
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
        SpecialRequest:
          description: Special customer requests.
          type: string
        TotalPrice:
          description: Total price for the booking returned from the CheckRoomRateResponse.
          type: number
      type: object
      x-apifox-orders:
      - AgentRefID
      - CheckIn
      - CheckOut
      - ContactInfo
      - CurrencyCode
      - HotelCode
      - PaxRooms
      - RateCode
      - RequestHeader
      - SpecialRequest
      - TotalPrice
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CreateOrderCreateOrderResponse:
      properties:
        Error:
          allOf:
          - $ref: '#/components/schemas/CommonError'
          description: This value will be returned if an error occurs.
        OrderInfo:
          allOf:
          - $ref: '#/components/schemas/CreateOrderOrderInfo'
          description: This value will not be returned if an error occurs.
        ReservationID:
          description: Tourmind order ID
          type: string
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - Error
      - OrderInfo
      - ReservationID
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CreateOrderOrderInfo:
      properties:
        OrderStatus:
          description: Order status. <br><table><tr><th>OrderStatus</th><th>Description</th></tr><tr><td>PENDING</td><td>Booking
            confirmation pending.</td></tr><tr><td>CONFIRMED</td><td>Booking confirmed.</td></tr><tr><td>CANCELLED</td><td>Booking
            cancelled.</td></tr><tr><td>FAILED</td><td>Booking confirmation failed.</td></tr></table>
          type: string
        ReservationID:
          description: Tourmind order ID
          type: string
      type: object
      x-apifox-orders:
      - OrderStatus
      - ReservationID
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CreateOrderPaxName:
      properties:
        FirstName:
          type: string
        LastName:
          type: string
        Type:
          description: ADU 代表Adult.，CHI代表Child.
          type: string
      type: object
      x-apifox-orders:
      - FirstName
      - LastName
      - Type
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    CreateOrderPaxRoom:
      properties:
        Adults:
          type: integer
        Children:
          type: integer
        ChildrenAges:
          description: Children's ages; the count of the array must equal the number
            of children.
          items:
            type: integer
          type: array
        PaxNames:
          items:
            $ref: '#/components/schemas/CreateOrderPaxName'
          type: array
        RoomCount:
          type: integer
      type: object
      x-apifox-orders:
      - Adults
      - Children
      - ChildrenAges
      - PaxNames
      - RoomCount
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailCancelPolicyInfo:
      properties:
        Amount:
          description: Cancellation charge amount.
          type: number
        CurrencyCode:
          description: Currency for the cancellation charge amount.
          type: string
        EndDateTime:
          description: 'Cancellation policy window end, format: “2006-01-02“.'
          type: string
        From:
          description: 开始时间的具体的时间 格式： “2006-01-02 15:04:05”
          type: string
        StartDateTime:
          description: 'Cancellation policy window start, format: “2006-01-02“.'
          type: string
        To:
          description: 结束时间的具体的时间 格式： “2006-01-02 15:04:05”
          type: string
      type: object
      x-apifox-orders:
      - Amount
      - CurrencyCode
      - EndDateTime
      - From
      - StartDateTime
      - To
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailDailyPriceInfo:
      properties:
        Count:
          description: this is daily inventory count
          type: integer
        Date:
          description: '日期，格式: “2006-01-02”'
          type: string
        Price:
          description: Price of a specific date.  单间房子 单晚的Price of a specific date.
          type: number
      type: object
      x-apifox-orders:
      - Count
      - Date
      - Price
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailHotel:
      properties:
        Address:
          description: Hotel address.
          type: string
        CheckIn:
          description: 'Check-in date, format: “2006-01-02”.'
          type: string
        CheckOut:
          description: 'Check-out date, format: “2006-01-02”.'
          type: string
        HotelCode:
          description: Hotel code.
          type: string
        Name:
          description: Hotel name.
          type: string
        RoomTypes:
          description: Room types available based on the search criteria.
          items:
            $ref: '#/components/schemas/HotelDetailRoomType'
          type: array
      type: object
      x-apifox-orders:
      - Address
      - CheckIn
      - CheckOut
      - HotelCode
      - Name
      - RoomTypes
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailHotelDetailRequest:
      properties:
        CheckIn:
          description: 'Check-in date, format: “2006-01-02”.'
          type: string
        CheckOut:
          description: 'Check-out date, format: “2006-01-02”.'
          type: string
        HotelCodes:
          description: TourMind hotel IDs; up to 20 IDs are supported in this request.
          items:
            type: integer
          type: array
        IsDailyPrice:
          description: 'If true, daily price information will be included in the response.
            Default: false.'
          type: boolean
        Nationality:
          description: Nationality.
          type: string
        PaxRooms:
          description: 'Request room occupancies. Note: Currently, only the same number
            of adults and children is supported for each room; for multiple rooms,
            you only need to fill in one object.'
          items:
            $ref: '#/components/schemas/HotelDetailPaxRoomRq'
          type: array
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - CheckIn
      - CheckOut
      - HotelCodes
      - IsDailyPrice
      - Nationality
      - PaxRooms
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailHotelDetailResponse:
      properties:
        Error:
          allOf:
          - $ref: '#/components/schemas/CommonError'
          description: This value will be returned if an error occurs.
        Hotels:
          description: Hotel information.
          items:
            $ref: '#/components/schemas/HotelDetailHotel'
          type: array
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - Error
      - Hotels
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailMealInfo:
      properties:
        MealCount:
          description: Number of free meals offered.
          type: integer
        MealType:
          description: Meal type <br><table><tr><th>MealType</th><th>Description</th></tr><tr><td>1</td><td>No
            Breakfast</td></tr><tr><td>2</td><td>Breakfast</td></tr><tr><td>3</td><td>Lunch</td></tr><tr><td>4</td><td>Dinner</td></tr><tr><td>5</td><td>Lunch
            and Dinner</td></tr><tr><td>6</td><td>HalfBoard</td></tr><tr><td>7</td><td>FullBoard</td></tr><tr><td>8</td><td>AllInclusive</td></tr><tr><td>9</td><td>SelfCatering</td></tr></table>
          type: string
      type: object
      x-apifox-orders:
      - MealCount
      - MealType
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailPaxRoomRq:
      properties:
        Adults:
          description: Number of adults per room.
          type: integer
        Children:
          description: Number of children per room.
          type: integer
        ChildrenAges:
          description: Children's ages; the count of the array must equal the number
            of children.
          items:
            type: integer
          type: array
        RoomCount:
          description: Room count.
          type: integer
      type: object
      x-apifox-orders:
      - Adults
      - Children
      - ChildrenAges
      - RoomCount
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailRateInfo:
      properties:
        Allotment:
          description: Available room inventory count.
          type: integer
        CancelPolicyInfos:
          description: Cancellation policy information.
          items:
            $ref: '#/components/schemas/HotelDetailCancelPolicyInfo'
          type: array
        CurrencyCode:
          description: Currency code.
          type: string
        DailyPriceInfo:
          description: Daily pricing information for the hotel.
          items:
            $ref: '#/components/schemas/HotelDetailDailyPriceInfo'
          type: array
        InvoiceInfo:
          description: '发票信息: 1: Hotel.开票, 2: 途灵开票'
          type: integer
        MealInfo:
          allOf:
          - $ref: '#/components/schemas/HotelDetailMealInfo'
          description: Meal information.
        Name:
          description: Additional rate description, such as meal type description.
          type: string
        NameCN:
          description: Additional rate description in Chinese, such as meal type description.
          type: string
        RateCode:
          description: Rate identifier for a sellable product; this code is unique
            across all TourMind hotels.
          type: string
        Refundable:
          type: boolean
        TotalPrice:
          description: Total price; this is the total amount charged.
          type: number
        bedTypeDesc:
          description: Bed type description.
          type: string
        bedTypeDescCN:
          description: Bed type description in Chinese.
          type: string
      type: object
      x-apifox-orders:
      - Allotment
      - CancelPolicyInfos
      - CurrencyCode
      - DailyPriceInfo
      - InvoiceInfo
      - MealInfo
      - Name
      - NameCN
      - RateCode
      - Refundable
      - TotalPrice
      - bedTypeDesc
      - bedTypeDescCN
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelDetailRoomType:
      properties:
        BedTypeDesc:
          description: Bed type description.
          type: string
        BedTypeDescCN:
          description: Bed type description in Chinese.
          type: string
        Name:
          description: Room type name.
          type: string
        NameCN:
          description: Room type name in Chinese.
          type: string
        RateInfos:
          description: A list of rates for a room type; at least one rate will be
            returned.
          items:
            $ref: '#/components/schemas/HotelDetailRateInfo'
          type: array
        RoomTypeCode:
          description: TourMind room type code, which is a unique identifier across
            all TourMind hotels.
          type: string
      type: object
      x-apifox-orders:
      - BedTypeDesc
      - BedTypeDescCN
      - Name
      - NameCN
      - RateInfos
      - RoomTypeCode
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticHotelInfo:
      properties:
        Address:
          description: Hotel address.
          type: string
        Address_CN:
          description: Hotel address in Chinese.
          type: string
        CityCode:
          description: City code.,Matched region ID.
          type: string
        CityName:
          description: City name.
          type: string
        CityNameCN:
          description: City name. 中文
          type: string
        CountryCode:
          description: 'Country code.，ISO 3166-1 alpha-2, e.g., China: CN.'
          type: string
        HotelId:
          description: TourMind hotel ID.
          type: string
        Images:
          items:
            $ref: '#/components/schemas/HotelstaticImage'
          type: array
        Latitude:
          description: Latitude.
          type: string
        Longitude:
          description: Longitude.
          type: string
        Name:
          description: Hotel name.
          type: string
        Name_CN:
          description: Hotel name in Chinese.
          type: string
        Phone:
          description: Hotel phone number.
          type: string
        StarRating:
          description: Hotel star rating.
          type: string
      type: object
      x-apifox-orders:
      - Address
      - Address_CN
      - CityCode
      - CityName
      - CityNameCN
      - CountryCode
      - HotelId
      - Images
      - Latitude
      - Longitude
      - Name
      - Name_CN
      - Phone
      - StarRating
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticHotelStaticListRequest:
      properties:
        CountryCode:
          description: Country code.
          type: string
        HotelIds:
          description: Identifiers for specific hotels..
          items:
            type: integer
          type: array
        Pagination:
          allOf:
          - $ref: '#/components/schemas/HotelstaticPagination'
          description: Pagination information.
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - CountryCode
      - HotelIds
      - Pagination
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticHotelStaticListResponse:
      properties:
        Error:
          allOf:
          - $ref: '#/components/schemas/CommonError'
          description: This value will be returned if an error occurs.
        HotelStaticListResult:
          allOf:
          - $ref: '#/components/schemas/HotelstaticHotelStaticListResult'
          description: This value will not be returned if an error occurs.
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - Error
      - HotelStaticListResult
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticHotelStaticListResult:
      properties:
        Hotels:
          description: Hotel information.
          items:
            $ref: '#/components/schemas/HotelstaticHotelInfo'
          type: array
        Pagination:
          allOf:
          - $ref: '#/components/schemas/HotelstaticPaginationRs'
          description: Pagination information.
      type: object
      x-apifox-orders:
      - Hotels
      - Pagination
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticImage:
      properties:
        caption:
          type: string
        category:
          type: integer
        hero_image:
          type: boolean
        links:
          additionalProperties:
            $ref: '#/components/schemas/HotelstaticLink'
          properties: {}
          type: object
          x-apifox-orders: []
          x-apifox-ignore-properties: []
      type: object
      x-apifox-orders:
      - caption
      - category
      - hero_image
      - links
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticLink:
      properties:
        href:
          type: string
        method:
          type: string
      type: object
      x-apifox-orders:
      - href
      - method
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticPagination:
      properties:
        PageIndex:
          type: string
        PageSize:
          description: Items per page.
          type: integer
      type: object
      x-apifox-orders:
      - PageIndex
      - PageSize
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    HotelstaticPaginationRs:
      properties:
        PageCount:
          description: Total number of pages.
          type: integer
        TotalCount:
          description: Total number of records.
          type: integer
      type: object
      x-apifox-orders:
      - PageCount
      - TotalCount
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionDateRange:
      properties:
        End:
          description: The end date of the low price period in ISO 8601 format.
          type: string
        Start:
          description: The start date of the low price period in ISO 8601 format.
          type: string
      type: object
      x-apifox-orders:
      - End
      - Start
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionHighPricePeriod:
      properties:
        DateRange:
          $ref: '#/components/schemas/RatePredictionDateRange'
        HighPriceRange:
          $ref: '#/components/schemas/RatePredictionPriceRange'
      type: object
      x-apifox-orders:
      - DateRange
      - HighPriceRange
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionHotel:
      properties:
        City:
          type: string
        Country:
          type: string
        HotelName:
          type: string
      type: object
      x-apifox-orders:
      - City
      - Country
      - HotelName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionHotelInfo:
      properties:
        City:
          description: The city where the hotel is situated.
          type: string
        Country:
          description: The country where the hotel is located.
          type: string
        HotelName:
          description: The name of the hotel.
          type: string
      type: object
      x-apifox-orders:
      - City
      - Country
      - HotelName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionLowPricePeriod:
      properties:
        DateRange:
          $ref: '#/components/schemas/RatePredictionDateRange'
        LowPriceRange:
          $ref: '#/components/schemas/RatePredictionPriceRange'
      type: object
      x-apifox-orders:
      - DateRange
      - LowPriceRange
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionPaxRoom:
      properties:
        Adults:
          type: integer
        Children:
          type: integer
        ChildrenAges:
          items:
            type: integer
          type: array
        RoomCount:
          type: integer
      type: object
      x-apifox-orders:
      - Adults
      - Children
      - ChildrenAges
      - RoomCount
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionPricePrediction:
      properties:
        HotelInfo:
          $ref: '#/components/schemas/RatePredictionHotelInfo'
        RoomTypes:
          items:
            $ref: '#/components/schemas/RatePredictionRoomType'
          type: array
      type: object
      x-apifox-orders:
      - HotelInfo
      - RoomTypes
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionPriceRange:
      properties:
        CurrencyCode:
          description: The currency code for the prices (e.g., USD, EUR).
          type: string
        Max:
          description: The maximum price during the low price period.
          type: number
        Min:
          description: The minimum price during the low price period.
          type: number
      type: object
      x-apifox-orders:
      - CurrencyCode
      - Max
      - Min
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionRatePredictionReq:
      properties:
        CheckIn:
          description: Check-in date (ISO 8601 format)
          type: string
        CheckOut:
          description: Check-out date (ISO 8601 format)
          type: string
        CurrencyCode:
          description: Preferred currency code
          type: string
        Hotels:
          description: List of hotels to search
          items:
            $ref: '#/components/schemas/RatePredictionHotel'
          type: array
        PaxRooms:
          description: Room occupancy details
          items:
            $ref: '#/components/schemas/RatePredictionPaxRoom'
          type: array
      type: object
      x-apifox-orders:
      - CheckIn
      - CheckOut
      - CurrencyCode
      - Hotels
      - PaxRooms
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionRatePredictionResp:
      properties:
        PricePredictions:
          items:
            $ref: '#/components/schemas/RatePredictionPricePrediction'
          type: array
      type: object
      x-apifox-orders:
      - PricePredictions
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RatePredictionRoomType:
      properties:
        HighPricePeriods:
          items:
            $ref: '#/components/schemas/RatePredictionHighPricePeriod'
          type: array
        LowPricePeriods:
          items:
            $ref: '#/components/schemas/RatePredictionLowPricePeriod'
          type: array
        RoomName:
          description: The name of the room type.
          type: string
      type: object
      x-apifox-orders:
      - HighPricePeriods
      - LowPricePeriods
      - RoomName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RegionListRegion:
      properties:
        CountryCode:
          description: 'ISO 3166-1 alpha-2, e.g., China: CN.,'
          type: string
        Name:
          description: Region name.
          type: string
        NameCN:
          description: Region name in Chinese. (Nullable.)
          type: string
        RegionID:
          description: TourMind region ID.
          type: string
        RegionNameLong:
          description: Region name
          type: string
        RegionNameLongCN:
          description: Full region name in Chinese.
          type: string
      type: object
      x-apifox-orders:
      - CountryCode
      - Name
      - NameCN
      - RegionID
      - RegionNameLong
      - RegionNameLongCN
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RegionListRegionListRequest:
      properties:
        CountryCode:
          description: Country code.
          type: string
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - CountryCode
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RegionListRegionListResponse:
      properties:
        Error:
          $ref: '#/components/schemas/CommonError'
        RegionListResult:
          $ref: '#/components/schemas/RegionListRegionListResult'
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - Error
      - RegionListResult
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RegionListRegionListResult:
      properties:
        Regions:
          items:
            $ref: '#/components/schemas/RegionListRegion'
          type: array
      type: object
      x-apifox-orders:
      - Regions
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailCancelPolicyInfo:
      properties:
        Amount:
          description: Cancellation charge amount.
          type: number
        CurrencyCode:
          description: Currency for the cancellation charge amount.
          type: string
        EndDateTime:
          description: 'Cancellation policy window end, format: “2006-01-02“.'
          type: string
        From:
          description: 开始时间的具体的时间 格式： “2006-01-02 15:04:05”
          type: string
        StartDateTime:
          description: 'Cancellation policy window start, format: “2006-01-02“.'
          type: string
        To:
          description: 结束时间的具体的时间 格式： “2006-01-02 15:04:05”
          type: string
      type: object
      x-apifox-orders:
      - Amount
      - CurrencyCode
      - EndDateTime
      - From
      - StartDateTime
      - To
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailDailyPriceInfo:
      properties:
        Count:
          description: this is daily inventory count
          type: integer
        Date:
          description: '日期，格式: “2006-01-02”'
          type: string
        Price:
          description: Price of a specific date.
          type: number
      type: object
      x-apifox-orders:
      - Count
      - Date
      - Price
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailDateRange:
      properties:
        End:
          description: End date of low price period
          type: string
        Start:
          description: Start date of low price period
          type: string
      type: object
      x-apifox-orders:
      - End
      - Start
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailHotel:
      properties:
        Address:
          description: Hotel address.
          type: string
        CheckIn:
          description: 'Check-in date, format: “2006-01-02”.'
          type: string
        CheckOut:
          description: 'Check-out date, format: “2006-01-02”.'
          type: string
        HotelCode:
          description: Hotel code.
          type: string
        LowPricePeriod:
          $ref: '#/components/schemas/RoomAvailLowPricePeriod'
        Name:
          description: Hotel name.
          type: string
        RoomTypes:
          description: Room types available based on the search criteria.
          items:
            $ref: '#/components/schemas/RoomAvailRoomType'
          type: array
      type: object
      x-apifox-orders:
      - Address
      - CheckIn
      - CheckOut
      - HotelCode
      - LowPricePeriod
      - Name
      - RoomTypes
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailLowPricePeriod:
      properties:
        CurrentPrice:
          description: Current price of the hotel
          type: number
        DateRange:
          $ref: '#/components/schemas/RoomAvailDateRange'
        LowPriceRange:
          $ref: '#/components/schemas/RoomAvailLowPriceRange'
        UpdateTime:
          description: Last update time of the price prediction
          type: string
      type: object
      x-apifox-orders:
      - CurrentPrice
      - DateRange
      - LowPriceRange
      - UpdateTime
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailLowPriceRange:
      properties:
        CurrencyCode:
          description: Currency code for the prices
          type: string
        Max:
          description: Maximum price in the range
          type: number
        Min:
          description: Minimum price in the range
          type: number
      type: object
      x-apifox-orders:
      - CurrencyCode
      - Max
      - Min
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailMealInfo:
      properties:
        MealCount:
          description: Number of free meals offered.
          type: integer
        MealType:
          description: Meal type <br><table><tr><th>MealType</th><th>Description</th></tr><tr><td>1</td><td>No
            Breakfast</td></tr><tr><td>2</td><td>Breakfast</td></tr></table>
          type: string
      type: object
      x-apifox-orders:
      - MealCount
      - MealType
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailPaxRoomRq:
      properties:
        Adults:
          description: Number of adults per room.
          type: integer
        Children:
          description: Number of children per room.
          type: integer
        ChildrenAges:
          description: Children's ages; the count of the array must equal the number
            of children.
          items:
            type: integer
          type: array
        RoomCount:
          description: Room count.
          type: integer
      type: object
      x-apifox-orders:
      - Adults
      - Children
      - ChildrenAges
      - RoomCount
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailRateInfo:
      properties:
        Allotment:
          description: Available room inventory count.
          type: integer
        CancelPolicyInfos:
          description: Cancellation policy information.
          items:
            $ref: '#/components/schemas/RoomAvailCancelPolicyInfo'
          type: array
        CurrencyCode:
          description: Currency code.
          type: string
        DailyPriceInfo:
          description: Daily pricing information for the hotel.
          items:
            $ref: '#/components/schemas/RoomAvailDailyPriceInfo'
          type: array
        MealInfo:
          allOf:
          - $ref: '#/components/schemas/RoomAvailMealInfo'
          description: Meal information.
        Name:
          description: Additional rate description, such as meal type description.
          type: string
        NameCN:
          description: Additional rate description in Chinese, such as meal type description.
          type: string
        RateCode:
          description: Rate identifier for a sellable product; this code is unique
            across all TourMind hotels.
          type: string
        Refundable:
          type: boolean
        TotalPrice:
          description: Total price; this is the total amount charged.
          type: number
        bedTypeDesc:
          description: Bed type description.
          type: string
        bedTypeDescCN:
          description: Bed type description in Chinese.
          type: string
      type: object
      x-apifox-orders:
      - Allotment
      - CancelPolicyInfos
      - CurrencyCode
      - DailyPriceInfo
      - MealInfo
      - Name
      - NameCN
      - RateCode
      - Refundable
      - TotalPrice
      - bedTypeDesc
      - bedTypeDescCN
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailRoomAvailRequest:
      properties:
        CheckIn:
          description: 'Check-in date, format: “2006-01-02”.'
          type: string
        CheckOut:
          description: 'Check-out date, format: “2006-01-02”.'
          type: string
        HotelCodes:
          description: TourMind Hotel ID; only one ID is required in this request.
          items:
            type: integer
          type: array
        Nationality:
          description: Nationality.
          type: string
        PaxRooms:
          description: 'Request room occupancies. Note: Currently, only the same number
            of adults and children is supported for each room.'
          items:
            $ref: '#/components/schemas/RoomAvailPaxRoomRq'
          type: array
        RateCode:
          description: TourMind Rate ID.
          type: string
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - CheckIn
      - CheckOut
      - HotelCodes
      - Nationality
      - PaxRooms
      - RateCode
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailRoomAvailResponse:
      properties:
        Error:
          allOf:
          - $ref: '#/components/schemas/CommonError'
          description: This value will be returned if an error occurs.
        Hotels:
          description: Hotel information.
          items:
            $ref: '#/components/schemas/RoomAvailHotel'
          type: array
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - Error
      - Hotels
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomAvailRoomType:
      properties:
        BedTypeDesc:
          description: Bed type description.
          type: string
        BedTypeDescCN:
          description: Bed type description in Chinese.
          type: string
        Name:
          description: Room type name.
          type: string
        NameCN:
          description: Room type name in Chinese.
          type: string
        RateInfos:
          description: A list of rates for a room type; at least one rate will be
            returned.
          items:
            $ref: '#/components/schemas/RoomAvailRateInfo'
          type: array
        RoomTypeCode:
          description: TourMind room type code, which is a unique identifier across
            all TourMind hotels.
          type: string
      type: object
      x-apifox-orders:
      - BedTypeDesc
      - BedTypeDescCN
      - Name
      - NameCN
      - RateInfos
      - RoomTypeCode
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMapAggregation:
      properties:
        GroupCode:
          description: A unique code representing the group of rooms.
          type: string
        GroupName:
          description: The name of the room group.
          type: string
        GroupRooms:
          items:
            $ref: '#/components/schemas/RoomMapGroupRoom'
          type: array
      type: object
      x-apifox-orders:
      - GroupCode
      - GroupName
      - GroupRooms
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMapGroupRoom:
      properties:
        RoomIds:
          description: The unique identifier for the room.
          type: string
        RoomName:
          description: The name of the individual room within the group.
          type: string
        SupplierId:
          description: The identifier for the supplier providing the room.
          type: string
      type: object
      x-apifox-orders:
      - RoomIds
      - RoomName
      - SupplierId
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMapRoomMapReq:
      properties:
        PropertyId:
          description: Unique identifier for the hotel property
          type: string
        PropertyName:
          description: The name of the hotel property
          type: string
        SupplierList:
          items:
            $ref: '#/components/schemas/RoomMapSupplierList'
          type: array
      type: object
      x-apifox-orders:
      - PropertyId
      - PropertyName
      - SupplierList
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMapRoomMapResp:
      properties:
        Aggregation:
          items:
            $ref: '#/components/schemas/RoomMapAggregation'
          type: array
        PropertyId:
          description: A unique identifier for the property.
          type: string
        PropertyName:
          description: The name of the property (hotel) being referenced.
          type: string
        Unmapped:
          description: A list of unmapped rooms or groups, if any.
          items:
            type: string
          type: array
      type: object
      x-apifox-orders:
      - Aggregation
      - PropertyId
      - PropertyName
      - Unmapped
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMapSupplierList:
      properties:
        SupplierId:
          description: Unique identifier for the supplier
          type: string
        SupplierRooms:
          description: List of rooms provided by the supplier
          items:
            $ref: '#/components/schemas/RoomMapSupplierRoom'
          type: array
      type: object
      x-apifox-orders:
      - SupplierId
      - SupplierRooms
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMapSupplierRoom:
      properties:
        RoomId:
          description: Unique identifier for the room
          type: string
        RoomName:
          description: Original room name provided by the supplier
          type: string
      type: object
      x-apifox-orders:
      - RoomId
      - RoomName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchMasterRoomListReq:
      properties:
        BedName:
          type: string
        RoomId:
          type: string
        RoomName:
          type: string
      type: object
      x-apifox-orders:
      - BedName
      - RoomId
      - RoomName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchMatchedRoomDetailResp:
      properties:
        Score:
          type: number
        SupplierBedName:
          type: string
        SupplierId:
          type: string
        SupplierRoomId:
          type: string
        SupplierRoomName:
          type: string
      type: object
      x-apifox-orders:
      - Score
      - SupplierBedName
      - SupplierId
      - SupplierRoomId
      - SupplierRoomName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchMatchedRoomResp:
      properties:
        BedName:
          type: string
        MatchedRooms:
          items:
            $ref: '#/components/schemas/RoomMatchMatchedRoomDetailResp'
          type: array
        RoomId:
          type: string
        RoomName:
          type: string
      type: object
      x-apifox-orders:
      - BedName
      - MatchedRooms
      - RoomId
      - RoomName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchPropertyInfoReq:
      properties:
        PropertyId:
          type: string
        PropertyName:
          type: string
      type: object
      x-apifox-orders:
      - PropertyId
      - PropertyName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchRoomMatchReq:
      properties:
        AppKey:
          type: string
        EnableDeepThinking:
          type: boolean
        MasterRoomList:
          items:
            $ref: '#/components/schemas/RoomMatchMasterRoomListReq'
          type: array
        PropertyInfo:
          $ref: '#/components/schemas/RoomMatchPropertyInfoReq'
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
        SupplierRoomList:
          items:
            $ref: '#/components/schemas/RoomMatchSupplierRoomListReq'
          type: array
      type: object
      x-apifox-orders:
      - AppKey
      - EnableDeepThinking
      - MasterRoomList
      - PropertyInfo
      - RequestHeader
      - SupplierRoomList
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchRoomMatchResp:
      properties:
        Code:
          type: integer
        Data:
          $ref: '#/components/schemas/RoomMatchRoomMatchResp_Data'
        Error:
          $ref: '#/components/schemas/CommonError'
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
        Success:
          type: boolean
        UnmatchedRooms:
          items:
            $ref: '#/components/schemas/RoomMatchMatchedRoomDetailResp'
          type: array
      type: object
      x-apifox-orders:
      - Code
      - Data
      - Error
      - ResponseHeader
      - Success
      - UnmatchedRooms
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchSupplierRoomInfoReq:
      properties:
        SupplierBedName:
          type: string
        SupplierRoomId:
          type: string
        SupplierRoomName:
          type: string
      type: object
      x-apifox-orders:
      - SupplierBedName
      - SupplierRoomId
      - SupplierRoomName
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchSupplierRoomListReq:
      properties:
        SupplierId:
          type: string
        SupplierRoomInfo:
          items:
            $ref: '#/components/schemas/RoomMatchSupplierRoomInfoReq'
          type: array
      type: object
      x-apifox-orders:
      - SupplierId
      - SupplierRoomInfo
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomStaticModelRoomStaticRequest:
      properties:
        HotelCode:
          description: Hotel.Code
          type: integer
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - HotelCode
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomStaticModelRoomStaticResponse:
      properties:
        Error:
          $ref: '#/components/schemas/CommonError'
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
        RoomTypes:
          items:
            $ref: '#/components/schemas/RoomStaticModelRoomTypeInfo'
          type: array
      type: object
      x-apifox-orders:
      - Error
      - ResponseHeader
      - RoomTypes
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomStaticModelRoomTypeInfo:
      properties:
        BedTypeDesc:
          type: string
        BedTypeDescCN:
          type: string
        MaxOccupancy:
          type: integer
        RoomTypeCode:
          type: string
        RoomTypeName:
          type: string
        RoomTypeNameCN:
          type: string
      type: object
      x-apifox-orders:
      - BedTypeDesc
      - BedTypeDescCN
      - MaxOccupancy
      - RoomTypeCode
      - RoomTypeName
      - RoomTypeNameCN
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    SearchOrderOrderInfo:
      properties:
        AgentRefID:
          description: Agent reference ID; maximum length is 128.
          type: string
        CurrencyCode:
          description: Currency code.
          type: string
        HotelConfirmationNo:
          description: Hotel confirmation number.
          type: string
        OrderStatus:
          description: Order status. <br><table><tr><th>OrderStatus</th><th>Description</th></tr><tr><td>PENDING</td><td>Booking
            confirmation pending.</td></tr><tr><td>CONFIRMED</td><td>Booking confirmed.</td></tr><tr><td>CANCELLED</td><td>Booking
            cancelled.</td></tr><tr><td>FAILED</td><td>Booking confirmation failed.</td></tr></table>
          type: string
        ReservationID:
          description: Tourmind order ID
          type: string
        TotalPrice:
          description: Total price of the booking.
          type: number
      type: object
      x-apifox-orders:
      - AgentRefID
      - CurrencyCode
      - HotelConfirmationNo
      - OrderStatus
      - ReservationID
      - TotalPrice
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    SearchOrderQueryOrderRequest:
      properties:
        AgentRefID:
          description: Agent reference ID provided in the CreateOrder request; maximum
            length is 128.
          type: string
        RequestHeader:
          $ref: '#/components/schemas/CommonRequestHeader'
      type: object
      x-apifox-orders:
      - AgentRefID
      - RequestHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    SearchOrderQueryOrderResponse:
      properties:
        Error:
          allOf:
          - $ref: '#/components/schemas/CommonError'
          description: This value will be returned if an error occurs.
        OrderInfo:
          allOf:
          - $ref: '#/components/schemas/SearchOrderOrderInfo'
          description: This value will not be returned if an error occurs.
        ResponseHeader:
          $ref: '#/components/schemas/CommonResponseHeader'
      type: object
      x-apifox-orders:
      - Error
      - OrderInfo
      - ResponseHeader
      x-apifox-ignore-properties: []
      x-apifox-folder: Schemas
    RoomMatchRoomMatchResp_Data:
      properties:
        MatchedRooms:
          items:
            $ref: '#/components/schemas/RoomMatchMatchedRoomResp'
          type: array
        PropertyId:
          type: string
        PropertyName:
          type: string
  securitySchemes: {}
