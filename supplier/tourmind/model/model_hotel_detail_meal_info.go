/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelDetailMealInfo struct for HotelDetailMealInfo
type HotelDetailMealInfo struct {
	// Number of free meals offered.
	MealCount int32 `json:"MealCount,omitempty"`
	// Meal type <br><table><tr><th>MealType</th><th>Description</th></tr><tr><td>1</td><td>No Breakfast</td></tr><tr><td>2</td><td>Breakfast</td></tr><tr><td>3</td><td>Lunch</td></tr><tr><td>4</td><td>Dinner</td></tr><tr><td>5</td><td>Lunch and Dinner</td></tr><tr><td>6</td><td>HalfBoard</td></tr><tr><td>7</td><td>FullBoard</td></tr><tr><td>8</td><td>AllInclusive</td></tr><tr><td>9</td><td>SelfCatering</td></tr></table>
	MealType string `json:"MealType,omitempty"`
}
