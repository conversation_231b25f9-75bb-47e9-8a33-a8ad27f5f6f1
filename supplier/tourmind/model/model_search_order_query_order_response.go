/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// SearchOrderQueryOrderResponse struct for SearchOrderQueryOrderResponse
type SearchOrderQueryOrderResponse struct {
	// This value will be returned if an error occurs.
	Error CommonError `json:"Error,omitempty"`
	// This value will not be returned if an error occurs.
	OrderInfo SearchOrderOrderInfo `json:"OrderInfo,omitempty"`
	ResponseHeader CommonResponseHeader `json:"ResponseHeader,omitempty"`
}
