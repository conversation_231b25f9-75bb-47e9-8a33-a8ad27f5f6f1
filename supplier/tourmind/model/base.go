package model

import "time"

// Error 错误信息 - 严格按照官方文档定义
type Error struct {
	ErrorCode    string `json:"ErrorCode"`    // 错误代码
	ErrorMessage string `json:"ErrorMessage"` // 错误消息
}

// BaseRequest 基础请求结构
type BaseRequest struct {
	RequestHeader CommonRequestHeader `json:"RequestHeader"`
}

// BaseResponse 基础响应结构
type BaseResponse struct {
	Error          *Error                `json:"Error,omitempty"`
	ResponseHeader *CommonResponseHeader `json:"ResponseHeader,omitempty"`
}

// Properties 供应商配置属性
type Properties struct {
	Header Header `json:"header"`
}

// Header 认证头信息
type Header struct {
	AgentCode string `json:"agentCode"`
	UserName  string `json:"userName"`
	Password  string `json:"password"`
}

// GenerateTransactionID 生成事务ID
func GenerateTransactionID() string {
	return time.Now().Format("20060102150405") + "001"
}

// FormatRequestTime 格式化请求时间
func FormatRequestTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}
