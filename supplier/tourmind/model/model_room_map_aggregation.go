/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomMapAggregation struct for RoomMapAggregation
type RoomMapAggregation struct {
	// A unique code representing the group of rooms.
	GroupCode string `json:"GroupCode,omitempty"`
	// The name of the room group.
	GroupName string `json:"GroupName,omitempty"`
	GroupRooms []RoomMapGroupRoom `json:"GroupRooms,omitempty"`
}
