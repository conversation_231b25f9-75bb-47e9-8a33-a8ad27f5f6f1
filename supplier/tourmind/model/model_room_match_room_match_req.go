/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomMatchRoomMatchReq struct for RoomMatchRoomMatchReq
type RoomMatchRoomMatchReq struct {
	AppKey string `json:"AppKey,omitempty"`
	EnableDeepThinking bool `json:"EnableDeepThinking,omitempty"`
	MasterRoomList []RoomMatchMasterRoomListReq `json:"MasterRoomList,omitempty"`
	PropertyInfo RoomMatchPropertyInfoReq `json:"PropertyInfo,omitempty"`
	RequestHeader CommonRequestHeader `json:"RequestHeader,omitempty"`
	SupplierRoomList []RoomMatchSupplierRoomListReq `json:"SupplierRoomList,omitempty"`
}
