/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelDetailHotel struct for HotelDetailHotel
type HotelDetailHotel struct {
	// Hotel address.
	Address string `json:"Address,omitempty"`
	// Check-in date, format: “2006-01-02”.
	CheckIn string `json:"CheckIn,omitempty"`
	// Check-out date, format: “2006-01-02”.
	CheckOut string `json:"CheckOut,omitempty"`
	// Hotel code.
	HotelCode string `json:"HotelCode,omitempty"`
	// Hotel name.
	Name string `json:"Name,omitempty"`
	// Room types available based on the search criteria.
	RoomTypes []HotelDetailRoomType `json:"RoomTypes,omitempty"`
}
