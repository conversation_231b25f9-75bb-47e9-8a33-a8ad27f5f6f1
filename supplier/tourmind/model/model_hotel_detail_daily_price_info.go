/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// HotelDetailDailyPriceInfo struct for HotelDetailDailyPriceInfo
type HotelDetailDailyPriceInfo struct {
	// this is daily inventory count
	Count int32 `json:"Count,omitempty"`
	// 日期，格式: “2006-01-02”
	Date string `json:"Date,omitempty"`
	// Price of a specific date.  单间房子 单晚的Price of a specific date.
	Price float32 `json:"Price,omitempty"`
}
