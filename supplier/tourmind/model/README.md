# Go API client for model

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

## Overview
This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.  By using the [OpenAPI-spec](https://www.openapis.org/) from a remote server, you can easily generate an API client.

- API version: 1.0.0
- Package version: 1.0.0
- Build package: org.openapitools.codegen.languages.GoClientCodegen

## Installation

Install the following dependencies:

```shell
go get github.com/stretchr/testify/assert
go get golang.org/x/oauth2
go get golang.org/x/net/context
go get github.com/antihax/optional
```

Put the package under your project folder and add the following in import:

```golang
import "./model"
```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------


## Documentation For Models

 - [CancelOrderCancelOrderRequest](docs/CancelOrderCancelOrderRequest.md)
 - [CancelOrderCancelOrderResponse](docs/CancelOrderCancelOrderResponse.md)
 - [CancelOrderCancelResult](docs/CancelOrderCancelResult.md)
 - [CommonError](docs/CommonError.md)
 - [CommonRequestHeader](docs/CommonRequestHeader.md)
 - [CommonResponseHeader](docs/CommonResponseHeader.md)
 - [CreateOrderContactInfo](docs/CreateOrderContactInfo.md)
 - [CreateOrderCreateOrderRequest](docs/CreateOrderCreateOrderRequest.md)
 - [CreateOrderCreateOrderResponse](docs/CreateOrderCreateOrderResponse.md)
 - [CreateOrderOrderInfo](docs/CreateOrderOrderInfo.md)
 - [CreateOrderPaxName](docs/CreateOrderPaxName.md)
 - [CreateOrderPaxRoom](docs/CreateOrderPaxRoom.md)
 - [HotelDetailCancelPolicyInfo](docs/HotelDetailCancelPolicyInfo.md)
 - [HotelDetailDailyPriceInfo](docs/HotelDetailDailyPriceInfo.md)
 - [HotelDetailHotel](docs/HotelDetailHotel.md)
 - [HotelDetailHotelDetailRequest](docs/HotelDetailHotelDetailRequest.md)
 - [HotelDetailHotelDetailResponse](docs/HotelDetailHotelDetailResponse.md)
 - [HotelDetailMealInfo](docs/HotelDetailMealInfo.md)
 - [HotelDetailPaxRoomRq](docs/HotelDetailPaxRoomRq.md)
 - [HotelDetailRateInfo](docs/HotelDetailRateInfo.md)
 - [HotelDetailRoomType](docs/HotelDetailRoomType.md)
 - [HotelstaticHotelInfo](docs/HotelstaticHotelInfo.md)
 - [HotelstaticHotelStaticListRequest](docs/HotelstaticHotelStaticListRequest.md)
 - [HotelstaticHotelStaticListResponse](docs/HotelstaticHotelStaticListResponse.md)
 - [HotelstaticHotelStaticListResult](docs/HotelstaticHotelStaticListResult.md)
 - [HotelstaticImage](docs/HotelstaticImage.md)
 - [HotelstaticLink](docs/HotelstaticLink.md)
 - [HotelstaticPagination](docs/HotelstaticPagination.md)
 - [HotelstaticPaginationRs](docs/HotelstaticPaginationRs.md)
 - [RatePredictionDateRange](docs/RatePredictionDateRange.md)
 - [RatePredictionHighPricePeriod](docs/RatePredictionHighPricePeriod.md)
 - [RatePredictionHotel](docs/RatePredictionHotel.md)
 - [RatePredictionHotelInfo](docs/RatePredictionHotelInfo.md)
 - [RatePredictionLowPricePeriod](docs/RatePredictionLowPricePeriod.md)
 - [RatePredictionPaxRoom](docs/RatePredictionPaxRoom.md)
 - [RatePredictionPricePrediction](docs/RatePredictionPricePrediction.md)
 - [RatePredictionPriceRange](docs/RatePredictionPriceRange.md)
 - [RatePredictionRatePredictionReq](docs/RatePredictionRatePredictionReq.md)
 - [RatePredictionRatePredictionResp](docs/RatePredictionRatePredictionResp.md)
 - [RatePredictionRoomType](docs/RatePredictionRoomType.md)
 - [RegionListRegion](docs/RegionListRegion.md)
 - [RegionListRegionListRequest](docs/RegionListRegionListRequest.md)
 - [RegionListRegionListResponse](docs/RegionListRegionListResponse.md)
 - [RegionListRegionListResult](docs/RegionListRegionListResult.md)
 - [RoomAvailCancelPolicyInfo](docs/RoomAvailCancelPolicyInfo.md)
 - [RoomAvailDailyPriceInfo](docs/RoomAvailDailyPriceInfo.md)
 - [RoomAvailDateRange](docs/RoomAvailDateRange.md)
 - [RoomAvailHotel](docs/RoomAvailHotel.md)
 - [RoomAvailLowPricePeriod](docs/RoomAvailLowPricePeriod.md)
 - [RoomAvailLowPriceRange](docs/RoomAvailLowPriceRange.md)
 - [RoomAvailMealInfo](docs/RoomAvailMealInfo.md)
 - [RoomAvailPaxRoomRq](docs/RoomAvailPaxRoomRq.md)
 - [RoomAvailRateInfo](docs/RoomAvailRateInfo.md)
 - [RoomAvailRoomAvailRequest](docs/RoomAvailRoomAvailRequest.md)
 - [RoomAvailRoomAvailResponse](docs/RoomAvailRoomAvailResponse.md)
 - [RoomAvailRoomType](docs/RoomAvailRoomType.md)
 - [RoomMapAggregation](docs/RoomMapAggregation.md)
 - [RoomMapGroupRoom](docs/RoomMapGroupRoom.md)
 - [RoomMapRoomMapReq](docs/RoomMapRoomMapReq.md)
 - [RoomMapRoomMapResp](docs/RoomMapRoomMapResp.md)
 - [RoomMapSupplierList](docs/RoomMapSupplierList.md)
 - [RoomMapSupplierRoom](docs/RoomMapSupplierRoom.md)
 - [RoomMatchMasterRoomListReq](docs/RoomMatchMasterRoomListReq.md)
 - [RoomMatchMatchedRoomDetailResp](docs/RoomMatchMatchedRoomDetailResp.md)
 - [RoomMatchMatchedRoomResp](docs/RoomMatchMatchedRoomResp.md)
 - [RoomMatchPropertyInfoReq](docs/RoomMatchPropertyInfoReq.md)
 - [RoomMatchRoomMatchReq](docs/RoomMatchRoomMatchReq.md)
 - [RoomMatchRoomMatchResp](docs/RoomMatchRoomMatchResp.md)
 - [RoomMatchRoomMatchRespData](docs/RoomMatchRoomMatchRespData.md)
 - [RoomMatchSupplierRoomInfoReq](docs/RoomMatchSupplierRoomInfoReq.md)
 - [RoomMatchSupplierRoomListReq](docs/RoomMatchSupplierRoomListReq.md)
 - [RoomStaticModelRoomStaticRequest](docs/RoomStaticModelRoomStaticRequest.md)
 - [RoomStaticModelRoomStaticResponse](docs/RoomStaticModelRoomStaticResponse.md)
 - [RoomStaticModelRoomTypeInfo](docs/RoomStaticModelRoomTypeInfo.md)
 - [SearchOrderOrderInfo](docs/SearchOrderOrderInfo.md)
 - [SearchOrderQueryOrderRequest](docs/SearchOrderQueryOrderRequest.md)
 - [SearchOrderQueryOrderResponse](docs/SearchOrderQueryOrderResponse.md)


## Documentation For Authorization

 Endpoints do not require authorization.



## Author


