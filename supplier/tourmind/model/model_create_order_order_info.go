/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CreateOrderOrderInfo struct for CreateOrderOrderInfo
type CreateOrderOrderInfo struct {
	// Order status. <br><table><tr><th>OrderStatus</th><th>Description</th></tr><tr><td>PENDING</td><td>Booking confirmation pending.</td></tr><tr><td>CONFIRMED</td><td>Booking confirmed.</td></tr><tr><td>CANCELLED</td><td>Booking cancelled.</td></tr><tr><td>FAILED</td><td>Booking confirmation failed.</td></tr></table>
	OrderStatus string `json:"OrderStatus,omitempty"`
	// Tourmind order ID
	ReservationID string `json:"ReservationID,omitempty"`
}
