/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// CreateOrderPaxRoom struct for CreateOrderPaxRoom
type CreateOrder<PERSON>axRoom struct {
	Adults int32 `json:"Adults,omitempty"`
	Children int32 `json:"Children,omitempty"`
	// Children's ages; the count of the array must equal the number of children.
	ChildrenAges []int32 `json:"ChildrenAges,omitempty"`
	PaxNames []CreateOrderPaxName `json:"PaxNames,omitempty"`
	RoomCount int32 `json:"RoomCount,omitempty"`
}
