/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model
// RoomAvailMealInfo struct for RoomAvailMealInfo
type RoomAvailMealInfo struct {
	// Number of free meals offered.
	MealCount int32 `json:"MealCount,omitempty"`
	// Meal type <br><table><tr><th>MealType</th><th>Description</th></tr><tr><td>1</td><td>No Breakfast</td></tr><tr><td>2</td><td>Breakfast</td></tr></table>
	MealType string `json:"MealType,omitempty"`
}
