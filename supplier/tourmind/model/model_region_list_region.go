/*
 * TourMind API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * API version: 1.0.0
 * Generated by: OpenAPI Generator (https://openapi-generator.tech)
 */

package model

// RegionListRegion represents a geographical region in the TourMind system
type RegionListRegion struct {
	// CountryCode is the ISO 3166-1 alpha-2 country code, e.g., China: CN
	CountryCode string `json:"CountryCode,omitempty"`
	// Name is the region name in English
	Name string `json:"Name,omitempty"`
	// NameCN is the region name in Chinese (nullable) //apidoc:zh
	NameCN string `json:"NameCN,omitempty"`
	// RegionID is the unique TourMind region identifier
	RegionID string `json:"RegionID,omitempty"`
	// RegionNameLong is the full region name in English
	RegionNameLong string `json:"RegionNameLong,omitempty"`
	// RegionNameLongCN is the full region name in Chinese //apidoc:zh
	RegionNameLongCN string `json:"RegionNameLongCN,omitempty"`
}
