package tourmind

import (
	"sync"

	configutils "hotel/common/config"
	"hotel/supplier/tourmind/model"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"
)

var (
	configFile = configutils.SafeFlagString("tourmind", "supplier/tourmind/config.yaml", "the tourmind config file")

	_once           sync.Once
	_tourmindClient *TourmindClient
)

func NewTourmindClient() *TourmindClient {
	_once.Do(func() {
		su := middleware.SupplierUtilWrapperFromConfigFilePath[model.Properties](domain.Supplier_Tourmind, *configFile)
		_tourmindClient = &TourmindClient{
			SupplierUtilWrapper: su,
		}
	})
	return _tourmindClient
}