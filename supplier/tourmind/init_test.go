package tourmind

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	common "hotel/common/domain"
	"hotel/supplier/domain"
	"hotel/supplier/tourmind/model"
)

// Tourmind 测试账号 - 官方文档提供的测试账号
const (
	TestAgentCode = "tms_test"
	TestUserName  = "tms_test"
	TestPassword  = "tms_test"
)

// testCtx 创建测试用的 context，注入真实认证信息
func testCtx() context.Context {
	return domain.InjectBaseRequestContextPayload(context.Background(), domain.BaseRequestContextPayload{
		Supplier: domain.Supplier_Tourmind,
		Properties: model.Properties{
			Header: model.Header{
				AgentCode: TestAgentCode,
				UserName:  TestUserName,
				Password:  TestPassword,
			},
		},
		Session: &common.Session{
			Id:     "test-session-id",
			Params: make(map[string]string),
		},
	})
}

func TestNewTourmindClient(t *testing.T) {
	t.Log("=== 测试 Tourmind 客户端初始化 ===")

	// 测试客户端创建
	client := NewTourmindClient()
	assert.NotNil(t, client, "客户端不应为空")

	// 测试供应商类型
	assert.Equal(t, domain.Supplier_Tourmind, client.Supplier(), "供应商类型应为 Tourmind")

	// 测试单例模式
	client2 := NewTourmindClient()
	assert.Equal(t, client, client2, "应返回同一个实例")

	t.Log("Tourmind 客户端初始化测试通过")
}