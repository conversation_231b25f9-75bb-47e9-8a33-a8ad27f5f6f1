package tourmind

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"hotel/supplier/domain"
)

func TestConvertGuestRoomOptions(t *testing.T) {
	// 测试用例：2个成人，1个儿童，1个房间
	options := []domain.GuestRoomOption{
		{
			RoomCount:     1,
			AdultCount:    2,
			ChildrenCount: 1,
			Guests: []domain.Guest{
				{Age: 25}, // 成人
				{Age: 30}, // 成人
				{Age: 8},  // 儿童
			},
		},
	}

	result := convertGuestRoomOptions(options)

	// 验证结果
	assert.Len(t, result, 1, "应该返回1个PaxRoomRQ")

	paxRoom := result[0]
	assert.Equal(t, 2, paxRoom.Adults, "成人数应该是2")
	assert.Equal(t, 1, paxRoom.Children, "儿童数应该是1")
	assert.Equal(t, 1, paxRoom.RoomCount, "房间数应该是1")
	assert.Equal(t, []int{8}, paxRoom.ChildrenAges, "儿童年龄应该是[8]")
}

func TestConvertGuestRoomOptions_MultipleRooms(t *testing.T) {
	// 测试用例：4个成人，2个儿童，2个房间
	options := []domain.GuestRoomOption{
		{
			RoomCount:     2,
			AdultCount:    4,
			ChildrenCount: 2,
			Guests: []domain.Guest{
				{Age: 25}, // 成人
				{Age: 30}, // 成人
				{Age: 35}, // 成人
				{Age: 40}, // 成人
				{Age: 8},  // 儿童
				{Age: 10}, // 儿童
			},
		},
	}

	result := convertGuestRoomOptions(options)

	// 验证结果
	assert.Len(t, result, 1, "应该返回1个PaxRoomRQ")

	paxRoom := result[0]
	assert.Equal(t, 2, paxRoom.Adults, "每个房间成人数应该是2")
	assert.Equal(t, 1, paxRoom.Children, "每个房间儿童数应该是1")
	assert.Equal(t, 2, paxRoom.RoomCount, "房间数应该是2")
	assert.Equal(t, []int{8, 10}, paxRoom.ChildrenAges, "儿童年龄应该是[8, 10]")
}

func TestConvertGuestRoomOptions_NoChildren(t *testing.T) {
	// 测试用例：2个成人，0个儿童，1个房间
	options := []domain.GuestRoomOption{
		{
			RoomCount:     1,
			AdultCount:    2,
			ChildrenCount: 0,
			Guests: []domain.Guest{
				{Age: 25}, // 成人
				{Age: 30}, // 成人
			},
		},
	}

	result := convertGuestRoomOptions(options)

	// 验证结果
	assert.Len(t, result, 1, "应该返回1个PaxRoomRQ")

	paxRoom := result[0]
	assert.Equal(t, 2, paxRoom.Adults, "成人数应该是2")
	assert.Equal(t, 0, paxRoom.Children, "儿童数应该是0")
	assert.Equal(t, 1, paxRoom.RoomCount, "房间数应该是1")
	assert.Empty(t, paxRoom.ChildrenAges, "儿童年龄列表应该为空")
}
