# Tourmind 供应商完整流程测试指南

## 概述

本文档说明如何运行 Tourmind 供应商的完整流程测试，确保能够真正连接到供应商并跑通完整的酒店预订流程。

## 测试配置

### 1. 环境要求

测试只在 DEV 环境下运行，需要设置环境变量：
```bash
export ENV=DEV
```

### 2. 认证信息配置

在 `init_test.go` 文件中，需要将测试账号替换为真实的 Tourmind 认证信息：

```go
const (
    TestAgentCode = "your_real_agent_code"    // 替换为真实的代理商代码
    TestUserName  = "your_real_username"      // 替换为真实的用户名
    TestPassword  = "your_real_password"      // 替换为真实的密码
)
```

## 测试流程

完整流程测试包含以下步骤：

1. **获取酒店列表** - 查询可用酒店
2. **获取酒店详情和价格** - 查询酒店房型和价格信息
3. **可定查询** - 验证房型是否可预订
4. **提交订单** - 创建预订订单
5. **查询订单** - 查询订单状态
6. **取消订单** - 取消预订订单

## 运行测试

### 命令行运行

```bash
# 进入项目目录
cd /Users/<USER>/GolandProjects/hotel-be

# 设置环境变量
export ENV=DEV

# 运行完整流程测试
go test -v ./supplier/tourmind -run TestTourmindClient_FullFlow
```

### IDE 运行

在 GoLand 或 VS Code 中：
1. 打开 `full_flow_test.go` 文件
2. 确保环境变量 `ENV=DEV` 已设置
3. 点击 `TestTourmindClient_FullFlow` 函数旁的运行按钮

## 测试特性

### 智能重试机制

- 测试会尝试多个酒店（最多5个），直到找到有可用房间的酒店
- 下单时如果遇到重复预订错误，会自动查询并取消之前的订单后重试

### 错误处理

- 如果认证信息未配置或错误，测试会跳过并给出提示
- 所有关键步骤都有详细的错误日志

### 数据持久化

- 查价请求会存储到 session 中，供后续步骤使用
- 订单ID会在各个步骤间传递，确保操作的一致性

## 预期结果

成功运行的测试应该输出类似以下内容：

```
=== 1. 获取酒店列表 ===
酒店列表查询成功，返回 X 个酒店
Trying hotel ID: XXXXX
=== 2. 获取酒店详情和价格 (尝试 1) ===
Found available room in hotel XXXXX
Selected rate package ID: XXXXX
Selected room: Standard Room, price: 100.00
=== 3. 可定查询 ===
CheckAvail success: ...
=== 4. 提交订单 ===
Book success: ...
Generated order ID: XXXXX
=== 5. 查询订单 ===
QueryOrder response: ...
=== 6. 取消订单 ===
Cancel response: ...
=== 全链路测试完成 ===
```

## 故障排除

### 常见问题

1. **测试被跳过**
   - 检查 `ENV=DEV` 环境变量是否设置
   - 确认认证信息是否正确配置

2. **认证失败**
   - 验证 AgentCode、UserName、Password 是否正确
   - 联系 Tourmind 确认账号状态

3. **没有可用酒店**
   - 检查查询的日期是否合理（测试使用7天后的日期）
   - 确认供应商在查询地区有可用库存

4. **下单失败**
   - 检查客人信息是否完整
   - 确认房型是否仍然可用

## 注意事项

1. **测试环境**：此测试会创建真实的预订订单，请确保使用测试环境的认证信息
2. **费用问题**：虽然测试会自动取消订单，但请确认测试账号的费用政策
3. **频率限制**：避免频繁运行测试，以免触发供应商的频率限制
4. **数据清理**：测试完成后会自动取消订单，但建议定期检查是否有遗留订单

## 参考

- 参考了 `dida` 供应商的测试实现
- 遵循了项目中供应商接入的最佳实践
- 使用了标准的错误处理和重试机制