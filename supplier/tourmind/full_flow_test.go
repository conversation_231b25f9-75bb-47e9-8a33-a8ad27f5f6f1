package tourmind

import (
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/common/utils"
	"hotel/supplier/domain"
)

func TestTourmindClient_FullFlow(t *testing.T) {
	// if os.Getenv("ENV") != "DEV" {
	// 	t.<PERSON><PERSON>("FullFlow only runs in DEV environment")
	// }
	t.Log("=== Tourmind 供应商完整流程测试 ===")

	// 初始化客户端
	client := NewTourmindClient()
	assert.NotNil(t, client)

	ctx := testCtx()
	hotelIdListResp, err := client.HotelIdList(ctx, &domain.HotelIdListReq{
		CityId:      "",
		CountryCode: "CN",
		PageReq:     pagehelper.PageReq{PageNum: 1, PageSize: 10},
	})
	t.Logf("HotelIdList error: %v", err)
	t.Logf("HotelIdList response: %+v", hotelIdListResp)
	assert.Nil(t, err)
	assert.NotNil(t, hotelIdListResp, "HotelIdList response should not be nil")
	testHotelIds := hotelIdListResp.SupplierHotelIds
	// 使用测试酒店ID，因为 Tourmind API 需要具体的酒店代码
	// testHotelIds := []string{"1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010"} // 测试酒店ID

	// 尝试多个酒店，直到找到一个有可用房间的
	var hotelID string
	var ratePkgID string
	var selectedRoom *domain.Room
	var hotelRatesReq *domain.HotelRatesReq

	for i, testHotelId := range testHotelIds {
		if i >= 10 { // 最多尝试10个酒店
			t.Fatalf("Tried 10 hotels but none have available rooms")
		}

		hotelID = testHotelId
		t.Logf("Trying hotel ID: %s", hotelID)

		// 2. 获取酒店详情和价格
		t.Logf("=== 2. 获取酒店详情和价格 (尝试 %d) ===", i+1)
		hotelRatesReq = &domain.HotelRatesReq{
			SupplierHotelId: hotelID,
			CheckInOut: domain.CheckInOut{
				CheckIn:  types.NewDateIntFromTime(time.Now().AddDate(0, 0, 7)),
				CheckOut: types.NewDateIntFromTime(time.Now().AddDate(0, 0, 8)),
			},
			GuestRoomOption: domain.GuestRoomOption{
				RoomCount:     1,
				AdultCount:    2,
				ChildrenCount: 0,
			},
		}

		// 存储查价请求到 session
		reqJSON := utils.LogMarsh(hotelRatesReq)
		ctxPl, _ := domain.RetrieveBaseRequestContextPayload(ctx)
		if ctxPl.Session.Params == nil {
			ctxPl.Session.Params = make(map[string]string)
		}
		ctxPl.Session.Params[domain.APIName_HotelRates] = reqJSON
		t.Logf("Stored hotel rates request in session: %s", reqJSON)

		hotelRatesResp, err := client.HotelRates(ctx, hotelRatesReq)
		if err != nil {
			t.Logf("HotelRates failed for hotel %s: %v", hotelID, err)
			continue
		}
		if hotelRatesResp == nil {
			t.Logf("HotelRatesResp is nil for hotel %s", hotelID)
			continue
		}
		if len(hotelRatesResp.Rooms) == 0 {
			t.Logf("No rooms available for hotel %s", hotelID)
			continue
		}

		// 检查是否有可用的房型
		for _, room := range hotelRatesResp.Rooms {
			for _, rate := range room.Rates {
				if rate.Available {
					ratePkgID = rate.RatePkgId
					selectedRoom = &room
					break
				}
			}
			if ratePkgID != "" {
				break
			}
		}

		if ratePkgID != "" {
			t.Logf("Found available room in hotel %s", hotelID)
			break
		}

		t.Logf("No available rate packages found for hotel %s", hotelID)
	}

	if ratePkgID == "" {
		t.Fatalf("No available rate packages found in any hotel")
	}

	t.Logf("Selected rate package ID: %s", ratePkgID)
	t.Logf("Selected room: %s, price: %.2f", selectedRoom.RoomName.En, selectedRoom.Rates[0].Rate.FinalRate.Amount)

	// 3. 可定查询 - 使用真实的 RatePkgId
	t.Log("=== 3. 可定查询 ===")
	checkAvailReq := &domain.CheckAvailReq{
		RatePkgID: ratePkgID,
	}
	// 使用与 HotelRates 相同的 context，这样就能获取到存储的 metadata
	checkAvailResp, err := client.CheckAvail(ctx, checkAvailReq)
	if err != nil {
		t.Fatalf("CheckAvail failed: %v", err)
	}
	t.Logf("CheckAvail success: %+v", checkAvailResp)

	// 4. 提交订单 - 使用真实的 RatePkgId
	t.Log("=== 4. 提交订单 ===")
	if checkAvailResp != nil && checkAvailResp.Status == domain.CheckAvailStatusAvailable {
		platformOrderId := time.Now().Unix()

		bookReq := &domain.BookReq{
			RatePkgId:       ratePkgID,
			PlatformOrderId: platformOrderId,
			Booker: domain.Booker{
				FirstName: "Test",
				LastName:  "User",
				Email:     "<EMAIL>",
			},
			Guests: []domain.Guest{
				{
					FirstName: "Test",
					LastName:  "User",
					Age:       30,
				},
				{
					FirstName: "Jane",
					LastName:  "User",
					Age:       28,
				},
			},
			BookReqExtra: domain.BookReqExtra{
				RemarkToHotel: "Test booking",
			},
		}

		var bookResp *domain.BookResp
		var bookErr error
		maxRetries := 2
		for i := 0; i < maxRetries; i++ {
			bookResp, bookErr = client.Book(ctx, bookReq)
			if bookErr != nil && strings.Contains(bookErr.Error(), "Same client reference detected") {
				t.Logf("检测到重复预定错误，尝试查询并取消订单后重试: %v", bookErr)
				// 查询订单
				queryOrderReq := &domain.QueryOrdersReq{
					PlatformOrderId: bookReq.PlatformOrderId,
				}
				queryOrderResp, queryErr := client.QueryOrderByIDs(ctx, queryOrderReq)
				if queryErr != nil {
					t.Logf("查询订单失败: %v", queryErr)
				} else if len(queryOrderResp.Orders) > 0 {
					cancelOrderIds := func() (out []string) {
						for _, o := range queryOrderResp.Orders {
							if domain.CancellableOrderStatusSet.Contains(o.Basic.OrderStatus) {
								out = append(out, o.Basic.SupplierOrderId)
							}
						}
						return out
					}()
					for _, orderId := range cancelOrderIds {
						t.Logf("查询到订单，准备取消: %s", orderId)
						cancelReq := &domain.CancelReq{SupplierOrderId: orderId}
						cancelResp, cancelErr := client.Cancel(ctx, cancelReq)
						if cancelErr != nil {
							t.Logf("取消订单失败: %v", cancelErr)
						} else {
							t.Logf("取消订单成功: %+v", cancelResp)
						}
					}
				}
				time.Sleep(1 * time.Second)
				continue
			}
			if bookErr != nil {
				t.Fatalf("Book failed: %v", bookErr)
			}
			break
		}
		if bookErr != nil {
			t.Fatalf("Book failed after retries: %v", bookErr)
		}
		t.Logf("Book success: %+v", bookResp)

		// 提取真实的订单ID
		orderId := bookResp.SupplierOrderId
		if orderId == "" {
			t.Fatalf("No order ID returned from booking")
		}
		t.Logf("Generated order ID: %s", orderId)

		// 5. 查询订单 - 使用真实的订单ID
		t.Log("=== 5. 查询订单 ===")
		queryOrderReq := &domain.QueryOrdersReq{
			SupplierOrderIds: []string{orderId},
		}
		queryOrderResp, err := client.QueryOrderByIDs(ctx, queryOrderReq)
		if err != nil {
			t.Fatalf("QueryOrderByIDs failed: %v", err)
		}
		t.Logf("QueryOrder response: %+v", utils.LogMarsh(queryOrderResp))

		// 6. 取消订单 - 使用真实的订单ID
		t.Log("=== 6. 取消订单 ===")
		cancelReq := &domain.CancelReq{
			SupplierOrderId: orderId,
		}
		cancelResp, err := client.Cancel(ctx, cancelReq)
		if err != nil {
			t.Fatalf("Cancel failed: %v", err)
		}
		t.Logf("Cancel response: %+v", utils.LogMarsh(cancelResp))
	}

	t.Log("=== 全链路测试完成 ===")
}

func TestTourmindClient_Supplier(t *testing.T) {
	client := NewTourmindClient()
	assert.Equal(t, domain.Supplier_Tourmind, client.Supplier())
}
