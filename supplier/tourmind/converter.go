package tourmind

import (
	"strconv"
	"strings"
	"time"

	"hotel/common/i18n"
	"hotel/common/money"
	geoDomain "hotel/geography/domain"
	"hotel/supplier/domain"
	"hotel/supplier/tourmind/model"
)

// convertStringToInt 字符串转整数
func convertStringToInt(s string) int {
	if i, err := strconv.Atoi(s); err == nil {
		return i
	}
	return 0
}

// convertIntToString 整数转字符串
func convertIntToString(i int) string {
	return strconv.Itoa(i)
}

// convertStringToFloat64 字符串转浮点数
func convertStringToFloat64(s string) float64 {
	if f, err := strconv.ParseFloat(s, 64); err == nil {
		return f
	}
	return 0.0
}

// generateAgentRefID 生成代理商参考ID
func generateAgentRefID() string {
	return "TMS_" + time.Now().Format("20060102150405")
}

// extractHotelAndRateFromPkgId 从RatePkgID中提取酒店ID和价格代码
func extractHotelAndRateFromPkgId(ratePkgId string) (string, string) {
	// 假设格式为 "hotelId:rateCode"
	parts := strings.Split(ratePkgId, ":")
	if len(parts) >= 2 {
		return parts[0], parts[1]
	}
	return ratePkgId, ""
}

// convertHotelToHotelProfile 转换酒店信息到酒店档案
func convertHotelToHotelProfile(hotel model.HotelstaticHotelInfo, roomTypes []model.RoomStaticModelRoomTypeInfo) domain.HotelStaticProfile {
	// 处理酒店名称，优先使用中文名称
	hotelNameEn := hotel.Name
	hotelNameZh := hotel.NameCN
	if hotelNameZh == "" {
		hotelNameZh = hotel.Name
	}

	// 处理地址，优先使用中文地址
	addressEn := hotel.Address
	addressCN := hotel.AddressCN
	cityNameEn := hotel.CityName
	cityNameCN := hotel.CityNameCN

	// 构建完整地址
	fullAddressEn := addressEn
	if cityNameEn != "" {
		if addressEn != "" {
			fullAddressEn = addressEn + ", " + cityNameEn
		} else {
			fullAddressEn = cityNameEn
		}
	}

	fullAddressCN := addressCN
	if addressCN == "" {
		fullAddressCN = addressEn
	}
	if cityNameCN != "" {
		if fullAddressCN != "" {
			fullAddressCN = fullAddressCN + ", " + cityNameCN
		} else {
			fullAddressCN = cityNameCN
		}
	} else if cityNameEn != "" {
		if fullAddressCN != "" {
			fullAddressCN = fullAddressCN + ", " + cityNameEn
		} else {
			fullAddressCN = cityNameEn
		}
	}

	// 构建房间类型描述
	roomTypeDesc := ""
	if len(roomTypes) > 0 {
		roomTypeNames := make([]string, 0, len(roomTypes))
		for _, roomType := range roomTypes {
			if roomType.RoomTypeName != "" {
				roomTypeNames = append(roomTypeNames, roomType.RoomTypeName)
			}
		}
		if len(roomTypeNames) > 0 {
			roomTypeDesc = "房型: " + strings.Join(roomTypeNames, ", ")
		}
	}

	return domain.HotelStaticProfile{
		Name: i18n.I18N{
			En: hotelNameEn,
			Zh: hotelNameZh,
		},
		Desc: i18n.I18N{
			En: roomTypeDesc,
			Zh: roomTypeDesc,
		},
		Star: convertStringToFloat64(hotel.StarRating),
		Address: i18n.I18N{
			En: fullAddressEn,
			Zh: fullAddressCN,
		},
		Phone: hotel.Phone,
		LatlngCoordinator: geoDomain.LatlngCoordinator{
			Google: &geoDomain.Latlng{
				Lat: convertStringToFloat64(hotel.Latitude),
				Lng: convertStringToFloat64(hotel.Longitude),
			},
		},
	}
}

// convertHotelStaticToSupplierHotel 转换TourMind的HotelStatic到domain.SupplierHotel
func convertHotelStaticToSupplierHotel(hotelStatic model.HotelstaticHotelInfo) domain.SupplierHotel {
	// 处理酒店名称，优先使用中文名称，如果没有则使用英文名称
	hotelName := hotelStatic.Name
	hotelNameCN := hotelStatic.NameCN
	if hotelNameCN != "" {
		hotelName = hotelNameCN
	}

	// 处理地址，优先使用中文地址
	address := hotelStatic.Address
	addressCN := hotelStatic.AddressCN
	if addressCN != "" {
		address = addressCN
	}

	// 处理城市名称
	cityName := hotelStatic.CityName
	cityNameCN := hotelStatic.CityNameCN
	if cityNameCN != "" {
		cityName = cityNameCN
	}

	return domain.SupplierHotel{
		SupplierHotelId: hotelStatic.HotelId,
		Supplier:        domain.Supplier_Tourmind,
		Name: i18n.I18N{
			En: hotelStatic.Name,
			Zh: hotelName,
		},
		HotelStaticProfile: domain.HotelStaticProfile{
			Name: i18n.I18N{
				En: hotelStatic.Name,
				Zh: hotelName,
			},
			Star: convertStringToFloat64(hotelStatic.StarRating),
			Address: i18n.I18N{
				En: hotelStatic.Address + ", " + hotelStatic.CityName,
				Zh: address + ", " + cityName,
			},
			Phone: hotelStatic.Phone,
			LatlngCoordinator: geoDomain.LatlngCoordinator{
				Google: &geoDomain.Latlng{
					Lat: convertStringToFloat64(hotelStatic.Latitude),
					Lng: convertStringToFloat64(hotelStatic.Longitude),
				},
			},
		},
	}
}

// convertHotelStaticListToSupplierHotels 批量转换HotelStatic列表到SupplierHotel列表
func convertHotelStaticListToSupplierHotels(hotelStatics []model.HotelstaticHotelInfo) []domain.SupplierHotel {
	result := make([]domain.SupplierHotel, 0, len(hotelStatics))
	for _, hotelStatic := range hotelStatics {
		result = append(result, convertHotelStaticToSupplierHotel(hotelStatic))
	}
	return result
}

// convertGuestRoomOptions 转换客房选项
func convertGuestRoomOptions(options []domain.GuestRoomOption) []model.HotelDetailPaxRoomRq {
	var paxRooms []model.HotelDetailPaxRoomRq
	for _, option := range options {
		// 根据 TourMind API 文档，PaxRooms 数组中每个元素代表一种房型的配置
		// 而不是每个房间的配置，RoomCount 字段指定该配置的房间数量
		roomCount := int(option.RoomCount)
		if roomCount <= 0 {
			roomCount = 1 // 默认至少1个房间
		}

		// 计算每个房间的人数（平均分配）
		adultsPerRoom := int(option.AdultCount) / roomCount
		childrenPerRoom := int(option.ChildrenCount) / roomCount

		// 从Guests中提取儿童年龄
		var childrenAges []int
		for _, guest := range option.Guests {
			if guest.Age < 18 {
				childrenAges = append(childrenAges, int(guest.Age))
			}
		}

		// 转换儿童年龄为 int32 类型
		var childrenAgesInt32 []int32
		for _, age := range childrenAges {
			childrenAgesInt32 = append(childrenAgesInt32, int32(age))
		}

		// 创建一个 PaxRoomRQ 对象，包含 RoomCount 字段
		paxRoom := model.HotelDetailPaxRoomRq{
			Adults:       int32(adultsPerRoom),
			Children:     int32(childrenPerRoom),
			ChildrenAges: childrenAgesInt32,
			RoomCount:    int32(roomCount),
		}

		paxRooms = append(paxRooms, paxRoom)
	}
	return paxRooms
}

// convertHotelDetailResponse 转换酒店详情响应
func convertHotelDetailResponse(hotel model.HotelDetailHotel) []domain.Room {
	var rooms []domain.Room
	for _, roomType := range hotel.RoomTypes {
		var rates []domain.RoomRatePkg
		for _, rate := range roomType.RateInfos {
			ratePkg := domain.RoomRatePkg{
				RatePkgId: hotel.HotelCode + ":" + rate.RateCode, //todo:
				Available: true,                                  // 假设可用
				RatePlan: domain.RatePlan{
					RatePlanId: rate.RateCode,
					Meal:       convertMealType(rate.MealInfo.MealType),
					Inventory:  int64(rate.Allotment),
				},
				Rate: domain.Rate{
					FinalRate: money.NewMoney(rate.CurrencyCode, float64(rate.TotalPrice)),
				},
			}
			rates = append(rates, ratePkg)
		}

		domainRoom := domain.Room{
			HotelRoomStaticProfile: domain.HotelRoomStaticProfile{
				RoomName: i18n.I18N{
					En: roomType.Name,
					Zh: roomType.NameCN,
				},
				Occupancy: 2,          // 默认值，因为新模型中没有这个字段
				Images:    []string{}, // 默认空数组，因为新模型中没有这个字段
			},
			Rates: rates,
		}
		rooms = append(rooms, domainRoom)
	}
	return rooms
}

// convertCheckAvailResponse 转换验价响应
func convertCheckAvailResponse(hotel model.RoomAvailHotel) *domain.RoomRatePkg {
	if len(hotel.RoomTypes) == 0 || len(hotel.RoomTypes[0].RateInfos) == 0 {
		return nil
	}

	roomType := hotel.RoomTypes[0]
	rate := roomType.RateInfos[0]

	return &domain.RoomRatePkg{
		RatePkgId: hotel.HotelCode + ":" + rate.RateCode,
		Available: true, // 假设可用
		RatePlan: domain.RatePlan{
			RatePlanId: rate.RateCode,
			Meal:       convertMealType(rate.MealInfo.MealType),
			Inventory:  int64(rate.Allotment),
		},
		Rate: domain.Rate{
			FinalRate: money.NewMoney(rate.CurrencyCode, float64(rate.TotalPrice)),
		},
	}
}

// convertContactInfo 转换联系信息
func convertContactInfo(booker domain.Booker) model.CreateOrderContactInfo {
	return model.CreateOrderContactInfo{
		Email:     booker.Email,
		FirstName: booker.FirstName,
		LastName:  booker.LastName,
		PhoneNo:   booker.Phone.Number,
	}
}

// convertBookingRooms 转换预订房间
func convertBookingRooms(guests []domain.Guest) []model.CreateOrderPaxRoom {
	// 按房间分组客人
	roomMap := make(map[int64][]domain.Guest)
	for _, guest := range guests {
		roomMap[guest.RoomIndex] = append(roomMap[guest.RoomIndex], guest)
	}

	var paxRooms []model.CreateOrderPaxRoom
	for _, roomGuests := range roomMap {
		adults := 0
		children := 0
		var ages []int32
		var modelGuests []model.CreateOrderPaxName

		for _, guest := range roomGuests {
			modelGuest := model.CreateOrderPaxName{
				FirstName: guest.FirstName,
				LastName:  guest.LastName,
			}

			if guest.Age >= 18 {
				adults++
			} else {
				children++
				ages = append(ages, int32(guest.Age))
			}

			modelGuests = append(modelGuests, modelGuest)
		}

		paxRoom := model.CreateOrderPaxRoom{
			Adults:       int32(adults),
			Children:     int32(children),
			ChildrenAges: ages,
			PaxNames:     modelGuests,
			RoomCount:    1, // 默认每个房间1间
		}
		paxRooms = append(paxRooms, paxRoom)
	}

	return paxRooms
}

// convertQueryOrderResponse 转换查询订单响应
func convertQueryOrderResponse(orderInfo *model.SearchOrderOrderInfo) *domain.HotelOrder {
	return &domain.HotelOrder{
		Basic: &domain.OrderBasic{
			Supplier:        domain.Supplier_Tourmind,
			SupplierOrderId: orderInfo.ReservationID,
			OrderStatus:     convertOrderStatus(orderInfo.OrderStatus),
			SettlementPrice: money.NewMoney(orderInfo.CurrencyCode, float64(orderInfo.TotalPrice)),
		},
	}
}

// convertOrderStatus 转换订单状态
func convertOrderStatus(status string) domain.OrderStatus {
	switch strings.ToUpper(status) {
	case "CONFIRMED":
		return domain.OrderStatus_Confirmed
	case "PENDING":
		return domain.OrderStatus_Confirming
	case "CANCELLED":
		return domain.OrderStatus_Cancelled
	case "FAILED":
		return domain.OrderStatus_UnSubmitted
	default:
		return domain.OrderStatus_Unknown
	}
}

// convertCancelStatus 转换取消状态
func convertCancelStatus(status string) domain.OrderStatus {
	switch strings.ToUpper(status) {
	case "CANCELLED":
		return domain.OrderStatus_Cancelled
	default:
		return domain.OrderStatus_Unknown
	}
}

// convertMealType 转换餐食类型
func convertMealType(mealPlan string) domain.Meal {
	switch strings.ToUpper(mealPlan) {
	case "BB", "BREAKFAST":
		return domain.Meal{
			Type:        domain.MealTypeBreakfast,
			Description: mealPlan,
		}
	case "HB", "HALF_BOARD":
		return domain.Meal{
			Type:        domain.MealTypeBreakfastDinner,
			Description: mealPlan,
		}
	case "FB", "FULL_BOARD":
		return domain.Meal{
			Type:        domain.MealTypeAll,
			Description: mealPlan,
		}
	case "AI", "ALL_INCLUSIVE":
		return domain.Meal{
			Type:        domain.MealTypeAll,
			Description: mealPlan,
		}
	default:
		return domain.Meal{
			Type:        domain.MealTypeNone,
			Description: mealPlan,
		}
	}
}
