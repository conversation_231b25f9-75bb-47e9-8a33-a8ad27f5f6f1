redis:
  host: "127.0.0.1:6379"
  type: "node"  # 可选值: "node" 或 "cluster"
  user: ""      # 如果 Redis 需要用户名，填写，否则可以省略
  pass: ""      # 如果 Redis 需要密码，填写，否则可以省略
  tls: false    # 是否启用 TLS 连接
  nonBlock: true  # 是否启用非阻塞模式，默认 true
  pingTimeout: "1s"  # Ping 超时时间，单位支持 s（秒）、ms（毫秒）、m（分钟）等
api:
  HotelIdList:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/HotelStaticList
    httpMethod: POST
  HotelList:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/HotelStaticList
    httpMethod: POST
  HotelStaticDetail:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/RoomStaticList
    httpMethod: POST
  HotelRates:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/HotelDetail
    httpMethod: POST
  CheckAvail:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/CheckRoomRate
    httpMethod: POST
  Book:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/CreateOrder
    httpMethod: POST
  QueryOrder:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/SearchOrder
    httpMethod: POST
  Cancel:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/CancelOrder
    httpMethod: POST
  RegionList:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/RegionList
    httpMethod: POST
  RoomMatch:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/RoomMatch
    httpMethod: POST
  RoomMap:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/RoomMap
    httpMethod: POST
  RatePrediction:
    baseURL: "http://developers.tourmind.cn"
    path: /v2/RatePrediction
    httpMethod: POST