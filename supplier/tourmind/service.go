package tourmind

import (
	"context"
	"errors"
	"time"

	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"hotel/common/pagehelper"
	"hotel/supplier/domain"
	"hotel/supplier/middleware"
	"hotel/supplier/tourmind/model"

	pkgerr "github.com/pkg/errors"
)

type TourmindClient struct {
	*middleware.SupplierUtilWrapper[model.Properties]
}

func (s *TourmindClient) Supplier() domain.Supplier {
	return domain.Supplier_Tourmind
}

func (s *TourmindClient) HotelStaticDetail(ctx context.Context, req *domain.HotelStaticDetailReq) (*domain.HotelStaticDetailResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	hotelLists, err := s.getHotelListByIds(ctx, []string{req.SupplierHotelId})
	if err != nil {
		return nil, pkgerr.Wrap(err, "getHotelListByIds")
	}

	h := hotelLists[0]
	// 请求静态房型列表
	tourmindReq := &model.RoomStaticModelRoomStaticRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
			TransactionID: generateAgentRefID(),
		},
		HotelCode: int32(cast.ToInt(req.SupplierHotelId)),
	}

	tourmindResp := new(model.RoomStaticModelRoomStaticResponse)
	err = s.execute(ctx, domain.APIName_HotelStaticDetail, tourmindReq, tourmindResp)
	if err != nil {
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}
	convertedResult := convertHotelToHotelProfile(h, tourmindResp.RoomTypes)

	return &domain.HotelStaticDetailResp{
		HotelStaticProfile: convertedResult,
	}, nil
}

func (s *TourmindClient) HotelIdList(ctx context.Context, req *domain.HotelIdListReq) (*domain.HotelIdListResp, error) {
	logx.WithContext(ctx).Info("HotelIdList method called")
	hotelLists, err := s.getHotelListByCountry(ctx, req)
	if err != nil {
		logx.WithContext(ctx).Errorf("getHotelListByCountry failed: %v", err)
		return nil, err
	}

	if hotelLists == nil {
		logx.WithContext(ctx).Error("hotelLists is nil")
		return &domain.HotelIdListResp{SupplierHotelIds: []string{}}, nil
	}

	logx.WithContext(ctx).Infof("hotelLists.Hotels length: %d", len(hotelLists.Hotels))
	hotelIDs := make([]string, 0, len(hotelLists.Hotels))
	for _, hotel := range hotelLists.Hotels {
		logx.WithContext(ctx).Infof("Processing hotel: %s", hotel.HotelId)
		hotelIDs = append(hotelIDs, hotel.HotelId)
	}

	logx.WithContext(ctx).Infof("Found %d hotels", len(hotelIDs))
	return &domain.HotelIdListResp{SupplierHotelIds: hotelIDs}, nil
}

func (s *TourmindClient) HotelList(ctx context.Context, req *domain.HotelListReq) (*domain.HotelListResp, error) {
	var supplierHotelIds []string
	if len(req.SupplierHotelIds) == 0 {
		hotelLists, err := s.getHotelListByCountry(ctx, &domain.HotelIdListReq{
			CountryCode: req.CountryOption.CountryCode,
		})
		if err != nil {
			return nil, err
		}

		// 转换HotelStatic列表到SupplierHotel列表
		supplierHotels := convertHotelStaticListToSupplierHotels(hotelLists.Hotels)
		return &domain.HotelListResp{
			Hotels:   supplierHotels,
			PageResp: pagehelper.PageResp{HasMore: int64(req.PageNum)*int64(req.PageSize) < int64(hotelLists.Pagination.TotalCount), Total: int64(hotelLists.Pagination.TotalCount)},
		}, nil
	}
	supplierHotelIds = req.SupplierHotelIds
	hotelDetails, err := s.batchHotelStaticDetail(ctx, supplierHotelIds)
	if err != nil {
		return nil, err
	}
	return &domain.HotelListResp{
		Hotels: hotelDetails.Hotels,
	}, nil
}

func (s *TourmindClient) HotelRates(ctx context.Context, req *domain.HotelRatesReq) (*domain.HotelRatesResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	tourmindReq := &model.HotelDetailHotelDetailRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
			TransactionID: generateAgentRefID(),
		},
		CheckIn:      req.CheckIn.Format("2006-01-02"),
		CheckOut:     req.CheckOut.Format("2006-01-02"),
		HotelCodes:   []int32{int32(convertStringToInt(req.SupplierHotelId))},
		IsDailyPrice: true,
		Nationality:  "CN",
		PaxRooms:     convertGuestRoomOptions([]domain.GuestRoomOption{req.GuestRoomOption}),
	}

	tourmindResp := new(model.HotelDetailHotelDetailResponse)
	err := s.execute(ctx, domain.APIName_HotelRates, tourmindReq, tourmindResp)
	if err != nil {
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}

	if len(tourmindResp.Hotels) == 0 {
		return &domain.HotelRatesResp{Rooms: []domain.Room{}}, nil
	}

	convertedResult := convertHotelDetailResponse(tourmindResp.Hotels[0])
	return &domain.HotelRatesResp{
		Supplier: domain.Supplier_Tourmind,
		Rooms:    convertedResult,
	}, nil
}

func (s *TourmindClient) CheckAvail(ctx context.Context, req *domain.CheckAvailReq) (*domain.CheckAvailResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	// 从RatePkgID中提取信息
	hotelId, rateCode := extractHotelAndRateFromPkgId(req.RatePkgId)

	tourmindReq := &model.RoomAvailRoomAvailRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
			TransactionID: generateAgentRefID(),
		},
		CheckIn:     time.Now().Format("2006-01-02"),
		CheckOut:    time.Now().AddDate(0, 0, 1).Format("2006-01-02"),
		HotelCodes:  []int32{int32(convertStringToInt(hotelId))},
		Nationality: "CN",
		PaxRooms: []model.RoomAvailPaxRoomRq{
			{
				Adults:   2,
				Children: 0,
			},
		},
		RateCode: rateCode,
	}

	tourmindResp := new(model.RoomAvailRoomAvailResponse)
	err := s.execute(ctx, domain.APIName_CheckAvail, tourmindReq, tourmindResp)
	if err != nil {
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}

	if len(tourmindResp.Hotels) == 0 {
		return nil, errors.New("room not available")
	}

	convertedResult := convertCheckAvailResponse(tourmindResp.Hotels[0])
	return &domain.CheckAvailResp{
		Status:      domain.CheckAvailStatusAvailable,
		RoomRatePkg: convertedResult,
	}, nil
}

func (s *TourmindClient) Book(ctx context.Context, req *domain.BookReq) (*domain.BookResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	hotelId, rateCode := extractHotelAndRateFromPkgId(req.RatePkgId)

	tourmindReq := &model.CreateOrderCreateOrderRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
			TransactionID: generateAgentRefID(),
		},
		AgentRefID:     generateAgentRefID(),
		CheckIn:        time.Now().Format("2006-01-02"),
		CheckOut:       time.Now().AddDate(0, 0, 1).Format("2006-01-02"),
		ContactInfo:    convertContactInfo(req.Booker),
		CurrencyCode:   "USD",
		HotelCode:      int32(convertStringToInt(hotelId)),
		PaxRooms:       convertBookingRooms(req.Guests),
		RateCode:       rateCode,
		SpecialRequest: req.RemarkToHotel,
		TotalPrice:     0, // 需要从验价结果获取
	}

	tourmindResp := new(model.CreateOrderCreateOrderResponse)
	err := s.execute(ctx, domain.APIName_Book, tourmindReq, tourmindResp)
	if err != nil {
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}

	if tourmindResp.OrderInfo.ReservationID == "" {
		return nil, errors.New("booking failed")
	}

	return &domain.BookResp{
		SupplierOrderId: tourmindResp.ReservationID,
		OrderStatus:     convertOrderStatus(tourmindResp.OrderInfo.OrderStatus),
	}, nil
}

func (s *TourmindClient) QueryOrderByIDs(ctx context.Context, req *domain.QueryOrdersReq) (*domain.QueryOrdersResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	var orders []*domain.HotelOrder
	for _, orderId := range req.SupplierOrderIds {
		tourmindReq := &model.SearchOrderQueryOrderRequest{
			RequestHeader: model.CommonRequestHeader{
				AgentCode:     credentials.Header.AgentCode,
				UserName:      credentials.Header.UserName,
				Password:      credentials.Header.Password,
				RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
				TransactionID: generateAgentRefID(),
			},
			AgentRefID: orderId,
		}

		tourmindResp := new(model.SearchOrderQueryOrderResponse)
		err := s.execute(ctx, domain.APIName_QueryOrder, tourmindReq, tourmindResp)
		if err != nil {
			logx.WithContext(ctx).Errorf("Failed to query order %s: %v", orderId, err)
			continue
		}

		if tourmindResp.Error.ErrorCode != "" {
			logx.WithContext(ctx).Errorf("Query order %s error: %s", orderId, tourmindResp.Error.ErrorMessage)
			continue
		}

		if tourmindResp.OrderInfo.ReservationID != "" {
			convertedOrder := convertQueryOrderResponse(&tourmindResp.OrderInfo)
			orders = append(orders, convertedOrder)
		}
	}

	return &domain.QueryOrdersResp{
		Orders: orders,
	}, nil
}

func (s *TourmindClient) Cancel(ctx context.Context, req *domain.CancelReq) (*domain.CancelResp, error) {
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	tourmindReq := &model.CancelOrderCancelOrderRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   model.FormatRequestTime(time.Now()),
			TransactionID: model.GenerateTransactionID(),
		},
		AgentRefID: req.SupplierOrderId,
	}

	tourmindResp := new(model.CancelOrderCancelOrderResponse)
	err := s.execute(ctx, domain.APIName_Cancel, tourmindReq, tourmindResp)
	if err != nil {
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}

	if tourmindResp.CancelResult.OrderStatus == "" {
		return nil, errors.New("cancellation failed")
	}

	return &domain.CancelResp{
		Status: convertCancelStatus(tourmindResp.CancelResult.OrderStatus),
	}, nil
}

// Helper methods
func (s *TourmindClient) execute(ctx context.Context, apiName string, req, resp interface{}) error {
	return s.SupplierUtilWrapper.Execute(ctx, apiName, req, resp)
}

func (s *TourmindClient) getHotelListByIds(ctx context.Context, ids []string) ([]model.HotelstaticHotelInfo, error) {
	logx.WithContext(ctx).Info("getHotelListByCountry method called")
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)
	var hotelIds []int32
	for _, id := range ids {
		hotelIds = append(hotelIds, int32(cast.ToInt(id)))
	}
	tourmindReq := &model.HotelstaticHotelStaticListRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
			TransactionID: generateAgentRefID(),
		},
		HotelIds: hotelIds,
	}

	tourmindResp := new(model.HotelstaticHotelStaticListResponse)
	err := s.execute(ctx, domain.APIName_HotelList, tourmindReq, tourmindResp)
	if err != nil {
		logx.WithContext(ctx).Errorf("execute failed: %v", err)
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}
	return tourmindResp.HotelStaticListResult.Hotels, nil
}

func (s *TourmindClient) getHotelListByCountry(ctx context.Context, req *domain.HotelIdListReq) (*model.HotelstaticHotelStaticListResult, error) {
	logx.WithContext(ctx).Info("getHotelListByCountry method called")
	prop, _ := domain.RetrieveBaseRequestContextPayload(ctx)
	credentials := prop.Properties.(model.Properties)

	tourmindReq := &model.HotelstaticHotelStaticListRequest{
		RequestHeader: model.CommonRequestHeader{
			AgentCode:     credentials.Header.AgentCode,
			UserName:      credentials.Header.UserName,
			Password:      credentials.Header.Password,
			RequestTime:   time.Now().Format("2006-01-02 15:04:05"),
			TransactionID: generateAgentRefID(),
		},
		CountryCode: req.CountryCode,
		Pagination: model.HotelstaticPagination{
			PageIndex: cast.ToString(req.PageNum),
			PageSize:  int32(req.PageSize),
		},
	}

	tourmindResp := new(model.HotelstaticHotelStaticListResponse)
	err := s.execute(ctx, domain.APIName_HotelList, tourmindReq, tourmindResp)
	if err != nil {
		logx.WithContext(ctx).Errorf("execute failed: %v", err)
		return nil, err
	}

	if tourmindResp.Error.ErrorCode != "" {
		return nil, errors.New(tourmindResp.Error.ErrorMessage)
	}
	return &tourmindResp.HotelStaticListResult, nil
}

func (s *TourmindClient) batchHotelStaticDetail(ctx context.Context, supplierHotelIds []string) (*domain.BatchHotelStaticDetailResp, error) {
	var hotels []domain.SupplierHotel
	for _, hotelId := range supplierHotelIds {
		detail, err := s.HotelStaticDetail(ctx, &domain.HotelStaticDetailReq{
			SupplierHotelId: hotelId,
		})
		if err != nil {
			logx.WithContext(ctx).Errorf("Failed to get hotel detail for %s: %v", hotelId, err)
			continue
		}
		hotels = append(hotels, domain.SupplierHotel{
			SupplierHotelId:    hotelId,
			Supplier:           domain.Supplier_Tourmind,
			HotelStaticProfile: detail.HotelStaticProfile,
		})
	}

	return &domain.BatchHotelStaticDetailResp{
		Hotels: hotels,
	}, nil
}
