package protocol

import supplierDomain "hotel/supplier/domain"

// GetSuppliersReq request for getting supplier list
type // GetSuppliersReq represents the request structure for GetSuppliers operation with 1 fields
GetSuppliersReq struct {
		// OnlyActive indicates whether onlyactive is enabled
OnlyActive bool `json:"onlyActive,omitempty"` // Only return active suppliers
}

// GetSuppliersResp response for getting supplier list
type // GetSuppliersResp represents the response structure for GetSuppliers operation with 1 fields
GetSuppliersResp struct {
		// Suppliers contains the suppliers data
Suppliers []supplierDomain.SupplierInfo `json:"suppliers"`
}