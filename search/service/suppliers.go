package service

import (
	"context"
	
	"hotel/search/protocol"
	supplierDomain "hotel/supplier/domain"
)

// GetSuppliers
// @tags: openapi,Resource,booking.hotelbyte.com/resource
// @path: /suppliers
func (s *SearchService) GetSuppliers(ctx context.Context, req *protocol.GetSuppliersReq) (*protocol.GetSuppliersResp, error) {
	// Get all suppliers from domain
	allSuppliers := supplierDomain.GetAllSuppliers()
	
	// Filter suppliers based on request parameters
	var filteredSuppliers []supplierDomain.SupplierInfo
	
	if req.OnlyActive {
		// Return only active suppliers
		for _, supplier := range allSuppliers {
			if supplier.IsActive {
				filteredSuppliers = append(filteredSuppliers, supplier)
			}
		}
	} else {
		// Return all suppliers
		filteredSuppliers = allSuppliers
	}
	
	return &protocol.GetSuppliersResp{
		Suppliers: filteredSuppliers,
	}, nil
}